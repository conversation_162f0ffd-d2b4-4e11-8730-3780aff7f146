#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/2025V2/[d123]钥云台/d123uni/node_modules/.store/@mastergo+magic-mcp@0.0.6/node_modules/@mastergo"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/2025V2/[d123]钥云台/d123uni/node_modules/.store/@mastergo+magic-mcp@0.0.6/node_modules/@mastergo"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/@mastergo+magic-mcp@0.0.6/node_modules/@mastergo/magic-mcp/bin/cli.js" "$@"
else
  exec node  "$basedir/../.store/@mastergo+magic-mcp@0.0.6/node_modules/@mastergo/magic-mcp/bin/cli.js" "$@"
fi
