<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" @scroll="onScroll" bgColor="#fff"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="详情" :leftIconColor="!is_bgChange ? '#fff' : ''"
					:titleStyle="{ 'color': !is_bgChange ? '#fff' : '' }" mpWeiXinShow :autoBack="true" :fixed="true"
					:bgColor="!is_bgChange ? 'transparent' : '#fff'" class="custom_navbar">
					<view slot="right">
						<u-icon :name="rightIcon" size="36rpx"></u-icon>
					</view>
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<!-- 顶部图片区域 -->
				<view class="top_image_container">
					<image class="top_image"
						src="https://img2.baidu.com/it/u=3018303209,1765139986&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=722"
						mode="aspectFill"></image>
				</view>

				<!-- 主要内容区域 -->
				<view class="main_content">
					<!-- 小区名称 -->
					<view class="community_name">龙湖兰园天序</view>

					<!-- 标签区域 -->
					<view class="tags_container">
						<view class="tag tag_highlight">高性价比</view>
						<view class="tag">刚需优选</view>
						<view class="tag">公交直达</view>
						<view class="tag">明星户型</view>
					</view>

					<!-- 位置信息卡片 -->
					<view class="location_card">
						<view class="location_info">
							<view class="location_text">仓山区-金山公园 龙湖兰园天序</view>
							<view class="distance_text">距离您-15.6km</view>
						</view>
						<!-- <view class="map_button">
							<u-icon name="map" color="#708EBB" size="42rpx"></u-icon>
							<text class="map_text">地图</text>
						</view> -->
					</view>

					<!-- 价格信息 -->
					<view class="price_section">
						<view class="price_row">
							<view class="price_item">
								<text class="price_value">22098元/㎡</text>
								<text class="price_label">参考均价</text>
							</view>
							<view class="price_item">
								<text class="price_value">441-683万/套</text>
								<text class="price_label">参考总价</text>
							</view>
							<view class="price_item">
								<text class="price_value">3/4室</text>
								<text class="price_label">89-138㎡</text>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 基础信息 -->
					<view class="section">
						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">小区名称</text>
									<text class="info_value">龙湖兰园天序</text>
								</view>
								<view class="info_item">
									<text class="info_label">交易权属</text>
									<text class="info_value">商品房/安置...</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">总楼栋数</text>
									<text class="info_value">20栋</text>
								</view>
								<view class="info_item">
									<text class="info_label">建筑类型</text>
									<text class="info_value">塔板结合</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">建成年代</text>
									<text class="info_value">2022年</text>
								</view>
								<view class="info_item">
									<text class="info_label">产权年限</text>
									<text class="info_value">70年</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">容 积 率</text>
									<text class="info_value">2.2</text>
								</view>
								<view class="info_item">
									<text class="info_label">绿 化 率</text>
									<text class="info_value">30%</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 开发商信息 -->
					<view class="section">
						<view class="section_title">基础信息</view>

						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">开发企业</text>
									<text class="info_value">龙湖地产有限公司</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">交易权属</text>
									<text class="info_value">商品房/动迁安置房</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">物业公司</text>
									<text class="info_value">福州龙湖物业服务有限公司</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">物业费</text>
									<text class="info_value">4.85元/月/㎡</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 分割线 -->
					<view class="divider"></view>

					<!-- 小区概况 -->
					<view class="section">
						<view class="section_title">小区概况</view>
						<view class="info_grid">
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">占地面积</text>
									<text class="info_value">101,300㎡</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">建筑面积</text>
									<text class="info_value">202,300㎡</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">人车分流</text>
									<text class="info_value">否</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">车位配比</text>
									<text class="info_value">1:1.6</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">停车费</text>
									<text class="info_value">580/月</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">总楼栋数</text>
									<text class="info_value">20栋</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">总户数</text>
									<text class="info_value">806户</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">供暖类型</text>
									<text class="info_value">--</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">供水类型</text>
									<text class="info_value">民用水</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">供电类型</text>
									<text class="info_value">民用电</text>
								</view>
							</view>
							<view class="info_row">
								<view class="info_item">
									<text class="info_label">供气方式</text>
									<text class="info_value">天然气</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			is_bgChange: false,
			screenHeight: 0,
		};
	},
	computed: {
		rightIcon() {
			let icon = '';
			if (this.is_bgChange) {
				icon = 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/share_black.png';
			} else {
				icon = 'https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/share_white.png';
			}
			return icon
		},

	},
	onLoad() {
		this.screenHeight = uni.getSystemInfoSync().windowHeight;
	},

	methods: {
		onScroll(e) {
			console.log(e.detail.scrollTop, this.screenHeight / 3);
			if (e.detail.scrollTop > this.screenHeight / 3) {
				this.is_bgChange = true;
			} else {
				this.is_bgChange = false;
			}
		},
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},

	},
}
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loading_list {
		width: 100%;

		// 顶部图片区域
		.top_image_container {
			position: relative;
			width: 100%;
			height: 750rpx;

			.top_image {
				width: 100%;
				height: 750rpx;
			}
		}

		// 主要内容区域
		.main_content {
			position: relative;
			top: -35rpx;
			background: #ffffff;
			border-radius: 30rpx 30rpx 0 0;

			>view {
				padding: 0 25rpx;
			}

			// 小区名称
			.community_name {
				font-size: 36rpx;
				font-weight: bold;
				color: #000000;
				line-height: 36rpx;
				padding-top: 30rpx;
			}

			// 标签区域
			.tags_container {
				@include flex-center(row, flex-start, center);
				gap: 20rpx;
				padding: 0 25rpx;
				margin-top: 20rpx;

				.tag {
					padding: 17rpx 20rpx;
					border-radius: 8rpx;
					background: #f7f7f7;
					font-size: 24rpx;
					color: #8a8a8a;

					&.tag_highlight {
						background: #f9f3ec;
						color: #b28c62;
					}
				}
			}

			// 位置信息卡片
			.location_card {
				margin: 20rpx 25rpx 0 25rpx;
				padding: 20rpx;
				background: #fafafa;
				border-radius: 20rpx;
				@include flex-center(row, space-between, center);
				box-sizing: border-box;
				background: url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/dw_bg.png') no-repeat center center;
				background-size: cover;
				background-size: 100% 100%;

				.location_info {
					flex: 0.7;
					@include flex-center(column, flex-start, flex-start);
					gap: 26rpx;

					.location_text {
						width: 100%;
						font-size: 26rpx;
						font-weight: bold;
						color: #000000;
						line-height: 26rpx;

						>view {
							@include text-overflow(100%, 1);
						}
					}

					.distance_text {
						font-size: 22rpx;
						color: #8a8a8a;
						line-height: 22rpx;
					}
				}

				.map_button {
					@include flex-center(column, center, center);
					gap: 8rpx;

					.map_text {
						font-size: 22rpx;
						color: #708ebb;
						line-height: 22rpx;
					}
				}
			}

			// 价格信息
			.price_section {
				padding: 30rpx 25rpx;

				.price_row {
					@include flex-center(row, space-between, flex-end);

					.price_item {
						@include flex-center(column, center, center);
						gap: 15rpx;

						.price_value {
							font-size: 30rpx;
							font-weight: bold;
							color: #000000;
							line-height: 30rpx;
						}

						.price_label {
							font-size: 24rpx;
							color: #969696;
							line-height: 24rpx;
						}
					}
				}
			}

			// 分割线
			.divider {
				width: 100%;
				height: 15rpx;
				background: #f8f8f8;
				padding: 0;
			}

			// 章节标题和信息网格
			.section {
				padding: 30rpx 25rpx;

				.section_title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333333;
					line-height: 32rpx;
					margin-bottom: 25rpx;
				}

				.info_grid {
					display: flex;
					flex-direction: column;
					gap: 25rpx;

					.info_row {
						@include flex-center(row, space-between, flex-start);

						&:last-child {
							margin-bottom: 0;
						}

						.info_item {
							flex: 1;
							@include flex-center(row, flex-start, flex-start);
							gap: 30rpx;

							.info_label {
								width: 115rpx;
								font-size: 28rpx;
								color: #969696;
								line-height: 28rpx;
								display: flex;
								justify-content: space-between;
							}

							.info_value {
								font-size: 28rpx;
								color: #333333;
								line-height: 28rpx;
							}
						}
					}
				}
			}
		}
	}
}
</style>