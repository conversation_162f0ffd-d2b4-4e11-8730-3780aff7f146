#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/2025V2/[d123]钥云台/d123uni/node_modules/.store/which@2.0.2/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/2025V2/[d123]钥云台/d123uni/node_modules/.store/which@2.0.2/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../which@2.0.2/node_modules/which/bin/node-which" "$@"
else
  exec node  "$basedir/../../../../../which@2.0.2/node_modules/which/bin/node-which" "$@"
fi
