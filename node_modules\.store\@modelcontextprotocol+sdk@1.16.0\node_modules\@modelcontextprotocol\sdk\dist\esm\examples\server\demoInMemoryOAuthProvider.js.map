{"version": 3, "file": "demoInMemoryOAuthProvider.js", "sourceRoot": "", "sources": ["../../../../src/examples/server/demoInMemoryOAuthProvider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAIzC,OAAO,OAA8B,MAAM,SAAS,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjF,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AAGtE,MAAM,OAAO,wBAAwB;IAArC;QACU,YAAO,GAAG,IAAI,GAAG,EAAsC,CAAC;IAUlE,CAAC;IARC,KAAK,CAAC,SAAS,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,cAA0C;QAC7D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAC3D,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,wBAAwB;IAOnC,YAAoB,gBAA8C;QAA9C,qBAAgB,GAAhB,gBAAgB,CAA8B;QANlE,iBAAY,GAAG,IAAI,wBAAwB,EAAE,CAAC;QACtC,UAAK,GAAG,IAAI,GAAG,EAEiB,CAAC;QACjC,WAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;IAEwB,CAAC;IAEtE,KAAK,CAAC,SAAS,CACb,MAAkC,EAClC,MAA2B,EAC3B,GAAa;QAEb,MAAM,IAAI,GAAG,UAAU,EAAE,CAAC;QAE1B,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACvC,IAAI;SACL,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;YACnB,MAAM;YACN,MAAM;SACP,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC3C,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,6BAA6B,CACjC,MAAkC,EAClC,iBAAyB;QAGzB,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC7B,MAAkC,EAClC,iBAAyB;IACzB,wDAAwD;IACxD,oCAAoC;IACpC,aAAsB;QAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,qDAAqD,QAAQ,CAAC,MAAM,CAAC,SAAS,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QAC3H,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,UAAU,EAAE,CAAC;QAE3B,MAAM,SAAS,GAAG;YAChB,KAAK;YACL,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,EAAE,SAAS;YAC1C,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,QAAQ;YAClC,IAAI,EAAE,QAAQ;SACf,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAElC,OAAO;YACL,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,QAAQ;YACpB,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;SAChD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,OAAmC,EACnC,aAAqB,EACrB,OAAkB,EAClB,SAAe;QAEf,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO;YACL,KAAK;YACL,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YACjD,QAAQ,EAAE,SAAS,CAAC,QAAQ;SAC7B,CAAC;IACJ,CAAC;CACF;AAGD,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,EAAC,aAAa,EAAE,YAAY,EAAE,cAAc,EAAmE,EAAiB,EAAE;IAChK,kCAAkC;IAClC,gEAAgE;IAChE,gEAAgE;IAChE,wEAAwE;IACxE,wBAAwB;IAExB,MAAM,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,QAAc,EAAE,EAAE;QAC3D,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QAC5B,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAChE,OAAO,QAAQ,CAAC,QAAQ,EAAE,KAAK,gBAAgB,CAAC,QAAQ,EAAE,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAEd,MAAM,QAAQ,GAAG,IAAI,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IAChE,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC;IAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5B,6BAA6B;IAC7B,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;IAElC,sCAAsC;IACtC,gEAAgE;IAChE,qCAAqC;IACrC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;QACxB,QAAQ;QACR,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE,CAAC,WAAW,CAAC;KAC/B,CAAC,CAAC,CAAC;IAEJ,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAChE,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC1D,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,SAAS,CAAC,QAAQ;gBAC7B,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;gBACjC,GAAG,EAAE,SAAS,CAAC,SAAS;gBACxB,GAAG,EAAE,SAAS,CAAC,QAAQ;aACxB,CAAC,CAAC;YACH,OAAM;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,MAAM,EAAE,KAAK;gBACb,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,kBAAkB,KAAK,EAAE;aAC7C,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC;IACrC,wBAAwB;IACxB,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;QAClC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,gDAAgD,SAAS,EAAE,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,gEAAgE;IAChE,iDAAiD;IACjD,MAAM,aAAa,GAAkB,mBAAmB,CAAC;QACvD,QAAQ;QACR,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE,CAAC,WAAW,CAAC;KAC/B,CAAC,CAAA;IAEF,aAAa,CAAC,sBAAsB,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;IAElF,OAAO,aAAa,CAAC;AACvB,CAAC,CAAA"}