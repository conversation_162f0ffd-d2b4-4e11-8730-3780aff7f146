<template>
    <view class="house_card" @click="gotoHouseDetail">
        <view class="house_image">
            <image :src="$t.getImgUrl(info.piclink)" mode="aspectFill"></image>
        </view>
        <view class="house_info">
            <view class="house_title">{{ info.title }}</view>
            <view class="house_desc">{{ info.room }}室{{ info.hall }}厅 | {{ info.area }}㎡ | {{ info.direction }} |
                {{ info.community }}</view>
            <view class="house_tags">
                <view class="house_tag house_tag_highlight" v-if="info.is_quality">优质户型</view>
                <view class="house_tag" v-if="info.near_subway">近地铁</view>
                <view class="house_tag" v-if="info.bright_layout">全明格局</view>
                <view class="house_tag" v-if="info.has_school">有学区</view>
            </view>
            <view class="house_price_row">
                <view class="house_price">{{ info.price }}万</view>
                <view class="house_unit_price">{{ info.unit_price }}元/㎡</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'house-card',
    props: {
        info: {
            type: Object,
            default: () => ({
                id: '',
                piclink: '',
                title: '人气好房·龙湖天序标准三房 靠东门业主精装自住',
                room: 3,
                hall: 2,
                area: 90,
                direction: '朝南',
                community: '',
                price: 169,
                unit_price: 18778,
                is_quality: true,
                near_subway: true,
                bright_layout: true,
                has_school: true
            })
        }
    },
    methods: {
        gotoHouseDetail() {
            this.$Router.push(`/pages/made/house_details/house_details?id=${this.info.id}`);
        }
    }
}
</script>

<style lang="scss" scoped>
.house_card {
    width: 100%;
    @include flex-center(row, flex-start, flex-start);
    gap: 20rpx;
    padding-bottom: 30rpx;
    border-bottom: 1rpx solid #eee;
    margin-bottom: 30rpx;

    .house_image {
        width: 185rpx;
        height: 222rpx;
        border-radius: 12rpx;
        overflow: hidden;
        flex-shrink: 0;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .house_info {
        flex: 1;
        @include flex-center(column, flex-start, flex-start);
        gap: 10rpx;

        >view {
            width: 100%;
        }

        .house_title {
            font-weight: bold;
            font-size: 26rpx;
            color: #000000;
            line-height: 36rpx;
            @include text-overflow(100%, 2);
        }

        .house_desc {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
        }

        .house_tags {
            @include flex-center(row, flex-start, center, wrap);
            gap: 10rpx;

            .house_tag {
                padding: 4rpx 8rpx;
                border-radius: 4rpx;
                background: #f7f7f7;
                font-size: 22rpx;
                color: #8A8A8A;

                &.house_tag_highlight {
                    background: #F9F3EC;
                    color: #B28C62;
                }
            }
        }

        .house_price_row {
            @include flex-center(row, flex-start, flex-end);
            gap: 16rpx;

            .house_price {
                font-size: 32rpx;
                font-weight: bold;
                color: #ff4444;
                line-height: 32rpx;
            }

            .house_unit_price {
                font-size: 22rpx;
                color: #999999;
                line-height: 22rpx;
            }
        }
    }
}
</style>