#!/usr/bin/env node
"use strict";var oh=Object.create;var jn=Object.defineProperty;var ch=Object.getOwnPropertyDescriptor;var lh=Object.getOwnPropertyNames;var ph=Object.getPrototypeOf,uh=Object.prototype.hasOwnProperty;var A=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Ho=(t,e)=>{for(var a in e)jn(t,a,{get:e[a],enumerable:!0})},dh=(t,e,a,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of lh(e))!uh.call(t,r)&&r!==a&&jn(t,r,{get:()=>e[r],enumerable:!(s=ch(e,r))||s.enumerable});return t};var Be=(t,e,a)=>(a=t!=null?oh(ph(t)):{},dh(e||!t||!t.__esModule?jn(a,"default",{value:t,enumerable:!0}):a,t));var Ec=A((xs,wc)=>{(function(t,e){typeof xs=="object"&&typeof wc<"u"?e(xs):typeof define=="function"&&define.amd?define(["exports"],e):e(t.URI=t.URI||{})})(xs,function(t){"use strict";function e(){for(var y=arguments.length,v=Array(y),_=0;_<y;_++)v[_]=arguments[_];if(v.length>1){v[0]=v[0].slice(0,-1);for(var S=v.length-1,P=1;P<S;++P)v[P]=v[P].slice(1,-1);return v[S]=v[S].slice(1),v.join("")}else return v[0]}function a(y){return"(?:"+y+")"}function s(y){return y===void 0?"undefined":y===null?"null":Object.prototype.toString.call(y).split(" ").pop().split("]").shift().toLowerCase()}function r(y){return y.toUpperCase()}function n(y){return y!=null?y instanceof Array?y:typeof y.length!="number"||y.split||y.setInterval||y.call?[y]:Array.prototype.slice.call(y):[]}function i(y,v){var _=y;if(v)for(var S in v)_[S]=v[S];return _}function o(y){var v="[A-Za-z]",_="[\\x0D]",S="[0-9]",P="[\\x22]",V=e(S,"[A-Fa-f]"),ae="[\\x0A]",ue="[\\x20]",he=a(a("%[EFef]"+V+"%"+V+V+"%"+V+V)+"|"+a("%[89A-Fa-f]"+V+"%"+V+V)+"|"+a("%"+V+V)),je="[\\:\\/\\?\\#\\[\\]\\@]",oe="[\\!\\$\\&\\'\\(\\)\\*\\+\\,\\;\\=]",Se=e(je,oe),Ie=y?"[\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]":"[]",we=y?"[\\uE000-\\uF8FF]":"[]",de=e(v,S,"[\\-\\.\\_\\~]",Ie),Pe=a(v+e(v,S,"[\\+\\-\\.]")+"*"),ve=a(a(he+"|"+e(de,oe,"[\\:]"))+"*"),ra=a(a("25[0-5]")+"|"+a("2[0-4]"+S)+"|"+a("1"+S+S)+"|"+a("[1-9]"+S)+"|"+S),at=a(a("25[0-5]")+"|"+a("2[0-4]"+S)+"|"+a("1"+S+S)+"|"+a("0?[1-9]"+S)+"|0?0?"+S),it=a(at+"\\."+at+"\\."+at+"\\."+at),xe=a(V+"{1,4}"),rt=a(a(xe+"\\:"+xe)+"|"+it),ot=a(a(xe+"\\:")+"{6}"+rt),St=a("\\:\\:"+a(xe+"\\:")+"{5}"+rt),sa=a(a(xe)+"?\\:\\:"+a(xe+"\\:")+"{4}"+rt),Dt=a(a(a(xe+"\\:")+"{0,1}"+xe)+"?\\:\\:"+a(xe+"\\:")+"{3}"+rt),fr=a(a(a(xe+"\\:")+"{0,2}"+xe)+"?\\:\\:"+a(xe+"\\:")+"{2}"+rt),Zr=a(a(a(xe+"\\:")+"{0,3}"+xe)+"?\\:\\:"+xe+"\\:"+rt),Gr=a(a(a(xe+"\\:")+"{0,4}"+xe)+"?\\:\\:"+rt),Na=a(a(a(xe+"\\:")+"{0,5}"+xe)+"?\\:\\:"+xe),Fa=a(a(a(xe+"\\:")+"{0,6}"+xe)+"?\\:\\:"),Nt=a([ot,St,sa,Dt,fr,Zr,Gr,Na,Fa].join("|")),La=a(a(de+"|"+he)+"+"),Cn=a(Nt+"\\%25"+La),na=a(Nt+a("\\%25|\\%(?!"+V+"{2})")+La),eh=a("[vV]"+V+"+\\."+e(de,oe,"[\\:]")+"+"),th=a("\\["+a(na+"|"+Nt+"|"+eh)+"\\]"),Uo=a(a(he+"|"+e(de,oe))+"*"),hr=a(th+"|"+it+"(?!"+Uo+")|"+Uo),vr=a(S+"*"),zo=a(a(ve+"@")+"?"+hr+a("\\:"+vr)+"?"),xr=a(he+"|"+e(de,oe,"[\\:\\@]")),ah=a(xr+"*"),Mo=a(xr+"+"),rh=a(a(he+"|"+e(de,oe,"[\\@]"))+"+"),Ft=a(a("\\/"+ah)+"*"),$a=a("\\/"+a(Mo+Ft)+"?"),On=a(rh+Ft),Jr=a(Mo+Ft),qa="(?!"+xr+")",vw=a(Ft+"|"+$a+"|"+On+"|"+Jr+"|"+qa),Ua=a(a(xr+"|"+e("[\\/\\?]",we))+"*"),gr=a(a(xr+"|[\\/\\?]")+"*"),Bo=a(a("\\/\\/"+zo+Ft)+"|"+$a+"|"+Jr+"|"+qa),sh=a(Pe+"\\:"+Bo+a("\\?"+Ua)+"?"+a("\\#"+gr)+"?"),nh=a(a("\\/\\/"+zo+Ft)+"|"+$a+"|"+On+"|"+qa),ih=a(nh+a("\\?"+Ua)+"?"+a("\\#"+gr)+"?"),xw=a(sh+"|"+ih),gw=a(Pe+"\\:"+Bo+a("\\?"+Ua)+"?"),yw="^("+Pe+")\\:"+a(a("\\/\\/("+a("("+ve+")@")+"?("+hr+")"+a("\\:("+vr+")")+"?)")+"?("+Ft+"|"+$a+"|"+Jr+"|"+qa+")")+a("\\?("+Ua+")")+"?"+a("\\#("+gr+")")+"?$",bw="^(){0}"+a(a("\\/\\/("+a("("+ve+")@")+"?("+hr+")"+a("\\:("+vr+")")+"?)")+"?("+Ft+"|"+$a+"|"+On+"|"+qa+")")+a("\\?("+Ua+")")+"?"+a("\\#("+gr+")")+"?$",_w="^("+Pe+")\\:"+a(a("\\/\\/("+a("("+ve+")@")+"?("+hr+")"+a("\\:("+vr+")")+"?)")+"?("+Ft+"|"+$a+"|"+Jr+"|"+qa+")")+a("\\?("+Ua+")")+"?$",ww="^"+a("\\#("+gr+")")+"?$",Ew="^"+a("("+ve+")@")+"?("+hr+")"+a("\\:("+vr+")")+"?$";return{NOT_SCHEME:new RegExp(e("[^]",v,S,"[\\+\\-\\.]"),"g"),NOT_USERINFO:new RegExp(e("[^\\%\\:]",de,oe),"g"),NOT_HOST:new RegExp(e("[^\\%\\[\\]\\:]",de,oe),"g"),NOT_PATH:new RegExp(e("[^\\%\\/\\:\\@]",de,oe),"g"),NOT_PATH_NOSCHEME:new RegExp(e("[^\\%\\/\\@]",de,oe),"g"),NOT_QUERY:new RegExp(e("[^\\%]",de,oe,"[\\:\\@\\/\\?]",we),"g"),NOT_FRAGMENT:new RegExp(e("[^\\%]",de,oe,"[\\:\\@\\/\\?]"),"g"),ESCAPE:new RegExp(e("[^]",de,oe),"g"),UNRESERVED:new RegExp(de,"g"),OTHER_CHARS:new RegExp(e("[^\\%]",de,Se),"g"),PCT_ENCODED:new RegExp(he,"g"),IPV4ADDRESS:new RegExp("^("+it+")$"),IPV6ADDRESS:new RegExp("^\\[?("+Nt+")"+a(a("\\%25|\\%(?!"+V+"{2})")+"("+La+")")+"?\\]?$")}}var p=o(!1),c=o(!0),l=function(){function y(v,_){var S=[],P=!0,V=!1,ae=void 0;try{for(var ue=v[Symbol.iterator](),he;!(P=(he=ue.next()).done)&&(S.push(he.value),!(_&&S.length===_));P=!0);}catch(je){V=!0,ae=je}finally{try{!P&&ue.return&&ue.return()}finally{if(V)throw ae}}return S}return function(v,_){if(Array.isArray(v))return v;if(Symbol.iterator in Object(v))return y(v,_);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(y){if(Array.isArray(y)){for(var v=0,_=Array(y.length);v<y.length;v++)_[v]=y[v];return _}else return Array.from(y)},x=2147483647,d=36,f=1,g=26,h=38,w=700,R=72,E=128,k="-",T=/^xn--/,C=/[^\0-\x7E]/,B=/[\x2E\u3002\uFF0E\uFF61]/g,U={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},I=d-f,Z=Math.floor,G=String.fromCharCode;function M(y){throw new RangeError(U[y])}function D(y,v){for(var _=[],S=y.length;S--;)_[S]=v(y[S]);return _}function O(y,v){var _=y.split("@"),S="";_.length>1&&(S=_[0]+"@",y=_[1]),y=y.replace(B,".");var P=y.split("."),V=D(P,v).join(".");return S+V}function H(y){for(var v=[],_=0,S=y.length;_<S;){var P=y.charCodeAt(_++);if(P>=55296&&P<=56319&&_<S){var V=y.charCodeAt(_++);(V&64512)==56320?v.push(((P&1023)<<10)+(V&1023)+65536):(v.push(P),_--)}else v.push(P)}return v}var le=function(v){return String.fromCodePoint.apply(String,m(v))},F=function(v){return v-48<10?v-22:v-65<26?v-65:v-97<26?v-97:d},L=function(v,_){return v+22+75*(v<26)-((_!=0)<<5)},Q=function(v,_,S){var P=0;for(v=S?Z(v/w):v>>1,v+=Z(v/_);v>I*g>>1;P+=d)v=Z(v/I);return Z(P+(I+1)*v/(v+h))},K=function(v){var _=[],S=v.length,P=0,V=E,ae=R,ue=v.lastIndexOf(k);ue<0&&(ue=0);for(var he=0;he<ue;++he)v.charCodeAt(he)>=128&&M("not-basic"),_.push(v.charCodeAt(he));for(var je=ue>0?ue+1:0;je<S;){for(var oe=P,Se=1,Ie=d;;Ie+=d){je>=S&&M("invalid-input");var we=F(v.charCodeAt(je++));(we>=d||we>Z((x-P)/Se))&&M("overflow"),P+=we*Se;var de=Ie<=ae?f:Ie>=ae+g?g:Ie-ae;if(we<de)break;var Pe=d-de;Se>Z(x/Pe)&&M("overflow"),Se*=Pe}var ve=_.length+1;ae=Q(P-oe,ve,oe==0),Z(P/ve)>x-V&&M("overflow"),V+=Z(P/ve),P%=ve,_.splice(P++,0,V)}return String.fromCodePoint.apply(String,_)},re=function(v){var _=[];v=H(v);var S=v.length,P=E,V=0,ae=R,ue=!0,he=!1,je=void 0;try{for(var oe=v[Symbol.iterator](),Se;!(ue=(Se=oe.next()).done);ue=!0){var Ie=Se.value;Ie<128&&_.push(G(Ie))}}catch(na){he=!0,je=na}finally{try{!ue&&oe.return&&oe.return()}finally{if(he)throw je}}var we=_.length,de=we;for(we&&_.push(k);de<S;){var Pe=x,ve=!0,ra=!1,at=void 0;try{for(var it=v[Symbol.iterator](),xe;!(ve=(xe=it.next()).done);ve=!0){var rt=xe.value;rt>=P&&rt<Pe&&(Pe=rt)}}catch(na){ra=!0,at=na}finally{try{!ve&&it.return&&it.return()}finally{if(ra)throw at}}var ot=de+1;Pe-P>Z((x-V)/ot)&&M("overflow"),V+=(Pe-P)*ot,P=Pe;var St=!0,sa=!1,Dt=void 0;try{for(var fr=v[Symbol.iterator](),Zr;!(St=(Zr=fr.next()).done);St=!0){var Gr=Zr.value;if(Gr<P&&++V>x&&M("overflow"),Gr==P){for(var Na=V,Fa=d;;Fa+=d){var Nt=Fa<=ae?f:Fa>=ae+g?g:Fa-ae;if(Na<Nt)break;var La=Na-Nt,Cn=d-Nt;_.push(G(L(Nt+La%Cn,0))),Na=Z(La/Cn)}_.push(G(L(Na,0))),ae=Q(V,ot,de==we),V=0,++de}}}catch(na){sa=!0,Dt=na}finally{try{!St&&fr.return&&fr.return()}finally{if(sa)throw Dt}}++V,++P}return _.join("")},be=function(v){return O(v,function(_){return T.test(_)?K(_.slice(4).toLowerCase()):_})},Ne=function(v){return O(v,function(_){return C.test(_)?"xn--"+re(_):_})},Y={version:"2.1.0",ucs2:{decode:H,encode:le},decode:K,encode:re,toASCII:Ne,toUnicode:be},pe={};function Ce(y){var v=y.charCodeAt(0),_=void 0;return v<16?_="%0"+v.toString(16).toUpperCase():v<128?_="%"+v.toString(16).toUpperCase():v<2048?_="%"+(v>>6|192).toString(16).toUpperCase()+"%"+(v&63|128).toString(16).toUpperCase():_="%"+(v>>12|224).toString(16).toUpperCase()+"%"+(v>>6&63|128).toString(16).toUpperCase()+"%"+(v&63|128).toString(16).toUpperCase(),_}function Fe(y){for(var v="",_=0,S=y.length;_<S;){var P=parseInt(y.substr(_+1,2),16);if(P<128)v+=String.fromCharCode(P),_+=3;else if(P>=194&&P<224){if(S-_>=6){var V=parseInt(y.substr(_+4,2),16);v+=String.fromCharCode((P&31)<<6|V&63)}else v+=y.substr(_,6);_+=6}else if(P>=224){if(S-_>=9){var ae=parseInt(y.substr(_+4,2),16),ue=parseInt(y.substr(_+7,2),16);v+=String.fromCharCode((P&15)<<12|(ae&63)<<6|ue&63)}else v+=y.substr(_,9);_+=9}else v+=y.substr(_,3),_+=3}return v}function _e(y,v){function _(S){var P=Fe(S);return P.match(v.UNRESERVED)?P:S}return y.scheme&&(y.scheme=String(y.scheme).replace(v.PCT_ENCODED,_).toLowerCase().replace(v.NOT_SCHEME,"")),y.userinfo!==void 0&&(y.userinfo=String(y.userinfo).replace(v.PCT_ENCODED,_).replace(v.NOT_USERINFO,Ce).replace(v.PCT_ENCODED,r)),y.host!==void 0&&(y.host=String(y.host).replace(v.PCT_ENCODED,_).toLowerCase().replace(v.NOT_HOST,Ce).replace(v.PCT_ENCODED,r)),y.path!==void 0&&(y.path=String(y.path).replace(v.PCT_ENCODED,_).replace(y.scheme?v.NOT_PATH:v.NOT_PATH_NOSCHEME,Ce).replace(v.PCT_ENCODED,r)),y.query!==void 0&&(y.query=String(y.query).replace(v.PCT_ENCODED,_).replace(v.NOT_QUERY,Ce).replace(v.PCT_ENCODED,r)),y.fragment!==void 0&&(y.fragment=String(y.fragment).replace(v.PCT_ENCODED,_).replace(v.NOT_FRAGMENT,Ce).replace(v.PCT_ENCODED,r)),y}function jt(y){return y.replace(/^0*(.*)/,"$1")||"0"}function Oe(y,v){var _=y.match(v.IPV4ADDRESS)||[],S=l(_,2),P=S[1];return P?P.split(".").map(jt).join("."):y}function ke(y,v){var _=y.match(v.IPV6ADDRESS)||[],S=l(_,3),P=S[1],V=S[2];if(P){for(var ae=P.toLowerCase().split("::").reverse(),ue=l(ae,2),he=ue[0],je=ue[1],oe=je?je.split(":").map(jt):[],Se=he.split(":").map(jt),Ie=v.IPV4ADDRESS.test(Se[Se.length-1]),we=Ie?7:8,de=Se.length-we,Pe=Array(we),ve=0;ve<we;++ve)Pe[ve]=oe[ve]||Se[de+ve]||"";Ie&&(Pe[we-1]=Oe(Pe[we-1],v));var ra=Pe.reduce(function(ot,St,sa){if(!St||St==="0"){var Dt=ot[ot.length-1];Dt&&Dt.index+Dt.length===sa?Dt.length++:ot.push({index:sa,length:1})}return ot},[]),at=ra.sort(function(ot,St){return St.length-ot.length})[0],it=void 0;if(at&&at.length>1){var xe=Pe.slice(0,at.index),rt=Pe.slice(at.index+at.length);it=xe.join(":")+"::"+rt.join(":")}else it=Pe.join(":");return V&&(it+="%"+V),it}else return y}var ea=/^(?:([^:\/?#]+):)?(?:\/\/((?:([^\/?#@]*)@)?(\[[^\/?#\]]+\]|[^\/?#:]*)(?:\:(\d*))?))?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n|\r)*))?/i,Me="".match(/(){0}/)[1]===void 0;function fe(y){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},_={},S=v.iri!==!1?c:p;v.reference==="suffix"&&(y=(v.scheme?v.scheme+":":"")+"//"+y);var P=y.match(ea);if(P){Me?(_.scheme=P[1],_.userinfo=P[3],_.host=P[4],_.port=parseInt(P[5],10),_.path=P[6]||"",_.query=P[7],_.fragment=P[8],isNaN(_.port)&&(_.port=P[5])):(_.scheme=P[1]||void 0,_.userinfo=y.indexOf("@")!==-1?P[3]:void 0,_.host=y.indexOf("//")!==-1?P[4]:void 0,_.port=parseInt(P[5],10),_.path=P[6]||"",_.query=y.indexOf("?")!==-1?P[7]:void 0,_.fragment=y.indexOf("#")!==-1?P[8]:void 0,isNaN(_.port)&&(_.port=y.match(/\/\/(?:.|\n)*\:(?:\/|\?|\#|$)/)?P[4]:void 0)),_.host&&(_.host=ke(Oe(_.host,S),S)),_.scheme===void 0&&_.userinfo===void 0&&_.host===void 0&&_.port===void 0&&!_.path&&_.query===void 0?_.reference="same-document":_.scheme===void 0?_.reference="relative":_.fragment===void 0?_.reference="absolute":_.reference="uri",v.reference&&v.reference!=="suffix"&&v.reference!==_.reference&&(_.error=_.error||"URI is not a "+v.reference+" reference.");var V=pe[(v.scheme||_.scheme||"").toLowerCase()];if(!v.unicodeSupport&&(!V||!V.unicodeSupport)){if(_.host&&(v.domainHost||V&&V.domainHost))try{_.host=Y.toASCII(_.host.replace(S.PCT_ENCODED,Fe).toLowerCase())}catch(ae){_.error=_.error||"Host's domain name can not be converted to ASCII via punycode: "+ae}_e(_,p)}else _e(_,S);V&&V.parse&&V.parse(_,v)}else _.error=_.error||"URI can not be parsed.";return _}function Bt(y,v){var _=v.iri!==!1?c:p,S=[];return y.userinfo!==void 0&&(S.push(y.userinfo),S.push("@")),y.host!==void 0&&S.push(ke(Oe(String(y.host),_),_).replace(_.IPV6ADDRESS,function(P,V,ae){return"["+V+(ae?"%25"+ae:"")+"]"})),(typeof y.port=="number"||typeof y.port=="string")&&(S.push(":"),S.push(String(y.port))),S.length?S.join(""):void 0}var It=/^\.\.?\//,ta=/^\/\.(\/|$)/,aa=/^\/\.\.(\/|$)/,Ue=/^\/?(?:.|\n)*?(?=\/|$)/;function nt(y){for(var v=[];y.length;)if(y.match(It))y=y.replace(It,"");else if(y.match(ta))y=y.replace(ta,"/");else if(y.match(aa))y=y.replace(aa,"/"),v.pop();else if(y==="."||y==="..")y="";else{var _=y.match(Ue);if(_){var S=_[0];y=y.slice(S.length),v.push(S)}else throw new Error("Unexpected dot segment condition")}return v.join("")}function Ge(y){var v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},_=v.iri?c:p,S=[],P=pe[(v.scheme||y.scheme||"").toLowerCase()];if(P&&P.serialize&&P.serialize(y,v),y.host&&!_.IPV6ADDRESS.test(y.host)){if(v.domainHost||P&&P.domainHost)try{y.host=v.iri?Y.toUnicode(y.host):Y.toASCII(y.host.replace(_.PCT_ENCODED,Fe).toLowerCase())}catch(ue){y.error=y.error||"Host's domain name can not be converted to "+(v.iri?"Unicode":"ASCII")+" via punycode: "+ue}}_e(y,_),v.reference!=="suffix"&&y.scheme&&(S.push(y.scheme),S.push(":"));var V=Bt(y,v);if(V!==void 0&&(v.reference!=="suffix"&&S.push("//"),S.push(V),y.path&&y.path.charAt(0)!=="/"&&S.push("/")),y.path!==void 0){var ae=y.path;!v.absolutePath&&(!P||!P.absolutePath)&&(ae=nt(ae)),V===void 0&&(ae=ae.replace(/^\/\//,"/%2F")),S.push(ae)}return y.query!==void 0&&(S.push("?"),S.push(y.query)),y.fragment!==void 0&&(S.push("#"),S.push(y.fragment)),S.join("")}function Ve(y,v){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},S=arguments[3],P={};return S||(y=fe(Ge(y,_),_),v=fe(Ge(v,_),_)),_=_||{},!_.tolerant&&v.scheme?(P.scheme=v.scheme,P.userinfo=v.userinfo,P.host=v.host,P.port=v.port,P.path=nt(v.path||""),P.query=v.query):(v.userinfo!==void 0||v.host!==void 0||v.port!==void 0?(P.userinfo=v.userinfo,P.host=v.host,P.port=v.port,P.path=nt(v.path||""),P.query=v.query):(v.path?(v.path.charAt(0)==="/"?P.path=nt(v.path):((y.userinfo!==void 0||y.host!==void 0||y.port!==void 0)&&!y.path?P.path="/"+v.path:y.path?P.path=y.path.slice(0,y.path.lastIndexOf("/")+1)+v.path:P.path=v.path,P.path=nt(P.path)),P.query=v.query):(P.path=y.path,v.query!==void 0?P.query=v.query:P.query=y.query),P.userinfo=y.userinfo,P.host=y.host,P.port=y.port),P.scheme=y.scheme),P.fragment=v.fragment,P}function wt(y,v,_){var S=i({scheme:"null"},_);return Ge(Ve(fe(y,S),fe(v,S),S,!0),S)}function tt(y,v){return typeof y=="string"?y=Ge(fe(y,v),v):s(y)==="object"&&(y=fe(Ge(y,v),v)),y}function Vr(y,v,_){return typeof y=="string"?y=Ge(fe(y,_),_):s(y)==="object"&&(y=Ge(y,_)),typeof v=="string"?v=Ge(fe(v,_),_):s(v)==="object"&&(v=Ge(v,_)),y===v}function Tn(y,v){return y&&y.toString().replace(!v||!v.iri?p.ESCAPE:c.ESCAPE,Ce)}function ht(y,v){return y&&y.toString().replace(!v||!v.iri?p.PCT_ENCODED:c.PCT_ENCODED,Fe)}var dr={scheme:"http",domainHost:!0,parse:function(v,_){return v.host||(v.error=v.error||"HTTP URIs must have a host."),v},serialize:function(v,_){var S=String(v.scheme).toLowerCase()==="https";return(v.port===(S?443:80)||v.port==="")&&(v.port=void 0),v.path||(v.path="/"),v}},jo={scheme:"https",domainHost:dr.domainHost,parse:dr.parse,serialize:dr.serialize};function Io(y){return typeof y.secure=="boolean"?y.secure:String(y.scheme).toLowerCase()==="wss"}var mr={scheme:"ws",domainHost:!0,parse:function(v,_){var S=v;return S.secure=Io(S),S.resourceName=(S.path||"/")+(S.query?"?"+S.query:""),S.path=void 0,S.query=void 0,S},serialize:function(v,_){if((v.port===(Io(v)?443:80)||v.port==="")&&(v.port=void 0),typeof v.secure=="boolean"&&(v.scheme=v.secure?"wss":"ws",v.secure=void 0),v.resourceName){var S=v.resourceName.split("?"),P=l(S,2),V=P[0],ae=P[1];v.path=V&&V!=="/"?V:void 0,v.query=ae,v.resourceName=void 0}return v.fragment=void 0,v}},Do={scheme:"wss",domainHost:mr.domainHost,parse:mr.parse,serialize:mr.serialize},Mf={},Bf=!0,No="[A-Za-z0-9\\-\\.\\_\\~"+(Bf?"\\xA0-\\u200D\\u2010-\\u2029\\u202F-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF":"")+"]",Et="[0-9A-Fa-f]",Hf=a(a("%[EFef]"+Et+"%"+Et+Et+"%"+Et+Et)+"|"+a("%[89A-Fa-f]"+Et+"%"+Et+Et)+"|"+a("%"+Et+Et)),Vf="[A-Za-z0-9\\!\\$\\%\\'\\*\\+\\-\\^\\_\\`\\{\\|\\}\\~]",Zf="[\\!\\$\\%\\'\\(\\)\\*\\+\\,\\-\\.0-9\\<\\>A-Z\\x5E-\\x7E]",Gf=e(Zf,'[\\"\\\\]'),Jf="[\\!\\$\\'\\(\\)\\*\\+\\,\\;\\:\\@]",Kf=new RegExp(No,"g"),Da=new RegExp(Hf,"g"),Qf=new RegExp(e("[^]",Vf,"[\\.]",'[\\"]',Gf),"g"),Fo=new RegExp(e("[^]",No,Jf),"g"),Wf=Fo;function An(y){var v=Fe(y);return v.match(Kf)?v:y}var Lo={scheme:"mailto",parse:function(v,_){var S=v,P=S.to=S.path?S.path.split(","):[];if(S.path=void 0,S.query){for(var V=!1,ae={},ue=S.query.split("&"),he=0,je=ue.length;he<je;++he){var oe=ue[he].split("=");switch(oe[0]){case"to":for(var Se=oe[1].split(","),Ie=0,we=Se.length;Ie<we;++Ie)P.push(Se[Ie]);break;case"subject":S.subject=ht(oe[1],_);break;case"body":S.body=ht(oe[1],_);break;default:V=!0,ae[ht(oe[0],_)]=ht(oe[1],_);break}}V&&(S.headers=ae)}S.query=void 0;for(var de=0,Pe=P.length;de<Pe;++de){var ve=P[de].split("@");if(ve[0]=ht(ve[0]),_.unicodeSupport)ve[1]=ht(ve[1],_).toLowerCase();else try{ve[1]=Y.toASCII(ht(ve[1],_).toLowerCase())}catch(ra){S.error=S.error||"Email address's domain name can not be converted to ASCII via punycode: "+ra}P[de]=ve.join("@")}return S},serialize:function(v,_){var S=v,P=n(v.to);if(P){for(var V=0,ae=P.length;V<ae;++V){var ue=String(P[V]),he=ue.lastIndexOf("@"),je=ue.slice(0,he).replace(Da,An).replace(Da,r).replace(Qf,Ce),oe=ue.slice(he+1);try{oe=_.iri?Y.toUnicode(oe):Y.toASCII(ht(oe,_).toLowerCase())}catch(de){S.error=S.error||"Email address's domain name can not be converted to "+(_.iri?"Unicode":"ASCII")+" via punycode: "+de}P[V]=je+"@"+oe}S.path=P.join(",")}var Se=v.headers=v.headers||{};v.subject&&(Se.subject=v.subject),v.body&&(Se.body=v.body);var Ie=[];for(var we in Se)Se[we]!==Mf[we]&&Ie.push(we.replace(Da,An).replace(Da,r).replace(Fo,Ce)+"="+Se[we].replace(Da,An).replace(Da,r).replace(Wf,Ce));return Ie.length&&(S.query=Ie.join("&")),S}},Xf=/^([^\:]+)\:(.*)/,$o={scheme:"urn",parse:function(v,_){var S=v.path&&v.path.match(Xf),P=v;if(S){var V=_.scheme||P.scheme||"urn",ae=S[1].toLowerCase(),ue=S[2],he=V+":"+(_.nid||ae),je=pe[he];P.nid=ae,P.nss=ue,P.path=void 0,je&&(P=je.parse(P,_))}else P.error=P.error||"URN can not be parsed.";return P},serialize:function(v,_){var S=_.scheme||v.scheme||"urn",P=v.nid,V=S+":"+(_.nid||P),ae=pe[V];ae&&(v=ae.serialize(v,_));var ue=v,he=v.nss;return ue.path=(P||_.nid)+":"+he,ue}},Yf=/^[0-9A-Fa-f]{8}(?:\-[0-9A-Fa-f]{4}){3}\-[0-9A-Fa-f]{12}$/,qo={scheme:"urn:uuid",parse:function(v,_){var S=v;return S.uuid=S.nss,S.nss=void 0,!_.tolerant&&(!S.uuid||!S.uuid.match(Yf))&&(S.error=S.error||"UUID is not valid."),S},serialize:function(v,_){var S=v;return S.nss=(v.uuid||"").toLowerCase(),S}};pe[dr.scheme]=dr,pe[jo.scheme]=jo,pe[mr.scheme]=mr,pe[Do.scheme]=Do,pe[Lo.scheme]=Lo,pe[$o.scheme]=$o,pe[qo.scheme]=qo,t.SCHEMES=pe,t.pctEncChar=Ce,t.pctDecChars=Fe,t.parse=fe,t.removeDotSegments=nt,t.serialize=Ge,t.resolveComponents=Ve,t.resolve=wt,t.normalize=tt,t.equal=Vr,t.escapeComponent=Tn,t.unescapeComponent=ht,Object.defineProperty(t,"__esModule",{value:!0})})});var gs=A((cE,Sc)=>{"use strict";Sc.exports=function t(e,a){if(e===a)return!0;if(e&&a&&typeof e=="object"&&typeof a=="object"){if(e.constructor!==a.constructor)return!1;var s,r,n;if(Array.isArray(e)){if(s=e.length,s!=a.length)return!1;for(r=s;r--!==0;)if(!t(e[r],a[r]))return!1;return!0}if(e.constructor===RegExp)return e.source===a.source&&e.flags===a.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===a.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===a.toString();if(n=Object.keys(e),s=n.length,s!==Object.keys(a).length)return!1;for(r=s;r--!==0;)if(!Object.prototype.hasOwnProperty.call(a,n[r]))return!1;for(r=s;r--!==0;){var i=n[r];if(!t(e[i],a[i]))return!1}return!0}return e!==e&&a!==a}});var Rc=A((lE,Pc)=>{"use strict";Pc.exports=function(e){for(var a=0,s=e.length,r=0,n;r<s;)a++,n=e.charCodeAt(r++),n>=55296&&n<=56319&&r<s&&(n=e.charCodeAt(r),(n&64512)==56320&&r++);return a}});var wa=A((pE,Ac)=>{"use strict";Ac.exports={copy:mx,checkDataType:Zn,checkDataTypes:fx,coerceToTypes:hx,toHash:Jn,getProperty:Kn,escapeQuotes:Qn,equal:gs(),ucs2length:Rc(),varOccurences:gx,varReplace:yx,schemaHasRules:bx,schemaHasRulesExcept:_x,schemaUnknownRules:wx,toQuotedString:Gn,getPathExpr:Ex,getPath:Sx,getData:kx,unescapeFragment:Tx,unescapeJsonPointer:Xn,escapeFragment:Ax,escapeJsonPointer:Wn};function mx(t,e){e=e||{};for(var a in t)e[a]=t[a];return e}function Zn(t,e,a,s){var r=s?" !== ":" === ",n=s?" || ":" && ",i=s?"!":"",o=s?"":"!";switch(t){case"null":return e+r+"null";case"array":return i+"Array.isArray("+e+")";case"object":return"("+i+e+n+"typeof "+e+r+'"object"'+n+o+"Array.isArray("+e+"))";case"integer":return"(typeof "+e+r+'"number"'+n+o+"("+e+" % 1)"+n+e+r+e+(a?n+i+"isFinite("+e+")":"")+")";case"number":return"(typeof "+e+r+'"'+t+'"'+(a?n+i+"isFinite("+e+")":"")+")";default:return"typeof "+e+r+'"'+t+'"'}}function fx(t,e,a){switch(t.length){case 1:return Zn(t[0],e,a,!0);default:var s="",r=Jn(t);r.array&&r.object&&(s=r.null?"(":"(!"+e+" || ",s+="typeof "+e+' !== "object")',delete r.null,delete r.array,delete r.object),r.number&&delete r.integer;for(var n in r)s+=(s?" && ":"")+Zn(n,e,a,!0);return s}}var kc=Jn(["string","number","integer","boolean","null"]);function hx(t,e){if(Array.isArray(e)){for(var a=[],s=0;s<e.length;s++){var r=e[s];(kc[r]||t==="array"&&r==="array")&&(a[a.length]=r)}if(a.length)return a}else{if(kc[e])return[e];if(t==="array"&&e==="array")return["array"]}}function Jn(t){for(var e={},a=0;a<t.length;a++)e[t[a]]=!0;return e}var vx=/^[a-z$_][a-z$_0-9]*$/i,xx=/'|\\/g;function Kn(t){return typeof t=="number"?"["+t+"]":vx.test(t)?"."+t:"['"+Qn(t)+"']"}function Qn(t){return t.replace(xx,"\\$&").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\f/g,"\\f").replace(/\t/g,"\\t")}function gx(t,e){e+="[^0-9]";var a=t.match(new RegExp(e,"g"));return a?a.length:0}function yx(t,e,a){return e+="([^0-9])",a=a.replace(/\$/g,"$$$$"),t.replace(new RegExp(e,"g"),a+"$1")}function bx(t,e){if(typeof t=="boolean")return!t;for(var a in t)if(e[a])return!0}function _x(t,e,a){if(typeof t=="boolean")return!t&&a!="not";for(var s in t)if(s!=a&&e[s])return!0}function wx(t,e){if(typeof t!="boolean"){for(var a in t)if(!e[a])return a}}function Gn(t){return"'"+Qn(t)+"'"}function Ex(t,e,a,s){var r=a?"'/' + "+e+(s?"":".replace(/~/g, '~0').replace(/\\//g, '~1')"):s?"'[' + "+e+" + ']'":"'[\\'' + "+e+" + '\\']'";return Tc(t,r)}function Sx(t,e,a){var s=Gn(a?"/"+Wn(e):Kn(e));return Tc(t,s)}var Px=/^\/(?:[^~]|~0|~1)*$/,Rx=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function kx(t,e,a){var s,r,n,i;if(t==="")return"rootData";if(t[0]=="/"){if(!Px.test(t))throw new Error("Invalid JSON-pointer: "+t);r=t,n="rootData"}else{if(i=t.match(Rx),!i)throw new Error("Invalid JSON-pointer: "+t);if(s=+i[1],r=i[2],r=="#"){if(s>=e)throw new Error("Cannot access property/index "+s+" levels up, current level is "+e);return a[e-s]}if(s>e)throw new Error("Cannot access data "+s+" levels up, current level is "+e);if(n="data"+(e-s||""),!r)return n}for(var o=n,p=r.split("/"),c=0;c<p.length;c++){var l=p[c];l&&(n+=Kn(Xn(l)),o+=" && "+n)}return o}function Tc(t,e){return t=='""'?e:(t+" + "+e).replace(/([^\\])' \+ '/g,"$1")}function Tx(t){return Xn(decodeURIComponent(t))}function Ax(t){return encodeURIComponent(Wn(t))}function Wn(t){return t.replace(/~/g,"~0").replace(/\//g,"~1")}function Xn(t){return t.replace(/~1/g,"/").replace(/~0/g,"~")}});var Yn=A((uE,Cc)=>{"use strict";var Cx=wa();Cc.exports=Ox;function Ox(t){Cx.copy(t,this)}});var jc=A((dE,Oc)=>{"use strict";var Jt=Oc.exports=function(t,e,a){typeof e=="function"&&(a=e,e={}),a=e.cb||a;var s=typeof a=="function"?a:a.pre||function(){},r=a.post||function(){};ys(e,s,r,t,"",t)};Jt.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0};Jt.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0};Jt.propsKeywords={definitions:!0,properties:!0,patternProperties:!0,dependencies:!0};Jt.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function ys(t,e,a,s,r,n,i,o,p,c){if(s&&typeof s=="object"&&!Array.isArray(s)){e(s,r,n,i,o,p,c);for(var l in s){var m=s[l];if(Array.isArray(m)){if(l in Jt.arrayKeywords)for(var x=0;x<m.length;x++)ys(t,e,a,m[x],r+"/"+l+"/"+x,n,r,l,s,x)}else if(l in Jt.propsKeywords){if(m&&typeof m=="object")for(var d in m)ys(t,e,a,m[d],r+"/"+l+"/"+jx(d),n,r,l,s,d)}else(l in Jt.keywords||t.allKeys&&!(l in Jt.skipKeywords))&&ys(t,e,a,m,r+"/"+l,n,r,l,s)}a(s,r,n,i,o,p,c)}}function jx(t){return t.replace(/~/g,"~0").replace(/\//g,"~1")}});var Ps=A((mE,Fc)=>{"use strict";var Sr=Ec(),Ic=gs(),Es=wa(),bs=Yn(),Ix=jc();Fc.exports=Qt;Qt.normalizeId=Kt;Qt.fullPath=_s;Qt.url=ws;Qt.ids=$x;Qt.inlineRef=ei;Qt.schema=Ss;function Qt(t,e,a){var s=this._refs[a];if(typeof s=="string")if(this._refs[s])s=this._refs[s];else return Qt.call(this,t,e,s);if(s=s||this._schemas[a],s instanceof bs)return ei(s.schema,this._opts.inlineRefs)?s.schema:s.validate||this._compile(s);var r=Ss.call(this,e,a),n,i,o;return r&&(n=r.schema,e=r.root,o=r.baseId),n instanceof bs?i=n.validate||t.call(this,n.schema,e,void 0,o):n!==void 0&&(i=ei(n,this._opts.inlineRefs)?n:t.call(this,n,e,void 0,o)),i}function Ss(t,e){var a=Sr.parse(e),s=Nc(a),r=_s(this._getId(t.schema));if(Object.keys(t.schema).length===0||s!==r){var n=Kt(s),i=this._refs[n];if(typeof i=="string")return Dx.call(this,t,i,a);if(i instanceof bs)i.validate||this._compile(i),t=i;else if(i=this._schemas[n],i instanceof bs){if(i.validate||this._compile(i),n==Kt(e))return{schema:i,root:t,baseId:r};t=i}else return;if(!t.schema)return;r=_s(this._getId(t.schema))}return Dc.call(this,a,r,t.schema,t)}function Dx(t,e,a){var s=Ss.call(this,t,e);if(s){var r=s.schema,n=s.baseId;t=s.root;var i=this._getId(r);return i&&(n=ws(n,i)),Dc.call(this,a,n,r,t)}}var Nx=Es.toHash(["properties","patternProperties","enum","dependencies","definitions"]);function Dc(t,e,a,s){if(t.fragment=t.fragment||"",t.fragment.slice(0,1)=="/"){for(var r=t.fragment.split("/"),n=1;n<r.length;n++){var i=r[n];if(i){if(i=Es.unescapeFragment(i),a=a[i],a===void 0)break;var o;if(!Nx[i]&&(o=this._getId(a),o&&(e=ws(e,o)),a.$ref)){var p=ws(e,a.$ref),c=Ss.call(this,s,p);c&&(a=c.schema,s=c.root,e=c.baseId)}}}if(a!==void 0&&a!==s.schema)return{schema:a,root:s,baseId:e}}}var Fx=Es.toHash(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum"]);function ei(t,e){if(e===!1)return!1;if(e===void 0||e===!0)return ti(t);if(e)return ai(t)<=e}function ti(t){var e;if(Array.isArray(t)){for(var a=0;a<t.length;a++)if(e=t[a],typeof e=="object"&&!ti(e))return!1}else for(var s in t)if(s=="$ref"||(e=t[s],typeof e=="object"&&!ti(e)))return!1;return!0}function ai(t){var e=0,a;if(Array.isArray(t)){for(var s=0;s<t.length;s++)if(a=t[s],typeof a=="object"&&(e+=ai(a)),e==1/0)return 1/0}else for(var r in t){if(r=="$ref")return 1/0;if(Fx[r])e++;else if(a=t[r],typeof a=="object"&&(e+=ai(a)+1),e==1/0)return 1/0}return e}function _s(t,e){e!==!1&&(t=Kt(t));var a=Sr.parse(t);return Nc(a)}function Nc(t){return Sr.serialize(t).split("#")[0]+"#"}var Lx=/#\/?$/;function Kt(t){return t?t.replace(Lx,""):""}function ws(t,e){return e=Kt(e),Sr.resolve(t,e)}function $x(t){var e=Kt(this._getId(t)),a={"":e},s={"":_s(e,!1)},r={},n=this;return Ix(t,{allKeys:!0},function(i,o,p,c,l,m,x){if(o!==""){var d=n._getId(i),f=a[c],g=s[c]+"/"+l;if(x!==void 0&&(g+="/"+(typeof x=="number"?x:Es.escapeFragment(x))),typeof d=="string"){d=f=Kt(f?Sr.resolve(f,d):d);var h=n._refs[d];if(typeof h=="string"&&(h=n._refs[h]),h&&h.schema){if(!Ic(i,h.schema))throw new Error('id "'+d+'" resolves to more than one schema')}else if(d!=Kt(g))if(d[0]=="#"){if(r[d]&&!Ic(i,r[d]))throw new Error('id "'+d+'" resolves to more than one schema');r[d]=i}else n._refs[d]=g}a[o]=f,s[o]=g}}),r}});var Rs=A((fE,$c)=>{"use strict";var ri=Ps();$c.exports={Validation:Lc(qx),MissingRef:Lc(si)};function qx(t){this.message="validation failed",this.errors=t,this.ajv=this.validation=!0}si.message=function(t,e){return"can't resolve reference "+e+" from id "+t};function si(t,e,a){this.message=a||si.message(t,e),this.missingRef=ri.url(t,e),this.missingSchema=ri.normalizeId(ri.fullPath(this.missingRef))}function Lc(t){return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}});var ni=A((hE,qc)=>{"use strict";qc.exports=function(t,e){e||(e={}),typeof e=="function"&&(e={cmp:e});var a=typeof e.cycles=="boolean"?e.cycles:!1,s=e.cmp&&function(n){return function(i){return function(o,p){var c={key:o,value:i[o]},l={key:p,value:i[p]};return n(c,l)}}}(e.cmp),r=[];return function n(i){if(i&&i.toJSON&&typeof i.toJSON=="function"&&(i=i.toJSON()),i!==void 0){if(typeof i=="number")return isFinite(i)?""+i:"null";if(typeof i!="object")return JSON.stringify(i);var o,p;if(Array.isArray(i)){for(p="[",o=0;o<i.length;o++)o&&(p+=","),p+=n(i[o])||"null";return p+"]"}if(i===null)return"null";if(r.indexOf(i)!==-1){if(a)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var c=r.push(i)-1,l=Object.keys(i).sort(s&&s(i));for(p="",o=0;o<l.length;o++){var m=l[o],x=n(i[m]);x&&(p&&(p+=","),p+=JSON.stringify(m)+":"+x)}return r.splice(c,1),"{"+p+"}"}}(t)}});var ii=A((vE,Uc)=>{"use strict";Uc.exports=function(e,a,s){var r="",n=e.schema.$async===!0,i=e.util.schemaHasRulesExcept(e.schema,e.RULES.all,"$ref"),o=e.self._getId(e.schema);if(e.opts.strictKeywords){var p=e.util.schemaUnknownRules(e.schema,e.RULES.keywords);if(p){var c="unknown keyword: "+p;if(e.opts.strictKeywords==="log")e.logger.warn(c);else throw new Error(c)}}if(e.isTop&&(r+=" var validate = ",n&&(e.async=!0,r+="async "),r+="function(data, dataPath, parentData, parentDataProperty, rootData) { 'use strict'; ",o&&(e.opts.sourceCode||e.opts.processCode)&&(r+=" "+("/*# sourceURL="+o+" */")+" ")),typeof e.schema=="boolean"||!(i||e.schema.$ref)){var a="false schema",l=e.level,m=e.dataLevel,x=e.schema[a],d=e.schemaPath+e.util.getProperty(a),f=e.errSchemaPath+"/"+a,T=!e.opts.allErrors,U,g="data"+(m||""),k="valid"+l;if(e.schema===!1){e.isTop?T=!0:r+=" var "+k+" = false; ";var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(U||"false schema")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(f)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'boolean schema is false' "),e.opts.verbose&&(r+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var w=r;r=h.pop(),!e.compositeRule&&T?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else e.isTop?n?r+=" return data; ":r+=" validate.errors = null; return true; ":r+=" var "+k+" = true; ";return e.isTop&&(r+=" }; return validate; "),r}if(e.isTop){var R=e.isTop,l=e.level=0,m=e.dataLevel=0,g="data";if(e.rootId=e.resolve.fullPath(e.self._getId(e.root.schema)),e.baseId=e.baseId||e.rootId,delete e.isTop,e.dataPathArr=[""],e.schema.default!==void 0&&e.opts.useDefaults&&e.opts.strictDefaults){var E="default is ignored in the schema root";if(e.opts.strictDefaults==="log")e.logger.warn(E);else throw new Error(E)}r+=" var vErrors = null; ",r+=" var errors = 0;     ",r+=" if (rootData === undefined) rootData = data; "}else{var l=e.level,m=e.dataLevel,g="data"+(m||"");if(o&&(e.baseId=e.resolve.url(e.baseId,o)),n&&!e.async)throw new Error("async schema in sync schema");r+=" var errs_"+l+" = errors;"}var k="valid"+l,T=!e.opts.allErrors,C="",B="",U,I=e.schema.type,Z=Array.isArray(I);if(I&&e.opts.nullable&&e.schema.nullable===!0&&(Z?I.indexOf("null")==-1&&(I=I.concat("null")):I!="null"&&(I=[I,"null"],Z=!0)),Z&&I.length==1&&(I=I[0],Z=!1),e.schema.$ref&&i){if(e.opts.extendRefs=="fail")throw new Error('$ref: validation keywords used in schema at path "'+e.errSchemaPath+'" (see option extendRefs)');e.opts.extendRefs!==!0&&(i=!1,e.logger.warn('$ref: keywords ignored in schema at path "'+e.errSchemaPath+'"'))}if(e.schema.$comment&&e.opts.$comment&&(r+=" "+e.RULES.all.$comment.code(e,"$comment")),I){if(e.opts.coerceTypes)var G=e.util.coerceToTypes(e.opts.coerceTypes,I);var M=e.RULES.types[I];if(G||Z||M===!0||M&&!Ue(M)){var d=e.schemaPath+".type",f=e.errSchemaPath+"/type",d=e.schemaPath+".type",f=e.errSchemaPath+"/type",D=Z?"checkDataTypes":"checkDataType";if(r+=" if ("+e.util[D](I,g,e.opts.strictNumbers,!0)+") { ",G){var O="dataType"+l,H="coerced"+l;r+=" var "+O+" = typeof "+g+"; var "+H+" = undefined; ",e.opts.coerceTypes=="array"&&(r+=" if ("+O+" == 'object' && Array.isArray("+g+") && "+g+".length == 1) { "+g+" = "+g+"[0]; "+O+" = typeof "+g+"; if ("+e.util.checkDataType(e.schema.type,g,e.opts.strictNumbers)+") "+H+" = "+g+"; } "),r+=" if ("+H+" !== undefined) ; ";var le=G;if(le)for(var F,L=-1,Q=le.length-1;L<Q;)F=le[L+=1],F=="string"?r+=" else if ("+O+" == 'number' || "+O+" == 'boolean') "+H+" = '' + "+g+"; else if ("+g+" === null) "+H+" = ''; ":F=="number"||F=="integer"?(r+=" else if ("+O+" == 'boolean' || "+g+" === null || ("+O+" == 'string' && "+g+" && "+g+" == +"+g+" ",F=="integer"&&(r+=" && !("+g+" % 1)"),r+=")) "+H+" = +"+g+"; "):F=="boolean"?r+=" else if ("+g+" === 'false' || "+g+" === 0 || "+g+" === null) "+H+" = false; else if ("+g+" === 'true' || "+g+" === 1) "+H+" = true; ":F=="null"?r+=" else if ("+g+" === '' || "+g+" === 0 || "+g+" === false) "+H+" = null; ":e.opts.coerceTypes=="array"&&F=="array"&&(r+=" else if ("+O+" == 'string' || "+O+" == 'number' || "+O+" == 'boolean' || "+g+" == null) "+H+" = ["+g+"]; ");r+=" else {   ";var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(U||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(f)+" , params: { type: '",Z?r+=""+I.join(","):r+=""+I,r+="' } ",e.opts.messages!==!1&&(r+=" , message: 'should be ",Z?r+=""+I.join(","):r+=""+I,r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+d+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var w=r;r=h.pop(),!e.compositeRule&&T?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } if ("+H+" !== undefined) {  ";var K=m?"data"+(m-1||""):"parentData",re=m?e.dataPathArr[m]:"parentDataProperty";r+=" "+g+" = "+H+"; ",m||(r+="if ("+K+" !== undefined)"),r+=" "+K+"["+re+"] = "+H+"; } "}else{var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(U||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(f)+" , params: { type: '",Z?r+=""+I.join(","):r+=""+I,r+="' } ",e.opts.messages!==!1&&(r+=" , message: 'should be ",Z?r+=""+I.join(","):r+=""+I,r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+d+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var w=r;r=h.pop(),!e.compositeRule&&T?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}r+=" } "}}if(e.schema.$ref&&!i)r+=" "+e.RULES.all.$ref.code(e,"$ref")+" ",T&&(r+=" } if (errors === ",R?r+="0":r+="errs_"+l,r+=") { ",B+="}");else{var be=e.RULES;if(be){for(var M,Ne=-1,Y=be.length-1;Ne<Y;)if(M=be[Ne+=1],Ue(M)){if(M.type&&(r+=" if ("+e.util.checkDataType(M.type,g,e.opts.strictNumbers)+") { "),e.opts.useDefaults){if(M.type=="object"&&e.schema.properties){var x=e.schema.properties,pe=Object.keys(x),Ce=pe;if(Ce)for(var Fe,_e=-1,jt=Ce.length-1;_e<jt;){Fe=Ce[_e+=1];var Oe=x[Fe];if(Oe.default!==void 0){var ke=g+e.util.getProperty(Fe);if(e.compositeRule){if(e.opts.strictDefaults){var E="default is ignored for: "+ke;if(e.opts.strictDefaults==="log")e.logger.warn(E);else throw new Error(E)}}else r+=" if ("+ke+" === undefined ",e.opts.useDefaults=="empty"&&(r+=" || "+ke+" === null || "+ke+" === '' "),r+=" ) "+ke+" = ",e.opts.useDefaults=="shared"?r+=" "+e.useDefault(Oe.default)+" ":r+=" "+JSON.stringify(Oe.default)+" ",r+="; "}}}else if(M.type=="array"&&Array.isArray(e.schema.items)){var ea=e.schema.items;if(ea){for(var Oe,L=-1,Me=ea.length-1;L<Me;)if(Oe=ea[L+=1],Oe.default!==void 0){var ke=g+"["+L+"]";if(e.compositeRule){if(e.opts.strictDefaults){var E="default is ignored for: "+ke;if(e.opts.strictDefaults==="log")e.logger.warn(E);else throw new Error(E)}}else r+=" if ("+ke+" === undefined ",e.opts.useDefaults=="empty"&&(r+=" || "+ke+" === null || "+ke+" === '' "),r+=" ) "+ke+" = ",e.opts.useDefaults=="shared"?r+=" "+e.useDefault(Oe.default)+" ":r+=" "+JSON.stringify(Oe.default)+" ",r+="; "}}}}var fe=M.rules;if(fe){for(var Bt,It=-1,ta=fe.length-1;It<ta;)if(Bt=fe[It+=1],nt(Bt)){var aa=Bt.code(e,Bt.keyword,M.type);aa&&(r+=" "+aa+" ",T&&(C+="}"))}}if(T&&(r+=" "+C+" ",C=""),M.type&&(r+=" } ",I&&I===M.type&&!G)){r+=" else { ";var d=e.schemaPath+".type",f=e.errSchemaPath+"/type",h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(U||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(f)+" , params: { type: '",Z?r+=""+I.join(","):r+=""+I,r+="' } ",e.opts.messages!==!1&&(r+=" , message: 'should be ",Z?r+=""+I.join(","):r+=""+I,r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+d+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+g+" "),r+=" } "):r+=" {} ";var w=r;r=h.pop(),!e.compositeRule&&T?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } "}T&&(r+=" if (errors === ",R?r+="0":r+="errs_"+l,r+=") { ",B+="}")}}}T&&(r+=" "+B+" "),R?(n?(r+=" if (errors === 0) return data;           ",r+=" else throw new ValidationError(vErrors); "):(r+=" validate.errors = vErrors; ",r+=" return errors === 0;       "),r+=" }; return validate;"):r+=" var "+k+" = errors === errs_"+l+";";function Ue(Ve){for(var wt=Ve.rules,tt=0;tt<wt.length;tt++)if(nt(wt[tt]))return!0}function nt(Ve){return e.schema[Ve.keyword]!==void 0||Ve.implements&&Ge(Ve)}function Ge(Ve){for(var wt=Ve.implements,tt=0;tt<wt.length;tt++)if(e.schema[wt[tt]]!==void 0)return!0}return r}});var Vc=A((xE,Hc)=>{"use strict";var ks=Ps(),As=wa(),Mc=Rs(),Ux=ni(),zc=ii(),zx=As.ucs2length,Mx=gs(),Bx=Mc.Validation;Hc.exports=oi;function oi(t,e,a,s){var r=this,n=this._opts,i=[void 0],o={},p=[],c={},l=[],m={},x=[];e=e||{schema:t,refVal:i,refs:o};var d=Hx.call(this,t,e,s),f=this._compilations[d.index];if(d.compiling)return f.callValidate=E;var g=this._formats,h=this.RULES;try{var w=k(t,e,a,s);f.validate=w;var R=f.callValidate;return R&&(R.schema=w.schema,R.errors=null,R.refs=w.refs,R.refVal=w.refVal,R.root=w.root,R.$async=w.$async,n.sourceCode&&(R.source=w.source)),w}finally{Vx.call(this,t,e,s)}function E(){var D=f.validate,O=D.apply(this,arguments);return E.errors=D.errors,O}function k(D,O,H,le){var F=!O||O&&O.schema==D;if(O.schema!=e.schema)return oi.call(r,D,O,H,le);var L=D.$async===!0,Q=zc({isTop:!0,schema:D,isRoot:F,baseId:le,root:O,schemaPath:"",errSchemaPath:"#",errorPath:'""',MissingRefError:Mc.MissingRef,RULES:h,validate:zc,util:As,resolve:ks,resolveRef:T,usePattern:Z,useDefault:G,useCustomRule:M,opts:n,formats:g,logger:r.logger,self:r});Q=Ts(i,Jx)+Ts(p,Zx)+Ts(l,Gx)+Ts(x,Kx)+Q,n.processCode&&(Q=n.processCode(Q,D));var K;try{var re=new Function("self","RULES","formats","root","refVal","defaults","customRules","equal","ucs2length","ValidationError",Q);K=re(r,h,g,e,i,l,x,Mx,zx,Bx),i[0]=K}catch(be){throw r.logger.error("Error compiling schema, function code:",Q),be}return K.schema=D,K.errors=null,K.refs=o,K.refVal=i,K.root=F?K:O,L&&(K.$async=!0),n.sourceCode===!0&&(K.source={code:Q,patterns:p,defaults:l}),K}function T(D,O,H){O=ks.url(D,O);var le=o[O],F,L;if(le!==void 0)return F=i[le],L="refVal["+le+"]",I(F,L);if(!H&&e.refs){var Q=e.refs[O];if(Q!==void 0)return F=e.refVal[Q],L=C(O,F),I(F,L)}L=C(O);var K=ks.call(r,k,e,O);if(K===void 0){var re=a&&a[O];re&&(K=ks.inlineRef(re,n.inlineRefs)?re:oi.call(r,re,e,a,D))}if(K===void 0)B(O);else return U(O,K),I(K,L)}function C(D,O){var H=i.length;return i[H]=O,o[D]=H,"refVal"+H}function B(D){delete o[D]}function U(D,O){var H=o[D];i[H]=O}function I(D,O){return typeof D=="object"||typeof D=="boolean"?{code:O,schema:D,inline:!0}:{code:O,$async:D&&!!D.$async}}function Z(D){var O=c[D];return O===void 0&&(O=c[D]=p.length,p[O]=D),"pattern"+O}function G(D){switch(typeof D){case"boolean":case"number":return""+D;case"string":return As.toQuotedString(D);case"object":if(D===null)return"null";var O=Ux(D),H=m[O];return H===void 0&&(H=m[O]=l.length,l[H]=D),"default"+H}}function M(D,O,H,le){if(r._opts.validateSchema!==!1){var F=D.definition.dependencies;if(F&&!F.every(function(Ce){return Object.prototype.hasOwnProperty.call(H,Ce)}))throw new Error("parent schema must have all required keywords: "+F.join(","));var L=D.definition.validateSchema;if(L){var Q=L(O);if(!Q){var K="keyword schema is invalid: "+r.errorsText(L.errors);if(r._opts.validateSchema=="log")r.logger.error(K);else throw new Error(K)}}}var re=D.definition.compile,be=D.definition.inline,Ne=D.definition.macro,Y;if(re)Y=re.call(r,O,H,le);else if(Ne)Y=Ne.call(r,O,H,le),n.validateSchema!==!1&&r.validateSchema(Y,!0);else if(be)Y=be.call(r,le,D.keyword,O,H);else if(Y=D.definition.validate,!Y)return;if(Y===void 0)throw new Error('custom keyword "'+D.keyword+'"failed to compile');var pe=x.length;return x[pe]=Y,{code:"customRule"+pe,validate:Y}}}function Hx(t,e,a){var s=Bc.call(this,t,e,a);return s>=0?{index:s,compiling:!0}:(s=this._compilations.length,this._compilations[s]={schema:t,root:e,baseId:a},{index:s,compiling:!1})}function Vx(t,e,a){var s=Bc.call(this,t,e,a);s>=0&&this._compilations.splice(s,1)}function Bc(t,e,a){for(var s=0;s<this._compilations.length;s++){var r=this._compilations[s];if(r.schema==t&&r.root==e&&r.baseId==a)return s}return-1}function Zx(t,e){return"var pattern"+t+" = new RegExp("+As.toQuotedString(e[t])+");"}function Gx(t){return"var default"+t+" = defaults["+t+"];"}function Jx(t,e){return e[t]===void 0?"":"var refVal"+t+" = refVal["+t+"];"}function Kx(t){return"var customRule"+t+" = customRules["+t+"];"}function Ts(t,e){if(!t.length)return"";for(var a="",s=0;s<t.length;s++)a+=e(s,t);return a}});var Gc=A((gE,Zc)=>{"use strict";var Cs=Zc.exports=function(){this._cache={}};Cs.prototype.put=function(e,a){this._cache[e]=a};Cs.prototype.get=function(e){return this._cache[e]};Cs.prototype.del=function(e){delete this._cache[e]};Cs.prototype.clear=function(){this._cache={}}});var nl=A((yE,sl)=>{"use strict";var Qx=wa(),Wx=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,Xx=[0,31,28,31,30,31,30,31,31,30,31,30,31],Yx=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i,Jc=/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,eg=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,tg=/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,Kc=/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,Qc=/^(?:(?:http[s\u017F]?|ftp):\/\/)(?:(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+(?::(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?@)?(?:(?!10(?:\.[0-9]{1,3}){3})(?!127(?:\.[0-9]{1,3}){3})(?!169\.254(?:\.[0-9]{1,3}){2})(?!192\.168(?:\.[0-9]{1,3}){2})(?!172\.(?:1[6-9]|2[0-9]|3[01])(?:\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)(?:\.(?:(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-)*(?:[0-9a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)*(?:\.(?:(?:[a-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\/(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?$/i,Wc=/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,Xc=/^(?:\/(?:[^~/]|~0|~1)*)*$/,Yc=/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,el=/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/;sl.exports=Os;function Os(t){return t=t=="full"?"full":"fast",Qx.copy(Os[t])}Os.fast={date:/^\d\d\d\d-[0-1]\d-[0-3]\d$/,time:/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,"date-time":/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,"uri-template":Kc,url:Qc,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,hostname:Jc,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:rl,uuid:Wc,"json-pointer":Xc,"json-pointer-uri-fragment":Yc,"relative-json-pointer":el};Os.full={date:tl,time:al,"date-time":sg,uri:ig,"uri-reference":tg,"uri-template":Kc,url:Qc,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:Jc,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:rl,uuid:Wc,"json-pointer":Xc,"json-pointer-uri-fragment":Yc,"relative-json-pointer":el};function ag(t){return t%4===0&&(t%100!==0||t%400===0)}function tl(t){var e=t.match(Wx);if(!e)return!1;var a=+e[1],s=+e[2],r=+e[3];return s>=1&&s<=12&&r>=1&&r<=(s==2&&ag(a)?29:Xx[s])}function al(t,e){var a=t.match(Yx);if(!a)return!1;var s=a[1],r=a[2],n=a[3],i=a[5];return(s<=23&&r<=59&&n<=59||s==23&&r==59&&n==60)&&(!e||i)}var rg=/t|\s/i;function sg(t){var e=t.split(rg);return e.length==2&&tl(e[0])&&al(e[1],!0)}var ng=/\/|:/;function ig(t){return ng.test(t)&&eg.test(t)}var og=/[^\\]\\Z/;function rl(t){if(og.test(t))return!1;try{return new RegExp(t),!0}catch{return!1}}});var ol=A((bE,il)=>{"use strict";il.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.errSchemaPath+"/"+a,c=!e.opts.allErrors,l="data"+(i||""),m="valid"+n,x,d;if(o=="#"||o=="#/")e.isRoot?(x=e.async,d="validate"):(x=e.root.schema.$async===!0,d="root.refVal[0]");else{var f=e.resolveRef(e.baseId,o,e.isRoot);if(f===void 0){var g=e.MissingRefError.message(e.baseId,o);if(e.opts.missingRefs=="fail"){e.logger.error(g);var h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '$ref' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(p)+" , params: { ref: '"+e.util.escapeQuotes(o)+"' } ",e.opts.messages!==!1&&(r+=" , message: 'can\\'t resolve reference "+e.util.escapeQuotes(o)+"' "),e.opts.verbose&&(r+=" , schema: "+e.util.toQuotedString(o)+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+l+" "),r+=" } "):r+=" {} ";var w=r;r=h.pop(),!e.compositeRule&&c?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",c&&(r+=" if (false) { ")}else if(e.opts.missingRefs=="ignore")e.logger.warn(g),c&&(r+=" if (true) { ");else throw new e.MissingRefError(e.baseId,o,g)}else if(f.inline){var R=e.util.copy(e);R.level++;var E="valid"+R.level;R.schema=f.schema,R.schemaPath="",R.errSchemaPath=o;var k=e.validate(R).replace(/validate\.schema/g,f.code);r+=" "+k+" ",c&&(r+=" if ("+E+") { ")}else x=f.$async===!0||e.async&&f.$async!==!1,d=f.code}if(d){var h=h||[];h.push(r),r="",e.opts.passContext?r+=" "+d+".call(this, ":r+=" "+d+"( ",r+=" "+l+", (dataPath || '')",e.errorPath!='""'&&(r+=" + "+e.errorPath);var T=i?"data"+(i-1||""):"parentData",C=i?e.dataPathArr[i]:"parentDataProperty";r+=" , "+T+" , "+C+", rootData)  ";var B=r;if(r=h.pop(),x){if(!e.async)throw new Error("async schema referenced by sync schema");c&&(r+=" var "+m+"; "),r+=" try { await "+B+"; ",c&&(r+=" "+m+" = true; "),r+=" } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ",c&&(r+=" "+m+" = false; "),r+=" } ",c&&(r+=" if ("+m+") { ")}else r+=" if (!"+B+") { if (vErrors === null) vErrors = "+d+".errors; else vErrors = vErrors.concat("+d+".errors); errors = vErrors.length; } ",c&&(r+=" else { ")}return r}});var ll=A((_E,cl)=>{"use strict";cl.exports=function(e,a,s){var r=" ",n=e.schema[a],i=e.schemaPath+e.util.getProperty(a),o=e.errSchemaPath+"/"+a,p=!e.opts.allErrors,c=e.util.copy(e),l="";c.level++;var m="valid"+c.level,x=c.baseId,d=!0,f=n;if(f)for(var g,h=-1,w=f.length-1;h<w;)g=f[h+=1],(e.opts.strictKeywords?typeof g=="object"&&Object.keys(g).length>0||g===!1:e.util.schemaHasRules(g,e.RULES.all))&&(d=!1,c.schema=g,c.schemaPath=i+"["+h+"]",c.errSchemaPath=o+"/"+h,r+="  "+e.validate(c)+" ",c.baseId=x,p&&(r+=" if ("+m+") { ",l+="}"));return p&&(d?r+=" if (true) { ":r+=" "+l.slice(0,-1)+" "),r}});var ul=A((wE,pl)=>{"use strict";pl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d="errs__"+n,f=e.util.copy(e),g="";f.level++;var h="valid"+f.level,w=o.every(function(U){return e.opts.strictKeywords?typeof U=="object"&&Object.keys(U).length>0||U===!1:e.util.schemaHasRules(U,e.RULES.all)});if(w){var R=f.baseId;r+=" var "+d+" = errors; var "+x+" = false;  ";var E=e.compositeRule;e.compositeRule=f.compositeRule=!0;var k=o;if(k)for(var T,C=-1,B=k.length-1;C<B;)T=k[C+=1],f.schema=T,f.schemaPath=p+"["+C+"]",f.errSchemaPath=c+"/"+C,r+="  "+e.validate(f)+" ",f.baseId=R,r+=" "+x+" = "+x+" || "+h+"; if (!"+x+") { ",g+="}";e.compositeRule=f.compositeRule=E,r+=" "+g+" if (!"+x+") {   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'anyOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should match some schema in anyOf' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),r+=" } else {  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; } ",e.opts.allErrors&&(r+=" } ")}else l&&(r+=" if (true) { ");return r}});var ml=A((EE,dl)=>{"use strict";dl.exports=function(e,a,s){var r=" ",n=e.schema[a],i=e.errSchemaPath+"/"+a,o=!e.opts.allErrors,p=e.util.toQuotedString(n);return e.opts.$comment===!0?r+=" console.log("+p+");":typeof e.opts.$comment=="function"&&(r+=" self._opts.$comment("+p+", "+e.util.toQuotedString(i)+", validate.root.schema);"),r}});var hl=A((SE,fl)=>{"use strict";fl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d=e.opts.$data&&o&&o.$data,f;d?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",f="schema"+n):f=o,d||(r+=" var schema"+n+" = validate.schema"+p+";"),r+="var "+x+" = equal("+m+", schema"+n+"); if (!"+x+") {   ";var g=g||[];g.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'const' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { allowedValue: schema"+n+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be equal to constant' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var h=r;return r=g.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+h+"]); ":r+=" validate.errors = ["+h+"]; return false; ":r+=" var err = "+h+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" }",l&&(r+=" else { "),r}});var xl=A((PE,vl)=>{"use strict";vl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d="errs__"+n,f=e.util.copy(e),g="";f.level++;var h="valid"+f.level,w="i"+n,R=f.dataLevel=e.dataLevel+1,E="data"+R,k=e.baseId,T=e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===!1:e.util.schemaHasRules(o,e.RULES.all);if(r+="var "+d+" = errors;var "+x+";",T){var C=e.compositeRule;e.compositeRule=f.compositeRule=!0,f.schema=o,f.schemaPath=p,f.errSchemaPath=c,r+=" var "+h+" = false; for (var "+w+" = 0; "+w+" < "+m+".length; "+w+"++) { ",f.errorPath=e.util.getPathExpr(e.errorPath,w,e.opts.jsonPointers,!0);var B=m+"["+w+"]";f.dataPathArr[R]=w;var U=e.validate(f);f.baseId=k,e.util.varOccurences(U,E)<2?r+=" "+e.util.varReplace(U,E,B)+" ":r+=" var "+E+" = "+B+"; "+U+" ",r+=" if ("+h+") break; }  ",e.compositeRule=f.compositeRule=C,r+=" "+g+" if (!"+h+") {"}else r+=" if ("+m+".length == 0) {";var I=I||[];I.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'contains' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should contain a valid item' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var Z=r;return r=I.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+Z+"]); ":r+=" validate.errors = ["+Z+"]; return false; ":r+=" var err = "+Z+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else { ",T&&(r+="  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; } "),e.opts.allErrors&&(r+=" } "),r}});var yl=A((RE,gl)=>{"use strict";gl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="errs__"+n,d=e.util.copy(e),f="";d.level++;var g="valid"+d.level,h={},w={},R=e.opts.ownProperties;for(C in o)if(C!="__proto__"){var E=o[C],k=Array.isArray(E)?w:h;k[C]=E}r+="var "+x+" = errors;";var T=e.errorPath;r+="var missing"+n+";";for(var C in w)if(k=w[C],k.length){if(r+=" if ( "+m+e.util.getProperty(C)+" !== undefined ",R&&(r+=" && Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(C)+"') "),l){r+=" && ( ";var B=k;if(B)for(var U,I=-1,Z=B.length-1;I<Z;){U=B[I+=1],I&&(r+=" || ");var G=e.util.getProperty(U),M=m+G;r+=" ( ( "+M+" === undefined ",R&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(U)+"') "),r+=") && (missing"+n+" = "+e.util.toQuotedString(e.opts.jsonPointers?U:G)+") ) "}r+=")) {  ";var D="missing"+n,O="' + "+D+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(T,D,!0):T+" + "+D);var H=H||[];H.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { property: '"+e.util.escapeQuotes(C)+"', missingProperty: '"+O+"', depsCount: "+k.length+", deps: '"+e.util.escapeQuotes(k.length==1?k[0]:k.join(", "))+"' } ",e.opts.messages!==!1&&(r+=" , message: 'should have ",k.length==1?r+="property "+e.util.escapeQuotes(k[0]):r+="properties "+e.util.escapeQuotes(k.join(", ")),r+=" when property "+e.util.escapeQuotes(C)+" is present' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var le=r;r=H.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+le+"]); ":r+=" validate.errors = ["+le+"]; return false; ":r+=" var err = "+le+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else{r+=" ) { ";var F=k;if(F)for(var U,L=-1,Q=F.length-1;L<Q;){U=F[L+=1];var G=e.util.getProperty(U),O=e.util.escapeQuotes(U),M=m+G;e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(T,U,e.opts.jsonPointers)),r+=" if ( "+M+" === undefined ",R&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(U)+"') "),r+=") {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { property: '"+e.util.escapeQuotes(C)+"', missingProperty: '"+O+"', depsCount: "+k.length+", deps: '"+e.util.escapeQuotes(k.length==1?k[0]:k.join(", "))+"' } ",e.opts.messages!==!1&&(r+=" , message: 'should have ",k.length==1?r+="property "+e.util.escapeQuotes(k[0]):r+="properties "+e.util.escapeQuotes(k.join(", ")),r+=" when property "+e.util.escapeQuotes(C)+" is present' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}r+=" }   ",l&&(f+="}",r+=" else { ")}e.errorPath=T;var K=d.baseId;for(var C in h){var E=h[C];(e.opts.strictKeywords?typeof E=="object"&&Object.keys(E).length>0||E===!1:e.util.schemaHasRules(E,e.RULES.all))&&(r+=" "+g+" = true; if ( "+m+e.util.getProperty(C)+" !== undefined ",R&&(r+=" && Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(C)+"') "),r+=") { ",d.schema=E,d.schemaPath=p+e.util.getProperty(C),d.errSchemaPath=c+"/"+e.util.escapeFragment(C),r+="  "+e.validate(d)+" ",d.baseId=K,r+=" }  ",l&&(r+=" if ("+g+") { ",f+="}"))}return l&&(r+="   "+f+" if ("+x+" == errors) {"),r}});var _l=A((kE,bl)=>{"use strict";bl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d=e.opts.$data&&o&&o.$data,f;d?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",f="schema"+n):f=o;var g="i"+n,h="schema"+n;d||(r+=" var "+h+" = validate.schema"+p+";"),r+="var "+x+";",d&&(r+=" if (schema"+n+" === undefined) "+x+" = true; else if (!Array.isArray(schema"+n+")) "+x+" = false; else {"),r+=""+x+" = false;for (var "+g+"=0; "+g+"<"+h+".length; "+g+"++) if (equal("+m+", "+h+"["+g+"])) { "+x+" = true; break; }",d&&(r+="  }  "),r+=" if (!"+x+") {   ";var w=w||[];w.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'enum' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { allowedValues: schema"+n+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be equal to one of the allowed values' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var R=r;return r=w.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+R+"]); ":r+=" validate.errors = ["+R+"]; return false; ":r+=" var err = "+R+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" }",l&&(r+=" else { "),r}});var El=A((TE,wl)=>{"use strict";wl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||"");if(e.opts.format===!1)return l&&(r+=" if (true) { "),r;var x=e.opts.$data&&o&&o.$data,d;x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o;var f=e.opts.unknownFormats,g=Array.isArray(f);if(x){var h="format"+n,w="isObject"+n,R="formatType"+n;r+=" var "+h+" = formats["+d+"]; var "+w+" = typeof "+h+" == 'object' && !("+h+" instanceof RegExp) && "+h+".validate; var "+R+" = "+w+" && "+h+".type || 'string'; if ("+w+") { ",e.async&&(r+=" var async"+n+" = "+h+".async; "),r+=" "+h+" = "+h+".validate; } if (  ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'string') || "),r+=" (",f!="ignore"&&(r+=" ("+d+" && !"+h+" ",g&&(r+=" && self._opts.unknownFormats.indexOf("+d+") == -1 "),r+=") || "),r+=" ("+h+" && "+R+" == '"+s+"' && !(typeof "+h+" == 'function' ? ",e.async?r+=" (async"+n+" ? await "+h+"("+m+") : "+h+"("+m+")) ":r+=" "+h+"("+m+") ",r+=" : "+h+".test("+m+"))))) {"}else{var h=e.formats[o];if(!h){if(f=="ignore")return e.logger.warn('unknown format "'+o+'" ignored in schema at path "'+e.errSchemaPath+'"'),l&&(r+=" if (true) { "),r;if(g&&f.indexOf(o)>=0)return l&&(r+=" if (true) { "),r;throw new Error('unknown format "'+o+'" is used in schema at path "'+e.errSchemaPath+'"')}var w=typeof h=="object"&&!(h instanceof RegExp)&&h.validate,R=w&&h.type||"string";if(w){var E=h.async===!0;h=h.validate}if(R!=s)return l&&(r+=" if (true) { "),r;if(E){if(!e.async)throw new Error("async format in sync schema");var k="formats"+e.util.getProperty(o)+".validate";r+=" if (!(await "+k+"("+m+"))) { "}else{r+=" if (! ";var k="formats"+e.util.getProperty(o);w&&(k+=".validate"),typeof h=="function"?r+=" "+k+"("+m+") ":r+=" "+k+".test("+m+") ",r+=") { "}}var T=T||[];T.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'format' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { format:  ",x?r+=""+d:r+=""+e.util.toQuotedString(o),r+="  } ",e.opts.messages!==!1&&(r+=` , message: 'should match format "`,x?r+="' + "+d+" + '":r+=""+e.util.escapeQuotes(o),r+=`"' `),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+e.util.toQuotedString(o),r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var C=r;return r=T.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+C+"]); ":r+=" validate.errors = ["+C+"]; return false; ":r+=" var err = "+C+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",l&&(r+=" else { "),r}});var Pl=A((AE,Sl)=>{"use strict";Sl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d="errs__"+n,f=e.util.copy(e);f.level++;var g="valid"+f.level,h=e.schema.then,w=e.schema.else,R=h!==void 0&&(e.opts.strictKeywords?typeof h=="object"&&Object.keys(h).length>0||h===!1:e.util.schemaHasRules(h,e.RULES.all)),E=w!==void 0&&(e.opts.strictKeywords?typeof w=="object"&&Object.keys(w).length>0||w===!1:e.util.schemaHasRules(w,e.RULES.all)),k=f.baseId;if(R||E){var T;f.createErrors=!1,f.schema=o,f.schemaPath=p,f.errSchemaPath=c,r+=" var "+d+" = errors; var "+x+" = true;  ";var C=e.compositeRule;e.compositeRule=f.compositeRule=!0,r+="  "+e.validate(f)+" ",f.baseId=k,f.createErrors=!0,r+="  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; }  ",e.compositeRule=f.compositeRule=C,R?(r+=" if ("+g+") {  ",f.schema=e.schema.then,f.schemaPath=e.schemaPath+".then",f.errSchemaPath=e.errSchemaPath+"/then",r+="  "+e.validate(f)+" ",f.baseId=k,r+=" "+x+" = "+g+"; ",R&&E?(T="ifClause"+n,r+=" var "+T+" = 'then'; "):T="'then'",r+=" } ",E&&(r+=" else { ")):r+=" if (!"+g+") { ",E&&(f.schema=e.schema.else,f.schemaPath=e.schemaPath+".else",f.errSchemaPath=e.errSchemaPath+"/else",r+="  "+e.validate(f)+" ",f.baseId=k,r+=" "+x+" = "+g+"; ",R&&E?(T="ifClause"+n,r+=" var "+T+" = 'else'; "):T="'else'",r+=" } "),r+=" if (!"+x+") {   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'if' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { failingKeyword: "+T+" } ",e.opts.messages!==!1&&(r+=` , message: 'should match "' + `+T+` + '" schema' `),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),r+=" }   ",l&&(r+=" else { ")}else l&&(r+=" if (true) { ");return r}});var kl=A((CE,Rl)=>{"use strict";Rl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d="errs__"+n,f=e.util.copy(e),g="";f.level++;var h="valid"+f.level,w="i"+n,R=f.dataLevel=e.dataLevel+1,E="data"+R,k=e.baseId;if(r+="var "+d+" = errors;var "+x+";",Array.isArray(o)){var T=e.schema.additionalItems;if(T===!1){r+=" "+x+" = "+m+".length <= "+o.length+"; ";var C=c;c=e.errSchemaPath+"/additionalItems",r+="  if (!"+x+") {   ";var B=B||[];B.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'additionalItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { limit: "+o.length+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have more than "+o.length+" items' "),e.opts.verbose&&(r+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var U=r;r=B.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+U+"]); ":r+=" validate.errors = ["+U+"]; return false; ":r+=" var err = "+U+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",c=C,l&&(g+="}",r+=" else { ")}var I=o;if(I){for(var Z,G=-1,M=I.length-1;G<M;)if(Z=I[G+=1],e.opts.strictKeywords?typeof Z=="object"&&Object.keys(Z).length>0||Z===!1:e.util.schemaHasRules(Z,e.RULES.all)){r+=" "+h+" = true; if ("+m+".length > "+G+") { ";var D=m+"["+G+"]";f.schema=Z,f.schemaPath=p+"["+G+"]",f.errSchemaPath=c+"/"+G,f.errorPath=e.util.getPathExpr(e.errorPath,G,e.opts.jsonPointers,!0),f.dataPathArr[R]=G;var O=e.validate(f);f.baseId=k,e.util.varOccurences(O,E)<2?r+=" "+e.util.varReplace(O,E,D)+" ":r+=" var "+E+" = "+D+"; "+O+" ",r+=" }  ",l&&(r+=" if ("+h+") { ",g+="}")}}if(typeof T=="object"&&(e.opts.strictKeywords?typeof T=="object"&&Object.keys(T).length>0||T===!1:e.util.schemaHasRules(T,e.RULES.all))){f.schema=T,f.schemaPath=e.schemaPath+".additionalItems",f.errSchemaPath=e.errSchemaPath+"/additionalItems",r+=" "+h+" = true; if ("+m+".length > "+o.length+") {  for (var "+w+" = "+o.length+"; "+w+" < "+m+".length; "+w+"++) { ",f.errorPath=e.util.getPathExpr(e.errorPath,w,e.opts.jsonPointers,!0);var D=m+"["+w+"]";f.dataPathArr[R]=w;var O=e.validate(f);f.baseId=k,e.util.varOccurences(O,E)<2?r+=" "+e.util.varReplace(O,E,D)+" ":r+=" var "+E+" = "+D+"; "+O+" ",l&&(r+=" if (!"+h+") break; "),r+=" } }  ",l&&(r+=" if ("+h+") { ",g+="}")}}else if(e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===!1:e.util.schemaHasRules(o,e.RULES.all)){f.schema=o,f.schemaPath=p,f.errSchemaPath=c,r+="  for (var "+w+" = 0; "+w+" < "+m+".length; "+w+"++) { ",f.errorPath=e.util.getPathExpr(e.errorPath,w,e.opts.jsonPointers,!0);var D=m+"["+w+"]";f.dataPathArr[R]=w;var O=e.validate(f);f.baseId=k,e.util.varOccurences(O,E)<2?r+=" "+e.util.varReplace(O,E,D)+" ":r+=" var "+E+" = "+D+"; "+O+" ",l&&(r+=" if (!"+h+") break; "),r+=" }"}return l&&(r+=" "+g+" if ("+d+" == errors) {"),r}});var ci=A((OE,Tl)=>{"use strict";Tl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,k,m="data"+(i||""),x=e.opts.$data&&o&&o.$data,d;x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o;var f=a=="maximum",g=f?"exclusiveMaximum":"exclusiveMinimum",h=e.schema[g],w=e.opts.$data&&h&&h.$data,R=f?"<":">",E=f?">":"<",k=void 0;if(!(x||typeof o=="number"||o===void 0))throw new Error(a+" must be number");if(!(w||h===void 0||typeof h=="number"||typeof h=="boolean"))throw new Error(g+" must be number or boolean");if(w){var T=e.util.getData(h.$data,i,e.dataPathArr),C="exclusive"+n,B="exclType"+n,U="exclIsNumber"+n,I="op"+n,Z="' + "+I+" + '";r+=" var schemaExcl"+n+" = "+T+"; ",T="schemaExcl"+n,r+=" var "+C+"; var "+B+" = typeof "+T+"; if ("+B+" != 'boolean' && "+B+" != 'undefined' && "+B+" != 'number') { ";var k=g,G=G||[];G.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(k||"_exclusiveLimit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: '"+g+" should be boolean' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var M=r;r=G.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+M+"]); ":r+=" validate.errors = ["+M+"]; return false; ":r+=" var err = "+M+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "),r+=" "+B+" == 'number' ? ( ("+C+" = "+d+" === undefined || "+T+" "+R+"= "+d+") ? "+m+" "+E+"= "+T+" : "+m+" "+E+" "+d+" ) : ( ("+C+" = "+T+" === true) ? "+m+" "+E+"= "+d+" : "+m+" "+E+" "+d+" ) || "+m+" !== "+m+") { var op"+n+" = "+C+" ? '"+R+"' : '"+R+"='; ",o===void 0&&(k=g,c=e.errSchemaPath+"/"+g,d=T,x=w)}else{var U=typeof h=="number",Z=R;if(U&&x){var I="'"+Z+"'";r+=" if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "),r+=" ( "+d+" === undefined || "+h+" "+R+"= "+d+" ? "+m+" "+E+"= "+h+" : "+m+" "+E+" "+d+" ) || "+m+" !== "+m+") { "}else{U&&o===void 0?(C=!0,k=g,c=e.errSchemaPath+"/"+g,d=h,E+="="):(U&&(d=Math[f?"min":"max"](h,o)),h===(U?d:!0)?(C=!0,k=g,c=e.errSchemaPath+"/"+g,E+="="):(C=!1,Z+="="));var I="'"+Z+"'";r+=" if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "),r+=" "+m+" "+E+" "+d+" || "+m+" !== "+m+") { "}}k=k||a;var G=G||[];G.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(k||"_limit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { comparison: "+I+", limit: "+d+", exclusive: "+C+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be "+Z+" ",x?r+="' + "+d:r+=""+d+"'"),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+o,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var M=r;return r=G.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+M+"]); ":r+=" validate.errors = ["+M+"]; return false; ":r+=" var err = "+M+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",l&&(r+=" else { "),r}});var li=A((jE,Al)=>{"use strict";Al.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,g,m="data"+(i||""),x=e.opts.$data&&o&&o.$data,d;if(x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o,!(x||typeof o=="number"))throw new Error(a+" must be number");var f=a=="maxItems"?">":"<";r+="if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "),r+=" "+m+".length "+f+" "+d+") { ";var g=a,h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(g||"_limitItems")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { limit: "+d+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have ",a=="maxItems"?r+="more":r+="fewer",r+=" than ",x?r+="' + "+d+" + '":r+=""+o,r+=" items' "),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+o,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var w=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r}});var pi=A((IE,Cl)=>{"use strict";Cl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,g,m="data"+(i||""),x=e.opts.$data&&o&&o.$data,d;if(x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o,!(x||typeof o=="number"))throw new Error(a+" must be number");var f=a=="maxLength"?">":"<";r+="if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "),e.opts.unicode===!1?r+=" "+m+".length ":r+=" ucs2length("+m+") ",r+=" "+f+" "+d+") { ";var g=a,h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(g||"_limitLength")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { limit: "+d+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT be ",a=="maxLength"?r+="longer":r+="shorter",r+=" than ",x?r+="' + "+d+" + '":r+=""+o,r+=" characters' "),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+o,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var w=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r}});var ui=A((DE,Ol)=>{"use strict";Ol.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,g,m="data"+(i||""),x=e.opts.$data&&o&&o.$data,d;if(x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o,!(x||typeof o=="number"))throw new Error(a+" must be number");var f=a=="maxProperties"?">":"<";r+="if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'number') || "),r+=" Object.keys("+m+").length "+f+" "+d+") { ";var g=a,h=h||[];h.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(g||"_limitProperties")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { limit: "+d+" } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have ",a=="maxProperties"?r+="more":r+="fewer",r+=" than ",x?r+="' + "+d+" + '":r+=""+o,r+=" properties' "),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+o,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var w=r;return r=h.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+w+"]); ":r+=" validate.errors = ["+w+"]; return false; ":r+=" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r}});var Il=A((NE,jl)=>{"use strict";jl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x=e.opts.$data&&o&&o.$data,d;if(x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o,!(x||typeof o=="number"))throw new Error(a+" must be number");r+="var division"+n+";if (",x&&(r+=" "+d+" !== undefined && ( typeof "+d+" != 'number' || "),r+=" (division"+n+" = "+m+" / "+d+", ",e.opts.multipleOfPrecision?r+=" Math.abs(Math.round(division"+n+") - division"+n+") > 1e-"+e.opts.multipleOfPrecision+" ":r+=" division"+n+" !== parseInt(division"+n+") ",r+=" ) ",x&&(r+="  )  "),r+=" ) {   ";var f=f||[];f.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'multipleOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { multipleOf: "+d+" } ",e.opts.messages!==!1&&(r+=" , message: 'should be multiple of ",x?r+="' + "+d:r+=""+d+"'"),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+o,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var g=r;return r=f.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+g+"]); ":r+=" validate.errors = ["+g+"]; return false; ":r+=" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r}});var Nl=A((FE,Dl)=>{"use strict";Dl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="errs__"+n,d=e.util.copy(e);d.level++;var f="valid"+d.level;if(e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===!1:e.util.schemaHasRules(o,e.RULES.all)){d.schema=o,d.schemaPath=p,d.errSchemaPath=c,r+=" var "+x+" = errors;  ";var g=e.compositeRule;e.compositeRule=d.compositeRule=!0,d.createErrors=!1;var h;d.opts.allErrors&&(h=d.opts.allErrors,d.opts.allErrors=!1),r+=" "+e.validate(d)+" ",d.createErrors=!0,h&&(d.opts.allErrors=h),e.compositeRule=d.compositeRule=g,r+=" if ("+f+") {   ";var w=w||[];w.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should NOT be valid' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var R=r;r=w.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+R+"]); ":r+=" validate.errors = ["+R+"]; return false; ":r+=" var err = "+R+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else {  errors = "+x+"; if (vErrors !== null) { if ("+x+") vErrors.length = "+x+"; else vErrors = null; } ",e.opts.allErrors&&(r+=" } ")}else r+="  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: {} ",e.opts.messages!==!1&&(r+=" , message: 'should NOT be valid' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",l&&(r+=" if (false) { ");return r}});var Ll=A((LE,Fl)=>{"use strict";Fl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d="errs__"+n,f=e.util.copy(e),g="";f.level++;var h="valid"+f.level,w=f.baseId,R="prevValid"+n,E="passingSchemas"+n;r+="var "+d+" = errors , "+R+" = false , "+x+" = false , "+E+" = null; ";var k=e.compositeRule;e.compositeRule=f.compositeRule=!0;var T=o;if(T)for(var C,B=-1,U=T.length-1;B<U;)C=T[B+=1],(e.opts.strictKeywords?typeof C=="object"&&Object.keys(C).length>0||C===!1:e.util.schemaHasRules(C,e.RULES.all))?(f.schema=C,f.schemaPath=p+"["+B+"]",f.errSchemaPath=c+"/"+B,r+="  "+e.validate(f)+" ",f.baseId=w):r+=" var "+h+" = true; ",B&&(r+=" if ("+h+" && "+R+") { "+x+" = false; "+E+" = ["+E+", "+B+"]; } else { ",g+="}"),r+=" if ("+h+") { "+x+" = "+R+" = true; "+E+" = "+B+"; }";return e.compositeRule=f.compositeRule=k,r+=""+g+"if (!"+x+") {   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'oneOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { passingSchemas: "+E+" } ",e.opts.messages!==!1&&(r+=" , message: 'should match exactly one schema in oneOf' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),r+="} else {  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; }",e.opts.allErrors&&(r+=" } "),r}});var ql=A(($E,$l)=>{"use strict";$l.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x=e.opts.$data&&o&&o.$data,d;x?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",d="schema"+n):d=o;var f=x?"(new RegExp("+d+"))":e.usePattern(o);r+="if ( ",x&&(r+=" ("+d+" !== undefined && typeof "+d+" != 'string') || "),r+=" !"+f+".test("+m+") ) {   ";var g=g||[];g.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'pattern' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { pattern:  ",x?r+=""+d:r+=""+e.util.toQuotedString(o),r+="  } ",e.opts.messages!==!1&&(r+=` , message: 'should match pattern "`,x?r+="' + "+d+" + '":r+=""+e.util.escapeQuotes(o),r+=`"' `),e.opts.verbose&&(r+=" , schema:  ",x?r+="validate.schema"+p:r+=""+e.util.toQuotedString(o),r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var h=r;return r=g.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+h+"]); ":r+=" validate.errors = ["+h+"]; return false; ":r+=" var err = "+h+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+="} ",l&&(r+=" else { "),r}});var zl=A((qE,Ul)=>{"use strict";Ul.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="errs__"+n,d=e.util.copy(e),f="";d.level++;var g="valid"+d.level,h="key"+n,w="idx"+n,R=d.dataLevel=e.dataLevel+1,E="data"+R,k="dataProperties"+n,T=Object.keys(o||{}).filter(L),C=e.schema.patternProperties||{},B=Object.keys(C).filter(L),U=e.schema.additionalProperties,I=T.length||B.length,Z=U===!1,G=typeof U=="object"&&Object.keys(U).length,M=e.opts.removeAdditional,D=Z||G||M,O=e.opts.ownProperties,H=e.baseId,le=e.schema.required;if(le&&!(e.opts.$data&&le.$data)&&le.length<e.opts.loopRequired)var F=e.util.toHash(le);function L(ht){return ht!=="__proto__"}if(r+="var "+x+" = errors;var "+g+" = true;",O&&(r+=" var "+k+" = undefined;"),D){if(O?r+=" "+k+" = "+k+" || Object.keys("+m+"); for (var "+w+"=0; "+w+"<"+k+".length; "+w+"++) { var "+h+" = "+k+"["+w+"]; ":r+=" for (var "+h+" in "+m+") { ",I){if(r+=" var isAdditional"+n+" = !(false ",T.length)if(T.length>8)r+=" || validate.schema"+p+".hasOwnProperty("+h+") ";else{var Q=T;if(Q)for(var K,re=-1,be=Q.length-1;re<be;)K=Q[re+=1],r+=" || "+h+" == "+e.util.toQuotedString(K)+" "}if(B.length){var Ne=B;if(Ne)for(var Y,pe=-1,Ce=Ne.length-1;pe<Ce;)Y=Ne[pe+=1],r+=" || "+e.usePattern(Y)+".test("+h+") "}r+=" ); if (isAdditional"+n+") { "}if(M=="all")r+=" delete "+m+"["+h+"]; ";else{var Fe=e.errorPath,_e="' + "+h+" + '";if(e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers)),Z)if(M)r+=" delete "+m+"["+h+"]; ";else{r+=" "+g+" = false; ";var jt=c;c=e.errSchemaPath+"/additionalProperties";var Oe=Oe||[];Oe.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'additionalProperties' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { additionalProperty: '"+_e+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is an invalid additional property":r+="should NOT have additional properties",r+="' "),e.opts.verbose&&(r+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var ke=r;r=Oe.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+ke+"]); ":r+=" validate.errors = ["+ke+"]; return false; ":r+=" var err = "+ke+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",c=jt,l&&(r+=" break; ")}else if(G)if(M=="failing"){r+=" var "+x+" = errors;  ";var ea=e.compositeRule;e.compositeRule=d.compositeRule=!0,d.schema=U,d.schemaPath=e.schemaPath+".additionalProperties",d.errSchemaPath=e.errSchemaPath+"/additionalProperties",d.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers);var Me=m+"["+h+"]";d.dataPathArr[R]=h;var fe=e.validate(d);d.baseId=H,e.util.varOccurences(fe,E)<2?r+=" "+e.util.varReplace(fe,E,Me)+" ":r+=" var "+E+" = "+Me+"; "+fe+" ",r+=" if (!"+g+") { errors = "+x+"; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete "+m+"["+h+"]; }  ",e.compositeRule=d.compositeRule=ea}else{d.schema=U,d.schemaPath=e.schemaPath+".additionalProperties",d.errSchemaPath=e.errSchemaPath+"/additionalProperties",d.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers);var Me=m+"["+h+"]";d.dataPathArr[R]=h;var fe=e.validate(d);d.baseId=H,e.util.varOccurences(fe,E)<2?r+=" "+e.util.varReplace(fe,E,Me)+" ":r+=" var "+E+" = "+Me+"; "+fe+" ",l&&(r+=" if (!"+g+") break; ")}e.errorPath=Fe}I&&(r+=" } "),r+=" }  ",l&&(r+=" if ("+g+") { ",f+="}")}var Bt=e.opts.useDefaults&&!e.compositeRule;if(T.length){var It=T;if(It)for(var K,ta=-1,aa=It.length-1;ta<aa;){K=It[ta+=1];var Ue=o[K];if(e.opts.strictKeywords?typeof Ue=="object"&&Object.keys(Ue).length>0||Ue===!1:e.util.schemaHasRules(Ue,e.RULES.all)){var nt=e.util.getProperty(K),Me=m+nt,Ge=Bt&&Ue.default!==void 0;d.schema=Ue,d.schemaPath=p+nt,d.errSchemaPath=c+"/"+e.util.escapeFragment(K),d.errorPath=e.util.getPath(e.errorPath,K,e.opts.jsonPointers),d.dataPathArr[R]=e.util.toQuotedString(K);var fe=e.validate(d);if(d.baseId=H,e.util.varOccurences(fe,E)<2){fe=e.util.varReplace(fe,E,Me);var Ve=Me}else{var Ve=E;r+=" var "+E+" = "+Me+"; "}if(Ge)r+=" "+fe+" ";else{if(F&&F[K]){r+=" if ( "+Ve+" === undefined ",O&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(K)+"') "),r+=") { "+g+" = false; ";var Fe=e.errorPath,jt=c,wt=e.util.escapeQuotes(K);e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(Fe,K,e.opts.jsonPointers)),c=e.errSchemaPath+"/required";var Oe=Oe||[];Oe.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { missingProperty: '"+wt+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+wt+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var ke=r;r=Oe.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+ke+"]); ":r+=" validate.errors = ["+ke+"]; return false; ":r+=" var err = "+ke+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",c=jt,e.errorPath=Fe,r+=" } else { "}else l?(r+=" if ( "+Ve+" === undefined ",O&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(K)+"') "),r+=") { "+g+" = true; } else { "):(r+=" if ("+Ve+" !== undefined ",O&&(r+=" &&   Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(K)+"') "),r+=" ) { ");r+=" "+fe+" } "}}l&&(r+=" if ("+g+") { ",f+="}")}}if(B.length){var tt=B;if(tt)for(var Y,Vr=-1,Tn=tt.length-1;Vr<Tn;){Y=tt[Vr+=1];var Ue=C[Y];if(e.opts.strictKeywords?typeof Ue=="object"&&Object.keys(Ue).length>0||Ue===!1:e.util.schemaHasRules(Ue,e.RULES.all)){d.schema=Ue,d.schemaPath=e.schemaPath+".patternProperties"+e.util.getProperty(Y),d.errSchemaPath=e.errSchemaPath+"/patternProperties/"+e.util.escapeFragment(Y),O?r+=" "+k+" = "+k+" || Object.keys("+m+"); for (var "+w+"=0; "+w+"<"+k+".length; "+w+"++) { var "+h+" = "+k+"["+w+"]; ":r+=" for (var "+h+" in "+m+") { ",r+=" if ("+e.usePattern(Y)+".test("+h+")) { ",d.errorPath=e.util.getPathExpr(e.errorPath,h,e.opts.jsonPointers);var Me=m+"["+h+"]";d.dataPathArr[R]=h;var fe=e.validate(d);d.baseId=H,e.util.varOccurences(fe,E)<2?r+=" "+e.util.varReplace(fe,E,Me)+" ":r+=" var "+E+" = "+Me+"; "+fe+" ",l&&(r+=" if (!"+g+") break; "),r+=" } ",l&&(r+=" else "+g+" = true; "),r+=" }  ",l&&(r+=" if ("+g+") { ",f+="}")}}}return l&&(r+=" "+f+" if ("+x+" == errors) {"),r}});var Bl=A((UE,Ml)=>{"use strict";Ml.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="errs__"+n,d=e.util.copy(e),f="";d.level++;var g="valid"+d.level;if(r+="var "+x+" = errors;",e.opts.strictKeywords?typeof o=="object"&&Object.keys(o).length>0||o===!1:e.util.schemaHasRules(o,e.RULES.all)){d.schema=o,d.schemaPath=p,d.errSchemaPath=c;var h="key"+n,w="idx"+n,R="i"+n,E="' + "+h+" + '",k=d.dataLevel=e.dataLevel+1,T="data"+k,C="dataProperties"+n,B=e.opts.ownProperties,U=e.baseId;B&&(r+=" var "+C+" = undefined; "),B?r+=" "+C+" = "+C+" || Object.keys("+m+"); for (var "+w+"=0; "+w+"<"+C+".length; "+w+"++) { var "+h+" = "+C+"["+w+"]; ":r+=" for (var "+h+" in "+m+") { ",r+=" var startErrs"+n+" = errors; ";var I=h,Z=e.compositeRule;e.compositeRule=d.compositeRule=!0;var G=e.validate(d);d.baseId=U,e.util.varOccurences(G,T)<2?r+=" "+e.util.varReplace(G,T,I)+" ":r+=" var "+T+" = "+I+"; "+G+" ",e.compositeRule=d.compositeRule=Z,r+=" if (!"+g+") { for (var "+R+"=startErrs"+n+"; "+R+"<errors; "+R+"++) { vErrors["+R+"].propertyName = "+h+"; }   var err =   ",e.createErrors!==!1?(r+=" { keyword: 'propertyNames' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { propertyName: '"+E+"' } ",e.opts.messages!==!1&&(r+=" , message: 'property name \\'"+E+"\\' is invalid' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; "),l&&(r+=" break; "),r+=" } }"}return l&&(r+=" "+f+" if ("+x+" == errors) {"),r}});var Vl=A((zE,Hl)=>{"use strict";Hl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d=e.opts.$data&&o&&o.$data,f;d?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",f="schema"+n):f=o;var g="schema"+n;if(!d)if(o.length<e.opts.loopRequired&&e.schema.properties&&Object.keys(e.schema.properties).length){var h=[],w=o;if(w)for(var R,E=-1,k=w.length-1;E<k;){R=w[E+=1];var T=e.schema.properties[R];T&&(e.opts.strictKeywords?typeof T=="object"&&Object.keys(T).length>0||T===!1:e.util.schemaHasRules(T,e.RULES.all))||(h[h.length]=R)}}else var h=o;if(d||h.length){var C=e.errorPath,B=d||h.length>=e.opts.loopRequired,U=e.opts.ownProperties;if(l)if(r+=" var missing"+n+"; ",B){d||(r+=" var "+g+" = validate.schema"+p+"; ");var I="i"+n,Z="schema"+n+"["+I+"]",G="' + "+Z+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(C,Z,e.opts.jsonPointers)),r+=" var "+x+" = true; ",d&&(r+=" if (schema"+n+" === undefined) "+x+" = true; else if (!Array.isArray(schema"+n+")) "+x+" = false; else {"),r+=" for (var "+I+" = 0; "+I+" < "+g+".length; "+I+"++) { "+x+" = "+m+"["+g+"["+I+"]] !== undefined ",U&&(r+=" &&   Object.prototype.hasOwnProperty.call("+m+", "+g+"["+I+"]) "),r+="; if (!"+x+") break; } ",d&&(r+="  }  "),r+="  if (!"+x+") {   ";var M=M||[];M.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { missingProperty: '"+G+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+G+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var D=r;r=M.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+D+"]); ":r+=" validate.errors = ["+D+"]; return false; ":r+=" var err = "+D+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else { "}else{r+=" if ( ";var O=h;if(O)for(var H,I=-1,le=O.length-1;I<le;){H=O[I+=1],I&&(r+=" || ");var F=e.util.getProperty(H),L=m+F;r+=" ( ( "+L+" === undefined ",U&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(H)+"') "),r+=") && (missing"+n+" = "+e.util.toQuotedString(e.opts.jsonPointers?H:F)+") ) "}r+=") {  ";var Z="missing"+n,G="' + "+Z+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(C,Z,!0):C+" + "+Z);var M=M||[];M.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { missingProperty: '"+G+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+G+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var D=r;r=M.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+D+"]); ":r+=" validate.errors = ["+D+"]; return false; ":r+=" var err = "+D+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } else { "}else if(B){d||(r+=" var "+g+" = validate.schema"+p+"; ");var I="i"+n,Z="schema"+n+"["+I+"]",G="' + "+Z+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(C,Z,e.opts.jsonPointers)),d&&(r+=" if ("+g+" && !Array.isArray("+g+")) {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { missingProperty: '"+G+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+G+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if ("+g+" !== undefined) { "),r+=" for (var "+I+" = 0; "+I+" < "+g+".length; "+I+"++) { if ("+m+"["+g+"["+I+"]] === undefined ",U&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", "+g+"["+I+"]) "),r+=") {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { missingProperty: '"+G+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+G+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ",d&&(r+="  }  ")}else{var Q=h;if(Q)for(var H,K=-1,re=Q.length-1;K<re;){H=Q[K+=1];var F=e.util.getProperty(H),G=e.util.escapeQuotes(H),L=m+F;e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(C,H,e.opts.jsonPointers)),r+=" if ( "+L+" === undefined ",U&&(r+=" || ! Object.prototype.hasOwnProperty.call("+m+", '"+e.util.escapeQuotes(H)+"') "),r+=") {  var err =   ",e.createErrors!==!1?(r+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { missingProperty: '"+G+"' } ",e.opts.messages!==!1&&(r+=" , message: '",e.opts._errorDataPathProperty?r+="is a required property":r+="should have required property \\'"+G+"\\'",r+="' "),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}e.errorPath=C}else l&&(r+=" if (true) {");return r}});var Gl=A((ME,Zl)=>{"use strict";Zl.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m="data"+(i||""),x="valid"+n,d=e.opts.$data&&o&&o.$data,f;if(d?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",f="schema"+n):f=o,(o||d)&&e.opts.uniqueItems!==!1){d&&(r+=" var "+x+"; if ("+f+" === false || "+f+" === undefined) "+x+" = true; else if (typeof "+f+" != 'boolean') "+x+" = false; else { "),r+=" var i = "+m+".length , "+x+" = true , j; if (i > 1) { ";var g=e.schema.items&&e.schema.items.type,h=Array.isArray(g);if(!g||g=="object"||g=="array"||h&&(g.indexOf("object")>=0||g.indexOf("array")>=0))r+=" outer: for (;i--;) { for (j = i; j--;) { if (equal("+m+"[i], "+m+"[j])) { "+x+" = false; break outer; } } } ";else{r+=" var itemIndices = {}, item; for (;i--;) { var item = "+m+"[i]; ";var w="checkDataType"+(h?"s":"");r+=" if ("+e.util[w](g,"item",e.opts.strictNumbers,!0)+") continue; ",h&&(r+=` if (typeof item == 'string') item = '"' + item; `),r+=" if (typeof itemIndices[item] == 'number') { "+x+" = false; j = itemIndices[item]; break; } itemIndices[item] = i; } "}r+=" } ",d&&(r+="  }  "),r+=" if (!"+x+") {   ";var R=R||[];R.push(r),r="",e.createErrors!==!1?(r+=" { keyword: 'uniqueItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { i: i, j: j } ",e.opts.messages!==!1&&(r+=" , message: 'should NOT have duplicate items (items ## ' + j + ' and ' + i + ' are identical)' "),e.opts.verbose&&(r+=" , schema:  ",d?r+="validate.schema"+p:r+=""+o,r+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+m+" "),r+=" } "):r+=" {} ";var E=r;r=R.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+E+"]); ":r+=" validate.errors = ["+E+"]; return false; ":r+=" var err = "+E+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",r+=" } ",l&&(r+=" else { ")}else l&&(r+=" if (true) { ");return r}});var Kl=A((BE,Jl)=>{"use strict";Jl.exports={$ref:ol(),allOf:ll(),anyOf:ul(),$comment:ml(),const:hl(),contains:xl(),dependencies:yl(),enum:_l(),format:El(),if:Pl(),items:kl(),maximum:ci(),minimum:ci(),maxItems:li(),minItems:li(),maxLength:pi(),minLength:pi(),maxProperties:ui(),minProperties:ui(),multipleOf:Il(),not:Nl(),oneOf:Ll(),pattern:ql(),properties:zl(),propertyNames:Bl(),required:Vl(),uniqueItems:Gl(),validate:ii()}});var Xl=A((HE,Wl)=>{"use strict";var Ql=Kl(),di=wa().toHash;Wl.exports=function(){var e=[{type:"number",rules:[{maximum:["exclusiveMaximum"]},{minimum:["exclusiveMinimum"]},"multipleOf","format"]},{type:"string",rules:["maxLength","minLength","pattern","format"]},{type:"array",rules:["maxItems","minItems","items","contains","uniqueItems"]},{type:"object",rules:["maxProperties","minProperties","required","dependencies","propertyNames",{properties:["additionalProperties","patternProperties"]}]},{rules:["$ref","const","enum","not","anyOf","oneOf","allOf","if"]}],a=["type","$comment"],s=["$schema","$id","id","$data","$async","title","description","default","definitions","examples","readOnly","writeOnly","contentMediaType","contentEncoding","additionalItems","then","else"],r=["number","integer","string","array","object","boolean","null"];return e.all=di(a),e.types=di(r),e.forEach(function(n){n.rules=n.rules.map(function(i){var o;if(typeof i=="object"){var p=Object.keys(i)[0];o=i[p],i=p,o.forEach(function(l){a.push(l),e.all[l]=!0})}a.push(i);var c=e.all[i]={keyword:i,code:Ql[i],implements:o};return c}),e.all.$comment={keyword:"$comment",code:Ql.$comment},n.type&&(e.types[n.type]=n)}),e.keywords=di(a.concat(s)),e.custom={},e}});var tp=A((VE,ep)=>{"use strict";var Yl=["multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","additionalItems","maxItems","minItems","uniqueItems","maxProperties","minProperties","required","additionalProperties","enum","format","const"];ep.exports=function(t,e){for(var a=0;a<e.length;a++){t=JSON.parse(JSON.stringify(t));var s=e[a].split("/"),r=t,n;for(n=1;n<s.length;n++)r=r[s[n]];for(n=0;n<Yl.length;n++){var i=Yl[n],o=r[i];o&&(r[i]={anyOf:[o,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]})}}return t}});var sp=A((ZE,rp)=>{"use strict";var cg=Rs().MissingRef;rp.exports=ap;function ap(t,e,a){var s=this;if(typeof this._opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");typeof e=="function"&&(a=e,e=void 0);var r=n(t).then(function(){var o=s._addSchema(t,void 0,e);return o.validate||i(o)});return a&&r.then(function(o){a(null,o)},a),r;function n(o){var p=o.$schema;return p&&!s.getSchema(p)?ap.call(s,{$ref:p},!0):Promise.resolve()}function i(o){try{return s._compile(o)}catch(c){if(c instanceof cg)return p(c);throw c}function p(c){var l=c.missingSchema;if(d(l))throw new Error("Schema "+l+" is loaded but "+c.missingRef+" cannot be resolved");var m=s._loadingSchemas[l];return m||(m=s._loadingSchemas[l]=s._opts.loadSchema(l),m.then(x,x)),m.then(function(f){if(!d(l))return n(f).then(function(){d(l)||s.addSchema(f,l,void 0,e)})}).then(function(){return i(o)});function x(){delete s._loadingSchemas[l]}function d(f){return s._refs[f]||s._schemas[f]}}}}});var ip=A((GE,np)=>{"use strict";np.exports=function(e,a,s){var r=" ",n=e.level,i=e.dataLevel,o=e.schema[a],p=e.schemaPath+e.util.getProperty(a),c=e.errSchemaPath+"/"+a,l=!e.opts.allErrors,m,x="data"+(i||""),d="valid"+n,f="errs__"+n,g=e.opts.$data&&o&&o.$data,h;g?(r+=" var schema"+n+" = "+e.util.getData(o.$data,i,e.dataPathArr)+"; ",h="schema"+n):h=o;var w=this,R="definition"+n,E=w.definition,k="",T,C,B,U,I;if(g&&E.$data){I="keywordValidate"+n;var Z=E.validateSchema;r+=" var "+R+" = RULES.custom['"+a+"'].definition; var "+I+" = "+R+".validate;"}else{if(U=e.useCustomRule(w,o,e.schema,e),!U)return;h="validate.schema"+p,I=U.code,T=E.compile,C=E.inline,B=E.macro}var G=I+".errors",M="i"+n,D="ruleErr"+n,O=E.async;if(O&&!e.async)throw new Error("async keyword in sync schema");if(C||B||(r+=""+G+" = null;"),r+="var "+f+" = errors;var "+d+";",g&&E.$data&&(k+="}",r+=" if ("+h+" === undefined) { "+d+" = true; } else { ",Z&&(k+="}",r+=" "+d+" = "+R+".validateSchema("+h+"); if ("+d+") { ")),C)E.statements?r+=" "+U.validate+" ":r+=" "+d+" = "+U.validate+"; ";else if(B){var H=e.util.copy(e),k="";H.level++;var le="valid"+H.level;H.schema=U.validate,H.schemaPath="";var F=e.compositeRule;e.compositeRule=H.compositeRule=!0;var L=e.validate(H).replace(/validate\.schema/g,I);e.compositeRule=H.compositeRule=F,r+=" "+L}else{var Q=Q||[];Q.push(r),r="",r+="  "+I+".call( ",e.opts.passContext?r+="this":r+="self",T||E.schema===!1?r+=" , "+x+" ":r+=" , "+h+" , "+x+" , validate.schema"+e.schemaPath+" ",r+=" , (dataPath || '')",e.errorPath!='""'&&(r+=" + "+e.errorPath);var K=i?"data"+(i-1||""):"parentData",re=i?e.dataPathArr[i]:"parentDataProperty";r+=" , "+K+" , "+re+" , rootData )  ";var be=r;r=Q.pop(),E.errors===!1?(r+=" "+d+" = ",O&&(r+="await "),r+=""+be+"; "):O?(G="customErrors"+n,r+=" var "+G+" = null; try { "+d+" = await "+be+"; } catch (e) { "+d+" = false; if (e instanceof ValidationError) "+G+" = e.errors; else throw e; } "):r+=" "+G+" = null; "+d+" = "+be+"; "}if(E.modifying&&(r+=" if ("+K+") "+x+" = "+K+"["+re+"];"),r+=""+k,E.valid)l&&(r+=" if (true) { ");else{r+=" if ( ",E.valid===void 0?(r+=" !",B?r+=""+le:r+=""+d):r+=" "+!E.valid+" ",r+=") { ",m=w.keyword;var Q=Q||[];Q.push(r),r="";var Q=Q||[];Q.push(r),r="",e.createErrors!==!1?(r+=" { keyword: '"+(m||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { keyword: '"+w.keyword+"' } ",e.opts.messages!==!1&&(r+=` , message: 'should pass "`+w.keyword+`" keyword validation' `),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+x+" "),r+=" } "):r+=" {} ";var Ne=r;r=Q.pop(),!e.compositeRule&&l?e.async?r+=" throw new ValidationError(["+Ne+"]); ":r+=" validate.errors = ["+Ne+"]; return false; ":r+=" var err = "+Ne+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";var Y=r;r=Q.pop(),C?E.errors?E.errors!="full"&&(r+="  for (var "+M+"="+f+"; "+M+"<errors; "+M+"++) { var "+D+" = vErrors["+M+"]; if ("+D+".dataPath === undefined) "+D+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+D+".schemaPath === undefined) { "+D+'.schemaPath = "'+c+'"; } ',e.opts.verbose&&(r+=" "+D+".schema = "+h+"; "+D+".data = "+x+"; "),r+=" } "):E.errors===!1?r+=" "+Y+" ":(r+=" if ("+f+" == errors) { "+Y+" } else {  for (var "+M+"="+f+"; "+M+"<errors; "+M+"++) { var "+D+" = vErrors["+M+"]; if ("+D+".dataPath === undefined) "+D+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+D+".schemaPath === undefined) { "+D+'.schemaPath = "'+c+'"; } ',e.opts.verbose&&(r+=" "+D+".schema = "+h+"; "+D+".data = "+x+"; "),r+=" } } "):B?(r+="   var err =   ",e.createErrors!==!1?(r+=" { keyword: '"+(m||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(c)+" , params: { keyword: '"+w.keyword+"' } ",e.opts.messages!==!1&&(r+=` , message: 'should pass "`+w.keyword+`" keyword validation' `),e.opts.verbose&&(r+=" , schema: validate.schema"+p+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+x+" "),r+=" } "):r+=" {} ",r+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&l&&(e.async?r+=" throw new ValidationError(vErrors); ":r+=" validate.errors = vErrors; return false; ")):E.errors===!1?r+=" "+Y+" ":(r+=" if (Array.isArray("+G+")) { if (vErrors === null) vErrors = "+G+"; else vErrors = vErrors.concat("+G+"); errors = vErrors.length;  for (var "+M+"="+f+"; "+M+"<errors; "+M+"++) { var "+D+" = vErrors["+M+"]; if ("+D+".dataPath === undefined) "+D+".dataPath = (dataPath || '') + "+e.errorPath+";  "+D+'.schemaPath = "'+c+'";  ',e.opts.verbose&&(r+=" "+D+".schema = "+h+"; "+D+".data = "+x+"; "),r+=" } } else { "+Y+" } "),r+=" } ",l&&(r+=" else { ")}return r}});var mi=A((JE,lg)=>{lg.exports={$schema:"http://json-schema.org/draft-07/schema#",$id:"http://json-schema.org/draft-07/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}});var lp=A((KE,cp)=>{"use strict";var op=mi();cp.exports={$id:"https://github.com/ajv-validator/ajv/blob/master/lib/definition_schema.js",definitions:{simpleTypes:op.definitions.simpleTypes},type:"object",dependencies:{schema:["validate"],$data:["validate"],statements:["inline"],valid:{not:{required:["macro"]}}},properties:{type:op.properties.type,schema:{type:"boolean"},statements:{type:"boolean"},dependencies:{type:"array",items:{type:"string"}},metaSchema:{type:"object"},modifying:{type:"boolean"},valid:{type:"boolean"},$data:{type:"boolean"},async:{type:"boolean"},errors:{anyOf:[{type:"boolean"},{const:"full"}]}}}});var up=A((QE,pp)=>{"use strict";var pg=/^[a-z_$][a-z0-9_$-]*$/i,ug=ip(),dg=lp();pp.exports={add:mg,get:fg,remove:hg,validate:fi};function mg(t,e){var a=this.RULES;if(a.keywords[t])throw new Error("Keyword "+t+" is already defined");if(!pg.test(t))throw new Error("Keyword "+t+" is not a valid identifier");if(e){this.validateKeyword(e,!0);var s=e.type;if(Array.isArray(s))for(var r=0;r<s.length;r++)i(t,s[r],e);else i(t,s,e);var n=e.metaSchema;n&&(e.$data&&this._opts.$data&&(n={anyOf:[n,{$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"}]}),e.validateSchema=this.compile(n,!0))}a.keywords[t]=a.all[t]=!0;function i(o,p,c){for(var l,m=0;m<a.length;m++){var x=a[m];if(x.type==p){l=x;break}}l||(l={type:p,rules:[]},a.push(l));var d={keyword:o,definition:c,custom:!0,code:ug,implements:c.implements};l.rules.push(d),a.custom[o]=d}return this}function fg(t){var e=this.RULES.custom[t];return e?e.definition:this.RULES.keywords[t]||!1}function hg(t){var e=this.RULES;delete e.keywords[t],delete e.all[t],delete e.custom[t];for(var a=0;a<e.length;a++)for(var s=e[a].rules,r=0;r<s.length;r++)if(s[r].keyword==t){s.splice(r,1);break}return this}function fi(t,e){fi.errors=null;var a=this._validateKeyword=this._validateKeyword||this.compile(dg,!0);if(a(t))return!0;if(fi.errors=a.errors,e)throw new Error("custom keyword definition is invalid: "+this.errorsText(a.errors));return!1}});var dp=A((WE,vg)=>{vg.exports={$schema:"http://json-schema.org/draft-07/schema#",$id:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",description:"Meta-schema for $data reference (JSON Schema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1}});var wp=A((XE,_p)=>{"use strict";var fp=Vc(),Ea=Ps(),xg=Gc(),hp=Yn(),gg=ni(),yg=nl(),bg=Xl(),vp=tp(),xp=wa();_p.exports=Te;Te.prototype.validate=wg;Te.prototype.compile=Eg;Te.prototype.addSchema=Sg;Te.prototype.addMetaSchema=Pg;Te.prototype.validateSchema=Rg;Te.prototype.getSchema=Tg;Te.prototype.removeSchema=Cg;Te.prototype.addFormat=$g;Te.prototype.errorsText=Lg;Te.prototype._addSchema=Og;Te.prototype._compile=jg;Te.prototype.compileAsync=sp();var Ds=up();Te.prototype.addKeyword=Ds.add;Te.prototype.getKeyword=Ds.get;Te.prototype.removeKeyword=Ds.remove;Te.prototype.validateKeyword=Ds.validate;var gp=Rs();Te.ValidationError=gp.Validation;Te.MissingRefError=gp.MissingRef;Te.$dataMetaSchema=vp;var Is="http://json-schema.org/draft-07/schema",mp=["removeAdditional","useDefaults","coerceTypes","strictDefaults"],_g=["/properties"];function Te(t){if(!(this instanceof Te))return new Te(t);t=this._opts=xp.copy(t)||{},Hg(this),this._schemas={},this._refs={},this._fragments={},this._formats=yg(t.format),this._cache=t.cache||new xg,this._loadingSchemas={},this._compilations=[],this.RULES=bg(),this._getId=Ig(t),t.loopRequired=t.loopRequired||1/0,t.errorDataPath=="property"&&(t._errorDataPathProperty=!0),t.serialize===void 0&&(t.serialize=gg),this._metaOpts=Bg(this),t.formats&&zg(this),t.keywords&&Mg(this),qg(this),typeof t.meta=="object"&&this.addMetaSchema(t.meta),t.nullable&&this.addKeyword("nullable",{metaSchema:{type:"boolean"}}),Ug(this)}function wg(t,e){var a;if(typeof t=="string"){if(a=this.getSchema(t),!a)throw new Error('no schema with key or ref "'+t+'"')}else{var s=this._addSchema(t);a=s.validate||this._compile(s)}var r=a(e);return a.$async!==!0&&(this.errors=a.errors),r}function Eg(t,e){var a=this._addSchema(t,void 0,e);return a.validate||this._compile(a)}function Sg(t,e,a,s){if(Array.isArray(t)){for(var r=0;r<t.length;r++)this.addSchema(t[r],void 0,a,s);return this}var n=this._getId(t);if(n!==void 0&&typeof n!="string")throw new Error("schema id must be string");return e=Ea.normalizeId(e||n),bp(this,e),this._schemas[e]=this._addSchema(t,a,s,!0),this}function Pg(t,e,a){return this.addSchema(t,e,a,!0),this}function Rg(t,e){var a=t.$schema;if(a!==void 0&&typeof a!="string")throw new Error("$schema must be a string");if(a=a||this._opts.defaultMeta||kg(this),!a)return this.logger.warn("meta-schema not available"),this.errors=null,!0;var s=this.validate(a,t);if(!s&&e){var r="schema is invalid: "+this.errorsText();if(this._opts.validateSchema=="log")this.logger.error(r);else throw new Error(r)}return s}function kg(t){var e=t._opts.meta;return t._opts.defaultMeta=typeof e=="object"?t._getId(e)||e:t.getSchema(Is)?Is:void 0,t._opts.defaultMeta}function Tg(t){var e=yp(this,t);switch(typeof e){case"object":return e.validate||this._compile(e);case"string":return this.getSchema(e);case"undefined":return Ag(this,t)}}function Ag(t,e){var a=Ea.schema.call(t,{schema:{}},e);if(a){var s=a.schema,r=a.root,n=a.baseId,i=fp.call(t,s,r,void 0,n);return t._fragments[e]=new hp({ref:e,fragment:!0,schema:s,root:r,baseId:n,validate:i}),i}}function yp(t,e){return e=Ea.normalizeId(e),t._schemas[e]||t._refs[e]||t._fragments[e]}function Cg(t){if(t instanceof RegExp)return js(this,this._schemas,t),js(this,this._refs,t),this;switch(typeof t){case"undefined":return js(this,this._schemas),js(this,this._refs),this._cache.clear(),this;case"string":var e=yp(this,t);return e&&this._cache.del(e.cacheKey),delete this._schemas[t],delete this._refs[t],this;case"object":var a=this._opts.serialize,s=a?a(t):t;this._cache.del(s);var r=this._getId(t);r&&(r=Ea.normalizeId(r),delete this._schemas[r],delete this._refs[r])}return this}function js(t,e,a){for(var s in e){var r=e[s];!r.meta&&(!a||a.test(s))&&(t._cache.del(r.cacheKey),delete e[s])}}function Og(t,e,a,s){if(typeof t!="object"&&typeof t!="boolean")throw new Error("schema should be object or boolean");var r=this._opts.serialize,n=r?r(t):t,i=this._cache.get(n);if(i)return i;s=s||this._opts.addUsedSchema!==!1;var o=Ea.normalizeId(this._getId(t));o&&s&&bp(this,o);var p=this._opts.validateSchema!==!1&&!e,c;p&&!(c=o&&o==Ea.normalizeId(t.$schema))&&this.validateSchema(t,!0);var l=Ea.ids.call(this,t),m=new hp({id:o,schema:t,localRefs:l,cacheKey:n,meta:a});return o[0]!="#"&&s&&(this._refs[o]=m),this._cache.put(n,m),p&&c&&this.validateSchema(t,!0),m}function jg(t,e){if(t.compiling)return t.validate=r,r.schema=t.schema,r.errors=null,r.root=e||r,t.schema.$async===!0&&(r.$async=!0),r;t.compiling=!0;var a;t.meta&&(a=this._opts,this._opts=this._metaOpts);var s;try{s=fp.call(this,t.schema,e,t.localRefs)}catch(n){throw delete t.validate,n}finally{t.compiling=!1,t.meta&&(this._opts=a)}return t.validate=s,t.refs=s.refs,t.refVal=s.refVal,t.root=s.root,s;function r(){var n=t.validate,i=n.apply(this,arguments);return r.errors=n.errors,i}}function Ig(t){switch(t.schemaId){case"auto":return Fg;case"id":return Dg;default:return Ng}}function Dg(t){return t.$id&&this.logger.warn("schema $id ignored",t.$id),t.id}function Ng(t){return t.id&&this.logger.warn("schema id ignored",t.id),t.$id}function Fg(t){if(t.$id&&t.id&&t.$id!=t.id)throw new Error("schema $id is different from id");return t.$id||t.id}function Lg(t,e){if(t=t||this.errors,!t)return"No errors";e=e||{};for(var a=e.separator===void 0?", ":e.separator,s=e.dataVar===void 0?"data":e.dataVar,r="",n=0;n<t.length;n++){var i=t[n];i&&(r+=s+i.dataPath+" "+i.message+a)}return r.slice(0,-a.length)}function $g(t,e){return typeof e=="string"&&(e=new RegExp(e)),this._formats[t]=e,this}function qg(t){var e;if(t._opts.$data&&(e=dp(),t.addMetaSchema(e,e.$id,!0)),t._opts.meta!==!1){var a=mi();t._opts.$data&&(a=vp(a,_g)),t.addMetaSchema(a,Is,!0),t._refs["http://json-schema.org/schema"]=Is}}function Ug(t){var e=t._opts.schemas;if(e)if(Array.isArray(e))t.addSchema(e);else for(var a in e)t.addSchema(e[a],a)}function zg(t){for(var e in t._opts.formats){var a=t._opts.formats[e];t.addFormat(e,a)}}function Mg(t){for(var e in t._opts.keywords){var a=t._opts.keywords[e];t.addKeyword(e,a)}}function bp(t,e){if(t._schemas[e]||t._refs[e])throw new Error('schema with key or id "'+e+'" already exists')}function Bg(t){for(var e=xp.copy(t._opts),a=0;a<mp.length;a++)delete e[mp[a]];return e}function Hg(t){var e=t._opts.logger;if(e===!1)t.logger={log:hi,warn:hi,error:hi};else{if(e===void 0&&(e=console),!(typeof e=="object"&&e.log&&e.warn&&e.error))throw new Error("logger must implement log, warn and error methods");t.logger=e}}function hi(){}});var xu=A((mk,vu)=>{var hu=require("stream").Stream,By=require("util");vu.exports=bt;function bt(){this.source=null,this.dataSize=0,this.maxDataSize=1024*1024,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}By.inherits(bt,hu);bt.create=function(t,e){var a=new this;e=e||{};for(var s in e)a[s]=e[s];a.source=t;var r=t.emit;return t.emit=function(){return a._handleEmit(arguments),r.apply(t,arguments)},t.on("error",function(){}),a.pauseStream&&t.pause(),a};Object.defineProperty(bt.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}});bt.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)};bt.prototype.resume=function(){this._released||this.release(),this.source.resume()};bt.prototype.pause=function(){this.source.pause()};bt.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(t){this.emit.apply(this,t)}.bind(this)),this._bufferedEvents=[]};bt.prototype.pipe=function(){var t=hu.prototype.pipe.apply(this,arguments);return this.resume(),t};bt.prototype._handleEmit=function(t){if(this._released){this.emit.apply(this,t);return}t[0]==="data"&&(this.dataSize+=t[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(t)};bt.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var t="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(t))}}});var _u=A((fk,bu)=>{var Hy=require("util"),yu=require("stream").Stream,gu=xu();bu.exports=Ae;function Ae(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2*1024*1024,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}Hy.inherits(Ae,yu);Ae.create=function(t){var e=new this;t=t||{};for(var a in t)e[a]=t[a];return e};Ae.isStreamLike=function(t){return typeof t!="function"&&typeof t!="string"&&typeof t!="boolean"&&typeof t!="number"&&!Buffer.isBuffer(t)};Ae.prototype.append=function(t){var e=Ae.isStreamLike(t);if(e){if(!(t instanceof gu)){var a=gu.create(t,{maxDataSize:1/0,pauseStream:this.pauseStreams});t.on("data",this._checkDataSize.bind(this)),t=a}this._handleErrors(t),this.pauseStreams&&t.pause()}return this._streams.push(t),this};Ae.prototype.pipe=function(t,e){return yu.prototype.pipe.call(this,t,e),this.resume(),t};Ae.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}};Ae.prototype._realGetNext=function(){var t=this._streams.shift();if(typeof t>"u"){this.end();return}if(typeof t!="function"){this._pipeNext(t);return}var e=t;e(function(a){var s=Ae.isStreamLike(a);s&&(a.on("data",this._checkDataSize.bind(this)),this._handleErrors(a)),this._pipeNext(a)}.bind(this))};Ae.prototype._pipeNext=function(t){this._currentStream=t;var e=Ae.isStreamLike(t);if(e){t.on("end",this._getNext.bind(this)),t.pipe(this,{end:!1});return}var a=t;this.write(a),this._getNext()};Ae.prototype._handleErrors=function(t){var e=this;t.on("error",function(a){e._emitError(a)})};Ae.prototype.write=function(t){this.emit("data",t)};Ae.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&typeof this._currentStream.pause=="function"&&this._currentStream.pause(),this.emit("pause"))};Ae.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&typeof this._currentStream.resume=="function"&&this._currentStream.resume(),this.emit("resume")};Ae.prototype.end=function(){this._reset(),this.emit("end")};Ae.prototype.destroy=function(){this._reset(),this.emit("close")};Ae.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null};Ae.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var t="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(t))}};Ae.prototype._updateDataSize=function(){this.dataSize=0;var t=this;this._streams.forEach(function(e){e.dataSize&&(t.dataSize+=e.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)};Ae.prototype._emitError=function(t){this._reset(),this.emit("error",t)}});var wu=A((hk,Vy)=>{Vy.exports={"application/1d-interleaved-parityfec":{source:"iana"},"application/3gpdash-qoe-report+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/3gpp-ims+xml":{source:"iana",compressible:!0},"application/3gpphal+json":{source:"iana",compressible:!0},"application/3gpphalforms+json":{source:"iana",compressible:!0},"application/a2l":{source:"iana"},"application/ace+cbor":{source:"iana"},"application/activemessage":{source:"iana"},"application/activity+json":{source:"iana",compressible:!0},"application/alto-costmap+json":{source:"iana",compressible:!0},"application/alto-costmapfilter+json":{source:"iana",compressible:!0},"application/alto-directory+json":{source:"iana",compressible:!0},"application/alto-endpointcost+json":{source:"iana",compressible:!0},"application/alto-endpointcostparams+json":{source:"iana",compressible:!0},"application/alto-endpointprop+json":{source:"iana",compressible:!0},"application/alto-endpointpropparams+json":{source:"iana",compressible:!0},"application/alto-error+json":{source:"iana",compressible:!0},"application/alto-networkmap+json":{source:"iana",compressible:!0},"application/alto-networkmapfilter+json":{source:"iana",compressible:!0},"application/alto-updatestreamcontrol+json":{source:"iana",compressible:!0},"application/alto-updatestreamparams+json":{source:"iana",compressible:!0},"application/aml":{source:"iana"},"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applefile":{source:"iana"},"application/applixware":{source:"apache",extensions:["aw"]},"application/at+jwt":{source:"iana"},"application/atf":{source:"iana"},"application/atfx":{source:"iana"},"application/atom+xml":{source:"iana",compressible:!0,extensions:["atom"]},"application/atomcat+xml":{source:"iana",compressible:!0,extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",compressible:!0,extensions:["atomdeleted"]},"application/atomicmail":{source:"iana"},"application/atomsvc+xml":{source:"iana",compressible:!0,extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",compressible:!0,extensions:["dwd"]},"application/atsc-dynamic-event-message":{source:"iana"},"application/atsc-held+xml":{source:"iana",compressible:!0,extensions:["held"]},"application/atsc-rdt+json":{source:"iana",compressible:!0},"application/atsc-rsat+xml":{source:"iana",compressible:!0,extensions:["rsat"]},"application/atxml":{source:"iana"},"application/auth-policy+xml":{source:"iana",compressible:!0},"application/bacnet-xdd+zip":{source:"iana",compressible:!1},"application/batch-smtp":{source:"iana"},"application/bdoc":{compressible:!1,extensions:["bdoc"]},"application/beep+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/calendar+json":{source:"iana",compressible:!0},"application/calendar+xml":{source:"iana",compressible:!0,extensions:["xcs"]},"application/call-completion":{source:"iana"},"application/cals-1840":{source:"iana"},"application/captive+json":{source:"iana",compressible:!0},"application/cbor":{source:"iana"},"application/cbor-seq":{source:"iana"},"application/cccex":{source:"iana"},"application/ccmp+xml":{source:"iana",compressible:!0},"application/ccxml+xml":{source:"iana",compressible:!0,extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",compressible:!0,extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cdni":{source:"iana"},"application/cea":{source:"iana"},"application/cea-2018+xml":{source:"iana",compressible:!0},"application/cellml+xml":{source:"iana",compressible:!0},"application/cfw":{source:"iana"},"application/city+json":{source:"iana",compressible:!0},"application/clr":{source:"iana"},"application/clue+xml":{source:"iana",compressible:!0},"application/clue_info+xml":{source:"iana",compressible:!0},"application/cms":{source:"iana"},"application/cnrp+xml":{source:"iana",compressible:!0},"application/coap-group+json":{source:"iana",compressible:!0},"application/coap-payload":{source:"iana"},"application/commonground":{source:"iana"},"application/conference-info+xml":{source:"iana",compressible:!0},"application/cose":{source:"iana"},"application/cose-key":{source:"iana"},"application/cose-key-set":{source:"iana"},"application/cpl+xml":{source:"iana",compressible:!0,extensions:["cpl"]},"application/csrattrs":{source:"iana"},"application/csta+xml":{source:"iana",compressible:!0},"application/cstadata+xml":{source:"iana",compressible:!0},"application/csvm+json":{source:"iana",compressible:!0},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/cwt":{source:"iana"},"application/cybercash":{source:"iana"},"application/dart":{compressible:!0},"application/dash+xml":{source:"iana",compressible:!0,extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",compressible:!0,extensions:["mpp"]},"application/dashdelta":{source:"iana"},"application/davmount+xml":{source:"iana",compressible:!0,extensions:["davmount"]},"application/dca-rft":{source:"iana"},"application/dcd":{source:"iana"},"application/dec-dx":{source:"iana"},"application/dialog-info+xml":{source:"iana",compressible:!0},"application/dicom":{source:"iana"},"application/dicom+json":{source:"iana",compressible:!0},"application/dicom+xml":{source:"iana",compressible:!0},"application/dii":{source:"iana"},"application/dit":{source:"iana"},"application/dns":{source:"iana"},"application/dns+json":{source:"iana",compressible:!0},"application/dns-message":{source:"iana"},"application/docbook+xml":{source:"apache",compressible:!0,extensions:["dbk"]},"application/dots+cbor":{source:"iana"},"application/dskpp+xml":{source:"iana",compressible:!0},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",compressible:!0,extensions:["xdssc"]},"application/dvcs":{source:"iana"},"application/ecmascript":{source:"iana",compressible:!0,extensions:["es","ecma"]},"application/edi-consent":{source:"iana"},"application/edi-x12":{source:"iana",compressible:!1},"application/edifact":{source:"iana",compressible:!1},"application/efi":{source:"iana"},"application/elm+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/elm+xml":{source:"iana",compressible:!0},"application/emergencycalldata.cap+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/emergencycalldata.comment+xml":{source:"iana",compressible:!0},"application/emergencycalldata.control+xml":{source:"iana",compressible:!0},"application/emergencycalldata.deviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.ecall.msd":{source:"iana"},"application/emergencycalldata.providerinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.serviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.subscriberinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.veds+xml":{source:"iana",compressible:!0},"application/emma+xml":{source:"iana",compressible:!0,extensions:["emma"]},"application/emotionml+xml":{source:"iana",compressible:!0,extensions:["emotionml"]},"application/encaprtp":{source:"iana"},"application/epp+xml":{source:"iana",compressible:!0},"application/epub+zip":{source:"iana",compressible:!1,extensions:["epub"]},"application/eshop":{source:"iana"},"application/exi":{source:"iana",extensions:["exi"]},"application/expect-ct-report+json":{source:"iana",compressible:!0},"application/express":{source:"iana",extensions:["exp"]},"application/fastinfoset":{source:"iana"},"application/fastsoap":{source:"iana"},"application/fdt+xml":{source:"iana",compressible:!0,extensions:["fdt"]},"application/fhir+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/fhir+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/fido.trusted-apps+json":{compressible:!0},"application/fits":{source:"iana"},"application/flexfec":{source:"iana"},"application/font-sfnt":{source:"iana"},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/font-woff":{source:"iana",compressible:!1},"application/framework-attributes+xml":{source:"iana",compressible:!0},"application/geo+json":{source:"iana",compressible:!0,extensions:["geojson"]},"application/geo+json-seq":{source:"iana"},"application/geopackage+sqlite3":{source:"iana"},"application/geoxacml+xml":{source:"iana",compressible:!0},"application/gltf-buffer":{source:"iana"},"application/gml+xml":{source:"iana",compressible:!0,extensions:["gml"]},"application/gpx+xml":{source:"apache",compressible:!0,extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",compressible:!1,extensions:["gz"]},"application/h224":{source:"iana"},"application/held+xml":{source:"iana",compressible:!0},"application/hjson":{extensions:["hjson"]},"application/http":{source:"iana"},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/ibe-key-request+xml":{source:"iana",compressible:!0},"application/ibe-pkg-reply+xml":{source:"iana",compressible:!0},"application/ibe-pp-data":{source:"iana"},"application/iges":{source:"iana"},"application/im-iscomposing+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/index":{source:"iana"},"application/index.cmd":{source:"iana"},"application/index.obj":{source:"iana"},"application/index.response":{source:"iana"},"application/index.vnd":{source:"iana"},"application/inkml+xml":{source:"iana",compressible:!0,extensions:["ink","inkml"]},"application/iotp":{source:"iana"},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/ipp":{source:"iana"},"application/isup":{source:"iana"},"application/its+xml":{source:"iana",compressible:!0,extensions:["its"]},"application/java-archive":{source:"apache",compressible:!1,extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",compressible:!1,extensions:["ser"]},"application/java-vm":{source:"apache",compressible:!1,extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["js","mjs"]},"application/jf2feed+json":{source:"iana",compressible:!0},"application/jose":{source:"iana"},"application/jose+json":{source:"iana",compressible:!0},"application/jrd+json":{source:"iana",compressible:!0},"application/jscalendar+json":{source:"iana",compressible:!0},"application/json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["json","map"]},"application/json-patch+json":{source:"iana",compressible:!0},"application/json-seq":{source:"iana"},"application/json5":{extensions:["json5"]},"application/jsonml+json":{source:"apache",compressible:!0,extensions:["jsonml"]},"application/jwk+json":{source:"iana",compressible:!0},"application/jwk-set+json":{source:"iana",compressible:!0},"application/jwt":{source:"iana"},"application/kpml-request+xml":{source:"iana",compressible:!0},"application/kpml-response+xml":{source:"iana",compressible:!0},"application/ld+json":{source:"iana",compressible:!0,extensions:["jsonld"]},"application/lgr+xml":{source:"iana",compressible:!0,extensions:["lgr"]},"application/link-format":{source:"iana"},"application/load-control+xml":{source:"iana",compressible:!0},"application/lost+xml":{source:"iana",compressible:!0,extensions:["lostxml"]},"application/lostsync+xml":{source:"iana",compressible:!0},"application/lpf+zip":{source:"iana",compressible:!1},"application/lxf":{source:"iana"},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/macwriteii":{source:"iana"},"application/mads+xml":{source:"iana",compressible:!0,extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",compressible:!0,extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",compressible:!0,extensions:["mathml"]},"application/mathml-content+xml":{source:"iana",compressible:!0},"application/mathml-presentation+xml":{source:"iana",compressible:!0},"application/mbms-associated-procedure-description+xml":{source:"iana",compressible:!0},"application/mbms-deregister+xml":{source:"iana",compressible:!0},"application/mbms-envelope+xml":{source:"iana",compressible:!0},"application/mbms-msk+xml":{source:"iana",compressible:!0},"application/mbms-msk-response+xml":{source:"iana",compressible:!0},"application/mbms-protection-description+xml":{source:"iana",compressible:!0},"application/mbms-reception-report+xml":{source:"iana",compressible:!0},"application/mbms-register+xml":{source:"iana",compressible:!0},"application/mbms-register-response+xml":{source:"iana",compressible:!0},"application/mbms-schedule+xml":{source:"iana",compressible:!0},"application/mbms-user-service-description+xml":{source:"iana",compressible:!0},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",compressible:!0,extensions:["mpf"]},"application/media_control+xml":{source:"iana",compressible:!0},"application/mediaservercontrol+xml":{source:"iana",compressible:!0,extensions:["mscml"]},"application/merge-patch+json":{source:"iana",compressible:!0},"application/metalink+xml":{source:"apache",compressible:!0,extensions:["metalink"]},"application/metalink4+xml":{source:"iana",compressible:!0,extensions:["meta4"]},"application/mets+xml":{source:"iana",compressible:!0,extensions:["mets"]},"application/mf4":{source:"iana"},"application/mikey":{source:"iana"},"application/mipc":{source:"iana"},"application/missing-blocks+cbor-seq":{source:"iana"},"application/mmt-aei+xml":{source:"iana",compressible:!0,extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",compressible:!0,extensions:["musd"]},"application/mods+xml":{source:"iana",compressible:!0,extensions:["mods"]},"application/moss-keys":{source:"iana"},"application/moss-signature":{source:"iana"},"application/mosskey-data":{source:"iana"},"application/mosskey-request":{source:"iana"},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/mpeg4-generic":{source:"iana"},"application/mpeg4-iod":{source:"iana"},"application/mpeg4-iod-xmt":{source:"iana"},"application/mrb-consumer+xml":{source:"iana",compressible:!0},"application/mrb-publish+xml":{source:"iana",compressible:!0},"application/msc-ivr+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msc-mixer+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msword":{source:"iana",compressible:!1,extensions:["doc","dot"]},"application/mud+json":{source:"iana",compressible:!0},"application/multipart-core":{source:"iana"},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/nasdata":{source:"iana"},"application/news-checkgroups":{source:"iana",charset:"US-ASCII"},"application/news-groupinfo":{source:"iana",charset:"US-ASCII"},"application/news-transmission":{source:"iana"},"application/nlsml+xml":{source:"iana",compressible:!0},"application/node":{source:"iana",extensions:["cjs"]},"application/nss":{source:"iana"},"application/oauth-authz-req+jwt":{source:"iana"},"application/oblivious-dns-message":{source:"iana"},"application/ocsp-request":{source:"iana"},"application/ocsp-response":{source:"iana"},"application/octet-stream":{source:"iana",compressible:!1,extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/odm+xml":{source:"iana",compressible:!0},"application/odx":{source:"iana"},"application/oebps-package+xml":{source:"iana",compressible:!0,extensions:["opf"]},"application/ogg":{source:"iana",compressible:!1,extensions:["ogx"]},"application/omdoc+xml":{source:"apache",compressible:!0,extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{source:"iana",compressible:!0},"application/oscore":{source:"iana"},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p21":{source:"iana"},"application/p21+zip":{source:"iana",compressible:!1},"application/p2p-overlay+xml":{source:"iana",compressible:!0,extensions:["relo"]},"application/parityfec":{source:"iana"},"application/passport":{source:"iana"},"application/patch-ops-error+xml":{source:"iana",compressible:!0,extensions:["xer"]},"application/pdf":{source:"iana",compressible:!1,extensions:["pdf"]},"application/pdx":{source:"iana"},"application/pem-certificate-chain":{source:"iana"},"application/pgp-encrypted":{source:"iana",compressible:!1,extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pidf+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pidf-diff+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs12":{source:"iana"},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkcs8-encrypted":{source:"iana"},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",compressible:!0,extensions:["pls"]},"application/poc-settings+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/postscript":{source:"iana",compressible:!0,extensions:["ai","eps","ps"]},"application/ppsp-tracker+json":{source:"iana",compressible:!0},"application/problem+json":{source:"iana",compressible:!0},"application/problem+xml":{source:"iana",compressible:!0},"application/provenance+xml":{source:"iana",compressible:!0,extensions:["provx"]},"application/prs.alvestrand.titrax-sheet":{source:"iana"},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/prs.cyn":{source:"iana",charset:"7-BIT"},"application/prs.hpub+zip":{source:"iana",compressible:!1},"application/prs.nprend":{source:"iana"},"application/prs.plucker":{source:"iana"},"application/prs.rdf-xml-crypt":{source:"iana"},"application/prs.xsf+xml":{source:"iana",compressible:!0},"application/pskc+xml":{source:"iana",compressible:!0,extensions:["pskcxml"]},"application/pvd+json":{source:"iana",compressible:!0},"application/qsig":{source:"iana"},"application/raml+yaml":{compressible:!0,extensions:["raml"]},"application/raptorfec":{source:"iana"},"application/rdap+json":{source:"iana",compressible:!0},"application/rdf+xml":{source:"iana",compressible:!0,extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",compressible:!0,extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/remote-printing":{source:"iana"},"application/reputon+json":{source:"iana",compressible:!0},"application/resource-lists+xml":{source:"iana",compressible:!0,extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",compressible:!0,extensions:["rld"]},"application/rfc+xml":{source:"iana",compressible:!0},"application/riscos":{source:"iana"},"application/rlmi+xml":{source:"iana",compressible:!0},"application/rls-services+xml":{source:"iana",compressible:!0,extensions:["rs"]},"application/route-apd+xml":{source:"iana",compressible:!0,extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",compressible:!0,extensions:["sls"]},"application/route-usd+xml":{source:"iana",compressible:!0,extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-publication":{source:"iana"},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rpki-updown":{source:"iana"},"application/rsd+xml":{source:"apache",compressible:!0,extensions:["rsd"]},"application/rss+xml":{source:"apache",compressible:!0,extensions:["rss"]},"application/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"application/rtploopback":{source:"iana"},"application/rtx":{source:"iana"},"application/samlassertion+xml":{source:"iana",compressible:!0},"application/samlmetadata+xml":{source:"iana",compressible:!0},"application/sarif+json":{source:"iana",compressible:!0},"application/sarif-external-properties+json":{source:"iana",compressible:!0},"application/sbe":{source:"iana"},"application/sbml+xml":{source:"iana",compressible:!0,extensions:["sbml"]},"application/scaip+xml":{source:"iana",compressible:!0},"application/scim+json":{source:"iana",compressible:!0},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/secevent+jwt":{source:"iana"},"application/senml+cbor":{source:"iana"},"application/senml+json":{source:"iana",compressible:!0},"application/senml+xml":{source:"iana",compressible:!0,extensions:["senmlx"]},"application/senml-etch+cbor":{source:"iana"},"application/senml-etch+json":{source:"iana",compressible:!0},"application/senml-exi":{source:"iana"},"application/sensml+cbor":{source:"iana"},"application/sensml+json":{source:"iana",compressible:!0},"application/sensml+xml":{source:"iana",compressible:!0,extensions:["sensmlx"]},"application/sensml-exi":{source:"iana"},"application/sep+xml":{source:"iana",compressible:!0},"application/sep-exi":{source:"iana"},"application/session-info":{source:"iana"},"application/set-payment":{source:"iana"},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration":{source:"iana"},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/sgml":{source:"iana"},"application/sgml-open-catalog":{source:"iana"},"application/shf+xml":{source:"iana",compressible:!0,extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/simple-filter+xml":{source:"iana",compressible:!0},"application/simple-message-summary":{source:"iana"},"application/simplesymbolcontainer":{source:"iana"},"application/sipc":{source:"iana"},"application/slate":{source:"iana"},"application/smil":{source:"iana"},"application/smil+xml":{source:"iana",compressible:!0,extensions:["smi","smil"]},"application/smpte336m":{source:"iana"},"application/soap+fastinfoset":{source:"iana"},"application/soap+xml":{source:"iana",compressible:!0},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",compressible:!0,extensions:["srx"]},"application/spdx+json":{source:"iana",compressible:!0},"application/spirits-event+xml":{source:"iana",compressible:!0},"application/sql":{source:"iana"},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",compressible:!0,extensions:["grxml"]},"application/sru+xml":{source:"iana",compressible:!0,extensions:["sru"]},"application/ssdl+xml":{source:"apache",compressible:!0,extensions:["ssdl"]},"application/ssml+xml":{source:"iana",compressible:!0,extensions:["ssml"]},"application/stix+json":{source:"iana",compressible:!0},"application/swid+xml":{source:"iana",compressible:!0,extensions:["swidtag"]},"application/tamp-apex-update":{source:"iana"},"application/tamp-apex-update-confirm":{source:"iana"},"application/tamp-community-update":{source:"iana"},"application/tamp-community-update-confirm":{source:"iana"},"application/tamp-error":{source:"iana"},"application/tamp-sequence-adjust":{source:"iana"},"application/tamp-sequence-adjust-confirm":{source:"iana"},"application/tamp-status-query":{source:"iana"},"application/tamp-status-response":{source:"iana"},"application/tamp-update":{source:"iana"},"application/tamp-update-confirm":{source:"iana"},"application/tar":{compressible:!0},"application/taxii+json":{source:"iana",compressible:!0},"application/td+json":{source:"iana",compressible:!0},"application/tei+xml":{source:"iana",compressible:!0,extensions:["tei","teicorpus"]},"application/tetra_isi":{source:"iana"},"application/thraud+xml":{source:"iana",compressible:!0,extensions:["tfi"]},"application/timestamp-query":{source:"iana"},"application/timestamp-reply":{source:"iana"},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/tlsrpt+gzip":{source:"iana"},"application/tlsrpt+json":{source:"iana",compressible:!0},"application/tnauthlist":{source:"iana"},"application/token-introspection+jwt":{source:"iana"},"application/toml":{compressible:!0,extensions:["toml"]},"application/trickle-ice-sdpfrag":{source:"iana"},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",compressible:!0,extensions:["ttml"]},"application/tve-trigger":{source:"iana"},"application/tzif":{source:"iana"},"application/tzif-leap":{source:"iana"},"application/ubjson":{compressible:!1,extensions:["ubj"]},"application/ulpfec":{source:"iana"},"application/urc-grpsheet+xml":{source:"iana",compressible:!0},"application/urc-ressheet+xml":{source:"iana",compressible:!0,extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",compressible:!0,extensions:["td"]},"application/urc-uisocketdesc+xml":{source:"iana",compressible:!0},"application/vcard+json":{source:"iana",compressible:!0},"application/vcard+xml":{source:"iana",compressible:!0},"application/vemmi":{source:"iana"},"application/vividence.scriptfile":{source:"apache"},"application/vnd.1000minds.decision-model+xml":{source:"iana",compressible:!0,extensions:["1km"]},"application/vnd.3gpp-prose+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-prose-pc3ch+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-v2x-local-service-information":{source:"iana"},"application/vnd.3gpp.5gnas":{source:"iana"},"application/vnd.3gpp.access-transfer-events+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.bsf+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gmop+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gtpc":{source:"iana"},"application/vnd.3gpp.interworking-data":{source:"iana"},"application/vnd.3gpp.lpp":{source:"iana"},"application/vnd.3gpp.mc-signalling-ear":{source:"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-payload":{source:"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-signalling":{source:"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-floor-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-signed+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-init-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-transmission-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mid-call+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ngap":{source:"iana"},"application/vnd.3gpp.pfcp":{source:"iana"},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp.s1ap":{source:"iana"},"application/vnd.3gpp.sms":{source:"iana"},"application/vnd.3gpp.sms+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-ext+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.state-and-event-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ussd+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.bcmcsinfo+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.sms":{source:"iana"},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3lightssoftware.imagescal":{source:"iana"},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",compressible:!1,extensions:["air"]},"application/vnd.adobe.flash.movie":{source:"iana"},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{source:"iana"},"application/vnd.adobe.xdp+xml":{source:"iana",compressible:!0,extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.aether.imp":{source:"iana"},"application/vnd.afpc.afplinedata":{source:"iana"},"application/vnd.afpc.afplinedata-pagedef":{source:"iana"},"application/vnd.afpc.cmoca-cmresource":{source:"iana"},"application/vnd.afpc.foca-charset":{source:"iana"},"application/vnd.afpc.foca-codedfont":{source:"iana"},"application/vnd.afpc.foca-codepage":{source:"iana"},"application/vnd.afpc.modca":{source:"iana"},"application/vnd.afpc.modca-cmtable":{source:"iana"},"application/vnd.afpc.modca-formdef":{source:"iana"},"application/vnd.afpc.modca-mediummap":{source:"iana"},"application/vnd.afpc.modca-objectcontainer":{source:"iana"},"application/vnd.afpc.modca-overlay":{source:"iana"},"application/vnd.afpc.modca-pagesegment":{source:"iana"},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ah-barcode":{source:"iana"},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amadeus+json":{source:"iana",compressible:!0},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.amazon.mobi8-ebook":{source:"iana"},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.amundsen.maze+xml":{source:"iana",compressible:!0},"application/vnd.android.ota":{source:"iana"},"application/vnd.android.package-archive":{source:"apache",compressible:!1,extensions:["apk"]},"application/vnd.anki":{source:"iana"},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apache.arrow.file":{source:"iana"},"application/vnd.apache.arrow.stream":{source:"iana"},"application/vnd.apache.thrift.binary":{source:"iana"},"application/vnd.apache.thrift.compact":{source:"iana"},"application/vnd.apache.thrift.json":{source:"iana"},"application/vnd.api+json":{source:"iana",compressible:!0},"application/vnd.aplextor.warrp+json":{source:"iana",compressible:!0},"application/vnd.apothekende.reservation+json":{source:"iana",compressible:!0},"application/vnd.apple.installer+xml":{source:"iana",compressible:!0,extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.apple.pkpass":{compressible:!1,extensions:["pkpass"]},"application/vnd.arastra.swi":{source:"iana"},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.artisan+json":{source:"iana",compressible:!0},"application/vnd.artsquare":{source:"iana"},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.autopackage":{source:"iana"},"application/vnd.avalon+json":{source:"iana",compressible:!0},"application/vnd.avistar+xml":{source:"iana",compressible:!0},"application/vnd.balsamiq.bmml+xml":{source:"iana",compressible:!0,extensions:["bmml"]},"application/vnd.balsamiq.bmpr":{source:"iana"},"application/vnd.banana-accounting":{source:"iana"},"application/vnd.bbf.usp.error":{source:"iana"},"application/vnd.bbf.usp.msg":{source:"iana"},"application/vnd.bbf.usp.msg+json":{source:"iana",compressible:!0},"application/vnd.bekitzur-stech+json":{source:"iana",compressible:!0},"application/vnd.bint.med-content":{source:"iana"},"application/vnd.biopax.rdf+xml":{source:"iana",compressible:!0},"application/vnd.blink-idb-value-wrapper":{source:"iana"},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bluetooth.ep.oob":{source:"iana"},"application/vnd.bluetooth.le.oob":{source:"iana"},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.bpf":{source:"iana"},"application/vnd.bpf3":{source:"iana"},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.byu.uapi+json":{source:"iana",compressible:!0},"application/vnd.cab-jscript":{source:"iana"},"application/vnd.canon-cpdl":{source:"iana"},"application/vnd.canon-lips":{source:"iana"},"application/vnd.capasystems-pg+json":{source:"iana",compressible:!0},"application/vnd.cendio.thinlinc.clientconf":{source:"iana"},"application/vnd.century-systems.tcp_stream":{source:"iana"},"application/vnd.chemdraw+xml":{source:"iana",compressible:!0,extensions:["cdxml"]},"application/vnd.chess-pgn":{source:"iana"},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.ciedi":{source:"iana"},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.cirpack.isdn-ext":{source:"iana"},"application/vnd.citationstyles.style+xml":{source:"iana",compressible:!0,extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.coffeescript":{source:"iana"},"application/vnd.collabio.xodocuments.document":{source:"iana"},"application/vnd.collabio.xodocuments.document-template":{source:"iana"},"application/vnd.collabio.xodocuments.presentation":{source:"iana"},"application/vnd.collabio.xodocuments.presentation-template":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{source:"iana"},"application/vnd.collection+json":{source:"iana",compressible:!0},"application/vnd.collection.doc+json":{source:"iana",compressible:!0},"application/vnd.collection.next+json":{source:"iana",compressible:!0},"application/vnd.comicbook+zip":{source:"iana",compressible:!1},"application/vnd.comicbook-rar":{source:"iana"},"application/vnd.commerce-battelle":{source:"iana"},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.coreos.ignition+json":{source:"iana",compressible:!0},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",compressible:!0,extensions:["wbs"]},"application/vnd.cryptii.pipe+json":{source:"iana",compressible:!0},"application/vnd.crypto-shade-file":{source:"iana"},"application/vnd.cryptomator.encrypted":{source:"iana"},"application/vnd.cryptomator.vault":{source:"iana"},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.ctct.ws+xml":{source:"iana",compressible:!0},"application/vnd.cups-pdf":{source:"iana"},"application/vnd.cups-postscript":{source:"iana"},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.cups-raster":{source:"iana"},"application/vnd.cups-raw":{source:"iana"},"application/vnd.curl":{source:"iana"},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.cyan.dean.root+xml":{source:"iana",compressible:!0},"application/vnd.cybank":{source:"iana"},"application/vnd.cyclonedx+json":{source:"iana",compressible:!0},"application/vnd.cyclonedx+xml":{source:"iana",compressible:!0},"application/vnd.d2l.coursepackage1p0+zip":{source:"iana",compressible:!1},"application/vnd.d3m-dataset":{source:"iana"},"application/vnd.d3m-problem":{source:"iana"},"application/vnd.dart":{source:"iana",compressible:!0,extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.datapackage+json":{source:"iana",compressible:!0},"application/vnd.dataresource+json":{source:"iana",compressible:!0},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.debian.binary-package":{source:"iana"},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",compressible:!0,extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.desmume.movie":{source:"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{source:"iana"},"application/vnd.dm.delegation+xml":{source:"iana",compressible:!0},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.document+json":{source:"iana",compressible:!0},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dolby.mobile.1":{source:"iana"},"application/vnd.dolby.mobile.2":{source:"iana"},"application/vnd.doremir.scorecloud-binary-document":{source:"iana"},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.drive+json":{source:"iana",compressible:!0},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dtg.local":{source:"iana"},"application/vnd.dtg.local.flash":{source:"iana"},"application/vnd.dtg.local.html":{source:"iana"},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.dvbisl+xml":{source:"iana",compressible:!0},"application/vnd.dvb.dvbj":{source:"iana"},"application/vnd.dvb.esgcontainer":{source:"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess2":{source:"iana"},"application/vnd.dvb.ipdcesgpdd":{source:"iana"},"application/vnd.dvb.ipdcroaming":{source:"iana"},"application/vnd.dvb.iptv.alfec-base":{source:"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{source:"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-container+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-generic+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-msglist+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-request+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-response+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-init+xml":{source:"iana",compressible:!0},"application/vnd.dvb.pfr":{source:"iana"},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dxr":{source:"iana"},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.dzr":{source:"iana"},"application/vnd.easykaraoke.cdgdownload":{source:"iana"},"application/vnd.ecdis-update":{source:"iana"},"application/vnd.ecip.rlp":{source:"iana"},"application/vnd.eclipse.ditto+json":{source:"iana",compressible:!0},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.ecowin.filerequest":{source:"iana"},"application/vnd.ecowin.fileupdate":{source:"iana"},"application/vnd.ecowin.series":{source:"iana"},"application/vnd.ecowin.seriesrequest":{source:"iana"},"application/vnd.ecowin.seriesupdate":{source:"iana"},"application/vnd.efi.img":{source:"iana"},"application/vnd.efi.iso":{source:"iana"},"application/vnd.emclient.accessrequest+xml":{source:"iana",compressible:!0},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.enphase.envoy":{source:"iana"},"application/vnd.eprints.data+xml":{source:"iana",compressible:!0},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.ericsson.quickcall":{source:"iana"},"application/vnd.espass-espass+zip":{source:"iana",compressible:!1},"application/vnd.eszigno3+xml":{source:"iana",compressible:!0,extensions:["es3","et3"]},"application/vnd.etsi.aoc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.asic-e+zip":{source:"iana",compressible:!1},"application/vnd.etsi.asic-s+zip":{source:"iana",compressible:!1},"application/vnd.etsi.cug+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvcommand+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-bc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-cod+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-npvr+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvservice+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsync+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvueprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mcid+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mheg5":{source:"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{source:"iana",compressible:!0},"application/vnd.etsi.pstn+xml":{source:"iana",compressible:!0},"application/vnd.etsi.sci+xml":{source:"iana",compressible:!0},"application/vnd.etsi.simservs+xml":{source:"iana",compressible:!0},"application/vnd.etsi.timestamp-token":{source:"iana"},"application/vnd.etsi.tsl+xml":{source:"iana",compressible:!0},"application/vnd.etsi.tsl.der":{source:"iana"},"application/vnd.eu.kasparian.car+json":{source:"iana",compressible:!0},"application/vnd.eudora.data":{source:"iana"},"application/vnd.evolv.ecig.profile":{source:"iana"},"application/vnd.evolv.ecig.settings":{source:"iana"},"application/vnd.evolv.ecig.theme":{source:"iana"},"application/vnd.exstream-empower+zip":{source:"iana",compressible:!1},"application/vnd.exstream-package":{source:"iana"},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.f-secure.mobile":{source:"iana"},"application/vnd.familysearch.gedcom+zip":{source:"iana",compressible:!1},"application/vnd.fastcopy-disk-image":{source:"iana"},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.ffsns":{source:"iana"},"application/vnd.ficlab.flb+zip":{source:"iana",compressible:!1},"application/vnd.filmit.zfc":{source:"iana"},"application/vnd.fints":{source:"iana"},"application/vnd.firemonkeys.cloudcell":{source:"iana"},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.font-fontforge-sfd":{source:"iana"},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujifilm.fb.docuworks":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.container":{source:"iana"},"application/vnd.fujifilm.fb.jfi+xml":{source:"iana",compressible:!0},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.art-ex":{source:"iana"},"application/vnd.fujixerox.art4":{source:"iana"},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fujixerox.docuworks.container":{source:"iana"},"application/vnd.fujixerox.hbpl":{source:"iana"},"application/vnd.fut-misnet":{source:"iana"},"application/vnd.futoin+cbor":{source:"iana"},"application/vnd.futoin+json":{source:"iana",compressible:!0},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.gentics.grd+json":{source:"iana",compressible:!0},"application/vnd.geo+json":{source:"iana",compressible:!0},"application/vnd.geocube+xml":{source:"iana",compressible:!0},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.slides":{source:"iana"},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gerber":{source:"iana"},"application/vnd.globalplatform.card-content-mgt":{source:"iana"},"application/vnd.globalplatform.card-content-mgt-response":{source:"iana"},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-apps.document":{compressible:!1,extensions:["gdoc"]},"application/vnd.google-apps.presentation":{compressible:!1,extensions:["gslides"]},"application/vnd.google-apps.spreadsheet":{compressible:!1,extensions:["gsheet"]},"application/vnd.google-earth.kml+xml":{source:"iana",compressible:!0,extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",compressible:!1,extensions:["kmz"]},"application/vnd.gov.sk.e-form+xml":{source:"iana",compressible:!0},"application/vnd.gov.sk.e-form+zip":{source:"iana",compressible:!1},"application/vnd.gov.sk.xmldatacontainer+xml":{source:"iana",compressible:!0},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.gridmp":{source:"iana"},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+json":{source:"iana",compressible:!0},"application/vnd.hal+xml":{source:"iana",compressible:!0,extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",compressible:!0,extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hc+json":{source:"iana",compressible:!0},"application/vnd.hcl-bireports":{source:"iana"},"application/vnd.hdt":{source:"iana"},"application/vnd.heroku+json":{source:"iana",compressible:!0},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hl7cda+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hl7v2+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.httphone":{source:"iana"},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.hyper+json":{source:"iana",compressible:!0},"application/vnd.hyper-item+json":{source:"iana",compressible:!0},"application/vnd.hyperdrive+json":{source:"iana",compressible:!0},"application/vnd.hzn-3d-crossword":{source:"iana"},"application/vnd.ibm.afplinedata":{source:"iana"},"application/vnd.ibm.electronic-media":{source:"iana"},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.ieee.1905":{source:"iana"},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.imagemeter.folder+zip":{source:"iana",compressible:!1},"application/vnd.imagemeter.image+zip":{source:"iana",compressible:!1},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.ims.imsccv1p1":{source:"iana"},"application/vnd.ims.imsccv1p2":{source:"iana"},"application/vnd.ims.imsccv1p3":{source:"iana"},"application/vnd.ims.lis.v2.result+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy.id+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings.simple+json":{source:"iana",compressible:!0},"application/vnd.informedcontrol.rms+xml":{source:"iana",compressible:!0},"application/vnd.informix-visionary":{source:"iana"},"application/vnd.infotech.project":{source:"iana"},"application/vnd.infotech.project+xml":{source:"iana",compressible:!0},"application/vnd.innopath.wamp.notification":{source:"iana"},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intertrust.digibox":{source:"iana"},"application/vnd.intertrust.nncp":{source:"iana"},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.conceptitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.knowledgeitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsmessage+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.packageitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.planningitem+xml":{source:"iana",compressible:!0},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",compressible:!0,extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.iso11783-10+zip":{source:"iana",compressible:!1},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.japannet-directory-service":{source:"iana"},"application/vnd.japannet-jpnstore-wakeup":{source:"iana"},"application/vnd.japannet-payment-wakeup":{source:"iana"},"application/vnd.japannet-registration":{source:"iana"},"application/vnd.japannet-registration-wakeup":{source:"iana"},"application/vnd.japannet-setstore-wakeup":{source:"iana"},"application/vnd.japannet-verification":{source:"iana"},"application/vnd.japannet-verification-wakeup":{source:"iana"},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.jsk.isdn-ngn":{source:"iana"},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las":{source:"iana"},"application/vnd.las.las+json":{source:"iana",compressible:!0},"application/vnd.las.las+xml":{source:"iana",compressible:!0,extensions:["lasxml"]},"application/vnd.laszip":{source:"iana"},"application/vnd.leap+json":{source:"iana",compressible:!0},"application/vnd.liberty-request+xml":{source:"iana",compressible:!0},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",compressible:!0,extensions:["lbe"]},"application/vnd.logipipe.circuit+zip":{source:"iana",compressible:!1},"application/vnd.loom":{source:"iana"},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.conftoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.license+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.mdcf":{source:"iana"},"application/vnd.mason+json":{source:"iana",compressible:!0},"application/vnd.maxar.archive.3tz+zip":{source:"iana",compressible:!1},"application/vnd.maxmind.maxmind-db":{source:"iana"},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.meridian-slingshot":{source:"iana"},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micro+json":{source:"iana",compressible:!0},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.microsoft.portable-executable":{source:"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{source:"iana"},"application/vnd.miele+json":{source:"iana",compressible:!0},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.minisoft-hp3000-save":{source:"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{source:"iana"},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.motorola.flexsuite":{source:"iana"},"application/vnd.motorola.flexsuite.adsi":{source:"iana"},"application/vnd.motorola.flexsuite.fis":{source:"iana"},"application/vnd.motorola.flexsuite.gotap":{source:"iana"},"application/vnd.motorola.flexsuite.kmr":{source:"iana"},"application/vnd.motorola.flexsuite.ttc":{source:"iana"},"application/vnd.motorola.flexsuite.wem":{source:"iana"},"application/vnd.motorola.iprm":{source:"iana"},"application/vnd.mozilla.xul+xml":{source:"iana",compressible:!0,extensions:["xul"]},"application/vnd.ms-3mfdocument":{source:"iana"},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-asf":{source:"iana"},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-color.iccprofile":{source:"apache"},"application/vnd.ms-excel":{source:"iana",compressible:!1,extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",compressible:!0,extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-office.activex+xml":{source:"iana",compressible:!0},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-opentype":{source:"apache",compressible:!0},"application/vnd.ms-outlook":{compressible:!1,extensions:["msg"]},"application/vnd.ms-package.obfuscated-opentype":{source:"apache"},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-playready.initiator+xml":{source:"iana",compressible:!0},"application/vnd.ms-powerpoint":{source:"iana",compressible:!1,extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{source:"iana",compressible:!0},"application/vnd.ms-printing.printticket+xml":{source:"apache",compressible:!0},"application/vnd.ms-printschematicket+xml":{source:"iana",compressible:!0},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-tnef":{source:"iana"},"application/vnd.ms-windows.devicepairing":{source:"iana"},"application/vnd.ms-windows.nwprinting.oob":{source:"iana"},"application/vnd.ms-windows.printerpairing":{source:"iana"},"application/vnd.ms-windows.wsd.oob":{source:"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.lic-resp":{source:"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.meter-resp":{source:"iana"},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",compressible:!1,extensions:["xps"]},"application/vnd.msa-disk-image":{source:"iana"},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.msign":{source:"iana"},"application/vnd.multiad.creator":{source:"iana"},"application/vnd.multiad.creator.cif":{source:"iana"},"application/vnd.music-niff":{source:"iana"},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.nacamar.ybrid+json":{source:"iana",compressible:!0},"application/vnd.ncd.control":{source:"iana"},"application/vnd.ncd.reference":{source:"iana"},"application/vnd.nearst.inv+json":{source:"iana",compressible:!0},"application/vnd.nebumind.line":{source:"iana"},"application/vnd.nervana":{source:"iana"},"application/vnd.netfpx":{source:"iana"},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nimn":{source:"iana"},"application/vnd.nintendo.nitro.rom":{source:"iana"},"application/vnd.nintendo.snes.rom":{source:"iana"},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.catalogs":{source:"iana"},"application/vnd.nokia.conml+wbxml":{source:"iana"},"application/vnd.nokia.conml+xml":{source:"iana",compressible:!0},"application/vnd.nokia.iptv.config+xml":{source:"iana",compressible:!0},"application/vnd.nokia.isds-radio-presets":{source:"iana"},"application/vnd.nokia.landmark+wbxml":{source:"iana"},"application/vnd.nokia.landmark+xml":{source:"iana",compressible:!0},"application/vnd.nokia.landmarkcollection+xml":{source:"iana",compressible:!0},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",compressible:!0,extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.ncd":{source:"iana"},"application/vnd.nokia.pcd+wbxml":{source:"iana"},"application/vnd.nokia.pcd+xml":{source:"iana",compressible:!0},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.ntt-local.content-share":{source:"iana"},"application/vnd.ntt-local.file-transfer":{source:"iana"},"application/vnd.ntt-local.ogw_remote-access":{source:"iana"},"application/vnd.ntt-local.sip-ta_remote":{source:"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{source:"iana"},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",compressible:!1,extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",compressible:!1,extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",compressible:!1,extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",compressible:!1,extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.obn":{source:"iana"},"application/vnd.ocf+cbor":{source:"iana"},"application/vnd.oci.image.manifest.v1+json":{source:"iana",compressible:!0},"application/vnd.oftn.l10n+json":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessdownload+xml":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessstreaming+xml":{source:"iana",compressible:!0},"application/vnd.oipf.cspg-hexbinary":{source:"iana"},"application/vnd.oipf.dae.svg+xml":{source:"iana",compressible:!0},"application/vnd.oipf.dae.xhtml+xml":{source:"iana",compressible:!0},"application/vnd.oipf.mippvcontrolmessage+xml":{source:"iana",compressible:!0},"application/vnd.oipf.pae.gem":{source:"iana"},"application/vnd.oipf.spdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.oipf.spdlist+xml":{source:"iana",compressible:!0},"application/vnd.oipf.ueprofile+xml":{source:"iana",compressible:!0},"application/vnd.oipf.userprofile+xml":{source:"iana",compressible:!0},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma-scws-config":{source:"iana"},"application/vnd.oma-scws-http-request":{source:"iana"},"application/vnd.oma-scws-http-response":{source:"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.drm-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.imd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.ltkm":{source:"iana"},"application/vnd.oma.bcast.notification+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.provisioningtrigger":{source:"iana"},"application/vnd.oma.bcast.sgboot":{source:"iana"},"application/vnd.oma.bcast.sgdd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sgdu":{source:"iana"},"application/vnd.oma.bcast.simple-symbol-container":{source:"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sprov+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.stkm":{source:"iana"},"application/vnd.oma.cab-address-book+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-feature-handler+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-pcc+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-subs-invite+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-user-prefs+xml":{source:"iana",compressible:!0},"application/vnd.oma.dcd":{source:"iana"},"application/vnd.oma.dcdc":{source:"iana"},"application/vnd.oma.dd2+xml":{source:"iana",compressible:!0,extensions:["dd2"]},"application/vnd.oma.drm.risd+xml":{source:"iana",compressible:!0},"application/vnd.oma.group-usage-list+xml":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+cbor":{source:"iana"},"application/vnd.oma.lwm2m+json":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+tlv":{source:"iana"},"application/vnd.oma.pal+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.detailed-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.final-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.groups+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.invocation-descriptor+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.optimized-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.push":{source:"iana"},"application/vnd.oma.scidm.messages+xml":{source:"iana",compressible:!0},"application/vnd.oma.xcap-directory+xml":{source:"iana",compressible:!0},"application/vnd.omads-email+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-file+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-folder+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omaloc-supl-init":{source:"iana"},"application/vnd.onepager":{source:"iana"},"application/vnd.onepagertamp":{source:"iana"},"application/vnd.onepagertamx":{source:"iana"},"application/vnd.onepagertat":{source:"iana"},"application/vnd.onepagertatp":{source:"iana"},"application/vnd.onepagertatx":{source:"iana"},"application/vnd.openblox.game+xml":{source:"iana",compressible:!0,extensions:["obgx"]},"application/vnd.openblox.game-binary":{source:"iana"},"application/vnd.openeye.oeb":{source:"iana"},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",compressible:!0,extensions:["osm"]},"application/vnd.opentimestamps.ots":{source:"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawing+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",compressible:!1,extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",compressible:!1,extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.theme+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.vmldrawing":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",compressible:!1,extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.core-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.relationships+xml":{source:"iana",compressible:!0},"application/vnd.oracle.resource+json":{source:"iana",compressible:!0},"application/vnd.orange.indata":{source:"iana"},"application/vnd.osa.netdeploy":{source:"iana"},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.bundle":{source:"iana"},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.otps.ct-kip+xml":{source:"iana",compressible:!0},"application/vnd.oxli.countgraph":{source:"iana"},"application/vnd.pagerduty+json":{source:"iana",compressible:!0},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.panoply":{source:"iana"},"application/vnd.paos.xml":{source:"iana"},"application/vnd.patentdive":{source:"iana"},"application/vnd.patientecommsdoc":{source:"iana"},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pcos":{source:"iana"},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.piaccess.application-licence":{source:"iana"},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.poc.group-advertisement+xml":{source:"iana",compressible:!0},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.powerbuilder6-s":{source:"iana"},"application/vnd.powerbuilder7":{source:"iana"},"application/vnd.powerbuilder7-s":{source:"iana"},"application/vnd.powerbuilder75":{source:"iana"},"application/vnd.powerbuilder75-s":{source:"iana"},"application/vnd.preminet":{source:"iana"},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.psfs":{source:"iana"},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.pwg-multiplexed":{source:"iana"},"application/vnd.pwg-xhtml-print+xml":{source:"iana",compressible:!0},"application/vnd.qualcomm.brew-app-res":{source:"iana"},"application/vnd.quarantainenet":{source:"iana"},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{source:"iana"},"application/vnd.radisys.moml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conn+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-stream+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-base+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-detect+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-group+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-speech+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-transform+xml":{source:"iana",compressible:!0},"application/vnd.rainstor.data":{source:"iana"},"application/vnd.rapid":{source:"iana"},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",compressible:!0,extensions:["musicxml"]},"application/vnd.renlearn.rlprint":{source:"iana"},"application/vnd.resilient.logic":{source:"iana"},"application/vnd.restful+json":{source:"iana",compressible:!0},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",compressible:!0,extensions:["link66"]},"application/vnd.rs-274x":{source:"iana"},"application/vnd.ruckus.download":{source:"iana"},"application/vnd.s3sms":{source:"iana"},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.sar":{source:"iana"},"application/vnd.sbm.cid":{source:"iana"},"application/vnd.sbm.mid2":{source:"iana"},"application/vnd.scribus":{source:"iana"},"application/vnd.sealed.3df":{source:"iana"},"application/vnd.sealed.csf":{source:"iana"},"application/vnd.sealed.doc":{source:"iana"},"application/vnd.sealed.eml":{source:"iana"},"application/vnd.sealed.mht":{source:"iana"},"application/vnd.sealed.net":{source:"iana"},"application/vnd.sealed.ppt":{source:"iana"},"application/vnd.sealed.tiff":{source:"iana"},"application/vnd.sealed.xls":{source:"iana"},"application/vnd.sealedmedia.softseal.html":{source:"iana"},"application/vnd.sealedmedia.softseal.pdf":{source:"iana"},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.seis+json":{source:"iana",compressible:!0},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shade-save-file":{source:"iana"},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.shootproof+json":{source:"iana",compressible:!0},"application/vnd.shopkick+json":{source:"iana",compressible:!0},"application/vnd.shp":{source:"iana"},"application/vnd.shx":{source:"iana"},"application/vnd.sigrok.session":{source:"iana"},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.siren+json":{source:"iana",compressible:!0},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.notebook":{source:"iana"},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.snesdev-page-table":{source:"iana"},"application/vnd.software602.filler.form+xml":{source:"iana",compressible:!0,extensions:["fo"]},"application/vnd.software602.filler.form-xml-zip":{source:"iana"},"application/vnd.solent.sdkm+xml":{source:"iana",compressible:!0,extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.sqlite3":{source:"iana"},"application/vnd.sss-cod":{source:"iana"},"application/vnd.sss-dtf":{source:"iana"},"application/vnd.sss-ntf":{source:"iana"},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.street-stream":{source:"iana"},"application/vnd.sun.wadl+xml":{source:"iana",compressible:!0,extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.swiftview-ics":{source:"iana"},"application/vnd.sycle+xml":{source:"iana",compressible:!0},"application/vnd.syft+json":{source:"iana",compressible:!0},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xdm"]},"application/vnd.syncml.dm.notification":{source:"iana"},"application/vnd.syncml.dmddf+wbxml":{source:"iana"},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{source:"iana"},"application/vnd.syncml.dmtnds+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.syncml.ds.notification":{source:"iana"},"application/vnd.tableschema+json":{source:"iana",compressible:!0},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{source:"iana",compressible:!0},"application/vnd.tmd.mediaflex.api+xml":{source:"iana",compressible:!0},"application/vnd.tml":{source:"iana"},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.tri.onesource":{source:"iana"},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.truedoc":{source:"iana"},"application/vnd.ubisoft.webplayer":{source:"iana"},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",compressible:!0,extensions:["uoml"]},"application/vnd.uplanet.alert":{source:"iana"},"application/vnd.uplanet.alert-wbxml":{source:"iana"},"application/vnd.uplanet.bearer-choice":{source:"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{source:"iana"},"application/vnd.uplanet.cacheop":{source:"iana"},"application/vnd.uplanet.cacheop-wbxml":{source:"iana"},"application/vnd.uplanet.channel":{source:"iana"},"application/vnd.uplanet.channel-wbxml":{source:"iana"},"application/vnd.uplanet.list":{source:"iana"},"application/vnd.uplanet.list-wbxml":{source:"iana"},"application/vnd.uplanet.listcmd":{source:"iana"},"application/vnd.uplanet.listcmd-wbxml":{source:"iana"},"application/vnd.uplanet.signal":{source:"iana"},"application/vnd.uri-map":{source:"iana"},"application/vnd.valve.source.material":{source:"iana"},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.vd-study":{source:"iana"},"application/vnd.vectorworks":{source:"iana"},"application/vnd.vel+json":{source:"iana",compressible:!0},"application/vnd.verimatrix.vcas":{source:"iana"},"application/vnd.veritone.aion+json":{source:"iana",compressible:!0},"application/vnd.veryant.thin":{source:"iana"},"application/vnd.ves.encrypted":{source:"iana"},"application/vnd.vidsoft.vidconference":{source:"iana"},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vividence.scriptfile":{source:"iana"},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.sic":{source:"iana"},"application/vnd.wap.slc":{source:"iana"},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wfa.dpp":{source:"iana"},"application/vnd.wfa.p2p":{source:"iana"},"application/vnd.wfa.wsc":{source:"iana"},"application/vnd.windows.devicepairing":{source:"iana"},"application/vnd.wmc":{source:"iana"},"application/vnd.wmf.bootstrap":{source:"iana"},"application/vnd.wolfram.mathematica":{source:"iana"},"application/vnd.wolfram.mathematica.package":{source:"iana"},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wrq-hp3000-labelled":{source:"iana"},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.wv.csp+wbxml":{source:"iana"},"application/vnd.wv.csp+xml":{source:"iana",compressible:!0},"application/vnd.wv.ssp+xml":{source:"iana",compressible:!0},"application/vnd.xacml+json":{source:"iana",compressible:!0},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.xfdl.webform":{source:"iana"},"application/vnd.xmi+xml":{source:"iana",compressible:!0},"application/vnd.xmpie.cpkg":{source:"iana"},"application/vnd.xmpie.dpkg":{source:"iana"},"application/vnd.xmpie.plan":{source:"iana"},"application/vnd.xmpie.ppkg":{source:"iana"},"application/vnd.xmpie.xlim":{source:"iana"},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",compressible:!0,extensions:["osfpvg"]},"application/vnd.yamaha.remote-setup":{source:"iana"},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yamaha.through-ngn":{source:"iana"},"application/vnd.yamaha.tunnel-udpencap":{source:"iana"},"application/vnd.yaoweme":{source:"iana"},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.youtube.yt":{source:"iana"},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",compressible:!0,extensions:["zaz"]},"application/voicexml+xml":{source:"iana",compressible:!0,extensions:["vxml"]},"application/voucher-cms+json":{source:"iana",compressible:!0},"application/vq-rtcpxr":{source:"iana"},"application/wasm":{source:"iana",compressible:!0,extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",compressible:!0,extensions:["wif"]},"application/webpush-options+json":{source:"iana",compressible:!0},"application/whoispp-query":{source:"iana"},"application/whoispp-response":{source:"iana"},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wita":{source:"iana"},"application/wordperfect5.1":{source:"iana"},"application/wsdl+xml":{source:"iana",compressible:!0,extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",compressible:!0,extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",compressible:!1,extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-amf":{source:"apache"},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-arj":{compressible:!1,extensions:["arj"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bdoc":{compressible:!1,extensions:["bdoc"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",compressible:!1,extensions:["bz"]},"application/x-bzip2":{source:"apache",compressible:!1,extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-chrome-extension":{extensions:["crx"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-compress":{source:"apache"},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-deb":{compressible:!1},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",compressible:!0,extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",compressible:!0,extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",compressible:!0,extensions:["res"]},"application/x-dvi":{source:"apache",compressible:!1,extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-dos":{source:"apache"},"application/x-font-framemaker":{source:"apache"},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-libgrx":{source:"apache"},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-speedo":{source:"apache"},"application/x-font-sunos-news":{source:"apache"},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{source:"apache"},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-gzip":{source:"apache"},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-httpd-php":{compressible:!0,extensions:["php"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-iwork-keynote-sffkey":{extensions:["key"]},"application/x-iwork-numbers-sffnumbers":{extensions:["numbers"]},"application/x-iwork-pages-sffpages":{extensions:["pages"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",compressible:!1,extensions:["jnlp"]},"application/x-javascript":{compressible:!0},"application/x-keepass2":{extensions:["kdbx"]},"application/x-latex":{source:"apache",compressible:!1,extensions:["latex"]},"application/x-lua-bytecode":{extensions:["luac"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-mpegurl":{compressible:!1},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdos-program":{extensions:["exe"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-ns-proxy-autoconfig":{compressible:!0,extensions:["pac"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",compressible:!1,extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-pki-message":{source:"iana"},"application/x-rar-compressed":{source:"apache",compressible:!1,extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",compressible:!0,extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",compressible:!1,extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",compressible:!1,extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",compressible:!0,extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-virtualbox-hdd":{compressible:!0,extensions:["hdd"]},"application/x-virtualbox-ova":{compressible:!0,extensions:["ova"]},"application/x-virtualbox-ovf":{compressible:!0,extensions:["ovf"]},"application/x-virtualbox-vbox":{compressible:!0,extensions:["vbox"]},"application/x-virtualbox-vbox-extpack":{compressible:!1,extensions:["vbox-extpack"]},"application/x-virtualbox-vdi":{compressible:!0,extensions:["vdi"]},"application/x-virtualbox-vhd":{compressible:!0,extensions:["vhd"]},"application/x-virtualbox-vmdk":{compressible:!0,extensions:["vmdk"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-web-app-manifest+json":{compressible:!0,extensions:["webapp"]},"application/x-www-form-urlencoded":{source:"iana",compressible:!0},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-x509-ca-ra-cert":{source:"iana"},"application/x-x509-next-ca-cert":{source:"iana"},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",compressible:!0,extensions:["xlf"]},"application/x-xpinstall":{source:"apache",compressible:!1,extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{source:"iana"},"application/xacml+xml":{source:"iana",compressible:!0},"application/xaml+xml":{source:"apache",compressible:!0,extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",compressible:!0,extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",compressible:!0,extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",compressible:!0,extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",compressible:!0,extensions:["xel"]},"application/xcap-error+xml":{source:"iana",compressible:!0},"application/xcap-ns+xml":{source:"iana",compressible:!0,extensions:["xns"]},"application/xcon-conference-info+xml":{source:"iana",compressible:!0},"application/xcon-conference-info-diff+xml":{source:"iana",compressible:!0},"application/xenc+xml":{source:"iana",compressible:!0,extensions:["xenc"]},"application/xhtml+xml":{source:"iana",compressible:!0,extensions:["xhtml","xht"]},"application/xhtml-voice+xml":{source:"apache",compressible:!0},"application/xliff+xml":{source:"iana",compressible:!0,extensions:["xlf"]},"application/xml":{source:"iana",compressible:!0,extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",compressible:!0,extensions:["dtd"]},"application/xml-external-parsed-entity":{source:"iana"},"application/xml-patch+xml":{source:"iana",compressible:!0},"application/xmpp+xml":{source:"iana",compressible:!0},"application/xop+xml":{source:"iana",compressible:!0,extensions:["xop"]},"application/xproc+xml":{source:"apache",compressible:!0,extensions:["xpl"]},"application/xslt+xml":{source:"iana",compressible:!0,extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",compressible:!0,extensions:["xspf"]},"application/xv+xml":{source:"iana",compressible:!0,extensions:["mxml","xhvml","xvml","xvm"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yang-data+json":{source:"iana",compressible:!0},"application/yang-data+xml":{source:"iana",compressible:!0},"application/yang-patch+json":{source:"iana",compressible:!0},"application/yang-patch+xml":{source:"iana",compressible:!0},"application/yin+xml":{source:"iana",compressible:!0,extensions:["yin"]},"application/zip":{source:"iana",compressible:!1,extensions:["zip"]},"application/zlib":{source:"iana"},"application/zstd":{source:"iana"},"audio/1d-interleaved-parityfec":{source:"iana"},"audio/32kadpcm":{source:"iana"},"audio/3gpp":{source:"iana",compressible:!1,extensions:["3gpp"]},"audio/3gpp2":{source:"iana"},"audio/aac":{source:"iana"},"audio/ac3":{source:"iana"},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/amr-wb":{source:"iana"},"audio/amr-wb+":{source:"iana"},"audio/aptx":{source:"iana"},"audio/asc":{source:"iana"},"audio/atrac-advanced-lossless":{source:"iana"},"audio/atrac-x":{source:"iana"},"audio/atrac3":{source:"iana"},"audio/basic":{source:"iana",compressible:!1,extensions:["au","snd"]},"audio/bv16":{source:"iana"},"audio/bv32":{source:"iana"},"audio/clearmode":{source:"iana"},"audio/cn":{source:"iana"},"audio/dat12":{source:"iana"},"audio/dls":{source:"iana"},"audio/dsr-es201108":{source:"iana"},"audio/dsr-es202050":{source:"iana"},"audio/dsr-es202211":{source:"iana"},"audio/dsr-es202212":{source:"iana"},"audio/dv":{source:"iana"},"audio/dvi4":{source:"iana"},"audio/eac3":{source:"iana"},"audio/encaprtp":{source:"iana"},"audio/evrc":{source:"iana"},"audio/evrc-qcp":{source:"iana"},"audio/evrc0":{source:"iana"},"audio/evrc1":{source:"iana"},"audio/evrcb":{source:"iana"},"audio/evrcb0":{source:"iana"},"audio/evrcb1":{source:"iana"},"audio/evrcnw":{source:"iana"},"audio/evrcnw0":{source:"iana"},"audio/evrcnw1":{source:"iana"},"audio/evrcwb":{source:"iana"},"audio/evrcwb0":{source:"iana"},"audio/evrcwb1":{source:"iana"},"audio/evs":{source:"iana"},"audio/flexfec":{source:"iana"},"audio/fwdred":{source:"iana"},"audio/g711-0":{source:"iana"},"audio/g719":{source:"iana"},"audio/g722":{source:"iana"},"audio/g7221":{source:"iana"},"audio/g723":{source:"iana"},"audio/g726-16":{source:"iana"},"audio/g726-24":{source:"iana"},"audio/g726-32":{source:"iana"},"audio/g726-40":{source:"iana"},"audio/g728":{source:"iana"},"audio/g729":{source:"iana"},"audio/g7291":{source:"iana"},"audio/g729d":{source:"iana"},"audio/g729e":{source:"iana"},"audio/gsm":{source:"iana"},"audio/gsm-efr":{source:"iana"},"audio/gsm-hr-08":{source:"iana"},"audio/ilbc":{source:"iana"},"audio/ip-mr_v2.5":{source:"iana"},"audio/isac":{source:"apache"},"audio/l16":{source:"iana"},"audio/l20":{source:"iana"},"audio/l24":{source:"iana",compressible:!1},"audio/l8":{source:"iana"},"audio/lpc":{source:"iana"},"audio/melp":{source:"iana"},"audio/melp1200":{source:"iana"},"audio/melp2400":{source:"iana"},"audio/melp600":{source:"iana"},"audio/mhas":{source:"iana"},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp3":{compressible:!1,extensions:["mp3"]},"audio/mp4":{source:"iana",compressible:!1,extensions:["m4a","mp4a"]},"audio/mp4a-latm":{source:"iana"},"audio/mpa":{source:"iana"},"audio/mpa-robust":{source:"iana"},"audio/mpeg":{source:"iana",compressible:!1,extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{source:"iana"},"audio/musepack":{source:"apache"},"audio/ogg":{source:"iana",compressible:!1,extensions:["oga","ogg","spx","opus"]},"audio/opus":{source:"iana"},"audio/parityfec":{source:"iana"},"audio/pcma":{source:"iana"},"audio/pcma-wb":{source:"iana"},"audio/pcmu":{source:"iana"},"audio/pcmu-wb":{source:"iana"},"audio/prs.sid":{source:"iana"},"audio/qcelp":{source:"iana"},"audio/raptorfec":{source:"iana"},"audio/red":{source:"iana"},"audio/rtp-enc-aescm128":{source:"iana"},"audio/rtp-midi":{source:"iana"},"audio/rtploopback":{source:"iana"},"audio/rtx":{source:"iana"},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/scip":{source:"iana"},"audio/silk":{source:"apache",extensions:["sil"]},"audio/smv":{source:"iana"},"audio/smv-qcp":{source:"iana"},"audio/smv0":{source:"iana"},"audio/sofa":{source:"iana"},"audio/sp-midi":{source:"iana"},"audio/speex":{source:"iana"},"audio/t140c":{source:"iana"},"audio/t38":{source:"iana"},"audio/telephone-event":{source:"iana"},"audio/tetra_acelp":{source:"iana"},"audio/tetra_acelp_bb":{source:"iana"},"audio/tone":{source:"iana"},"audio/tsvcis":{source:"iana"},"audio/uemclip":{source:"iana"},"audio/ulpfec":{source:"iana"},"audio/usac":{source:"iana"},"audio/vdvi":{source:"iana"},"audio/vmr-wb":{source:"iana"},"audio/vnd.3gpp.iufp":{source:"iana"},"audio/vnd.4sb":{source:"iana"},"audio/vnd.audiokoz":{source:"iana"},"audio/vnd.celp":{source:"iana"},"audio/vnd.cisco.nse":{source:"iana"},"audio/vnd.cmles.radio-events":{source:"iana"},"audio/vnd.cns.anp1":{source:"iana"},"audio/vnd.cns.inf1":{source:"iana"},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dlna.adts":{source:"iana"},"audio/vnd.dolby.heaac.1":{source:"iana"},"audio/vnd.dolby.heaac.2":{source:"iana"},"audio/vnd.dolby.mlp":{source:"iana"},"audio/vnd.dolby.mps":{source:"iana"},"audio/vnd.dolby.pl2":{source:"iana"},"audio/vnd.dolby.pl2x":{source:"iana"},"audio/vnd.dolby.pl2z":{source:"iana"},"audio/vnd.dolby.pulse.1":{source:"iana"},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.dts.uhd":{source:"iana"},"audio/vnd.dvb.file":{source:"iana"},"audio/vnd.everad.plj":{source:"iana"},"audio/vnd.hns.audio":{source:"iana"},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nokia.mobile-xmf":{source:"iana"},"audio/vnd.nortel.vbk":{source:"iana"},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.octel.sbc":{source:"iana"},"audio/vnd.presonus.multitrack":{source:"iana"},"audio/vnd.qcelp":{source:"iana"},"audio/vnd.rhetorex.32kadpcm":{source:"iana"},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/vnd.rn-realaudio":{compressible:!1},"audio/vnd.sealedmedia.softseal.mpeg":{source:"iana"},"audio/vnd.vmx.cvsd":{source:"iana"},"audio/vnd.wave":{compressible:!1},"audio/vorbis":{source:"iana",compressible:!1},"audio/vorbis-config":{source:"iana"},"audio/wav":{compressible:!1,extensions:["wav"]},"audio/wave":{compressible:!1,extensions:["wav"]},"audio/webm":{source:"apache",compressible:!1,extensions:["weba"]},"audio/x-aac":{source:"apache",compressible:!1,extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",compressible:!1,extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-tta":{source:"apache"},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/xm":{source:"apache",extensions:["xm"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-pdb":{source:"apache"},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",compressible:!0,extensions:["otf"]},"font/sfnt":{source:"iana"},"font/ttf":{source:"iana",compressible:!0,extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"image/aces":{source:"iana",extensions:["exr"]},"image/apng":{compressible:!1,extensions:["apng"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",compressible:!1,extensions:["avif"]},"image/bmp":{source:"iana",compressible:!0,extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",compressible:!1,extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",compressible:!1,extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",compressible:!1,extensions:["jpeg","jpg","jpe"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",compressible:!1,extensions:["jpm"]},"image/jpx":{source:"iana",compressible:!1,extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/naplps":{source:"iana"},"image/pjpeg":{compressible:!1},"image/png":{source:"iana",compressible:!1,extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/pwg-raster":{source:"iana"},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",compressible:!0,extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",compressible:!1,extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",compressible:!0,extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.cns.inf2":{source:"iana"},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.globalgraphics.pgb":{source:"iana"},"image/vnd.microsoft.icon":{source:"iana",compressible:!0,extensions:["ico"]},"image/vnd.mix":{source:"iana"},"image/vnd.mozilla.apng":{source:"iana"},"image/vnd.ms-dds":{compressible:!0,extensions:["dds"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.radiance":{source:"iana"},"image/vnd.sealed.png":{source:"iana"},"image/vnd.sealedmedia.softseal.gif":{source:"iana"},"image/vnd.sealedmedia.softseal.jpg":{source:"iana"},"image/vnd.svf":{source:"iana"},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",compressible:!0,extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",compressible:!0,extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xcf":{compressible:!1},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"message/cpim":{source:"iana"},"message/delivery-status":{source:"iana"},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/external-body":{source:"iana"},"message/feedback-report":{source:"iana"},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/http":{source:"iana",compressible:!1},"message/imdn+xml":{source:"iana",compressible:!0},"message/news":{source:"iana"},"message/partial":{source:"iana",compressible:!1},"message/rfc822":{source:"iana",compressible:!0,extensions:["eml","mime"]},"message/s-http":{source:"iana"},"message/sip":{source:"iana"},"message/sipfrag":{source:"iana"},"message/tracking-status":{source:"iana"},"message/vnd.si.simp":{source:"iana"},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/e57":{source:"iana"},"model/gltf+json":{source:"iana",compressible:!0,extensions:["gltf"]},"model/gltf-binary":{source:"iana",compressible:!0,extensions:["glb"]},"model/iges":{source:"iana",compressible:!1,extensions:["igs","iges"]},"model/mesh":{source:"iana",compressible:!1,extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana"},"model/step+xml":{source:"iana",compressible:!0,extensions:["stpx"]},"model/step+zip":{source:"iana",compressible:!1,extensions:["stpz"]},"model/step-xml+zip":{source:"iana",compressible:!1,extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",compressible:!0,extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.flatland.3dml":{source:"iana"},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gs-gdl":{source:"apache"},"model/vnd.gs.gdl":{source:"iana"},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.moml+xml":{source:"iana",compressible:!0},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.pytha.pyox":{source:"iana"},"model/vnd.rosette.annotated-data-model":{source:"iana"},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",compressible:!1,extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",compressible:!1,extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",compressible:!1,extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",compressible:!1,extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",compressible:!0,extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"multipart/alternative":{source:"iana",compressible:!1},"multipart/appledouble":{source:"iana"},"multipart/byteranges":{source:"iana"},"multipart/digest":{source:"iana"},"multipart/encrypted":{source:"iana",compressible:!1},"multipart/form-data":{source:"iana",compressible:!1},"multipart/header-set":{source:"iana"},"multipart/mixed":{source:"iana"},"multipart/multilingual":{source:"iana"},"multipart/parallel":{source:"iana"},"multipart/related":{source:"iana",compressible:!1},"multipart/report":{source:"iana"},"multipart/signed":{source:"iana",compressible:!1},"multipart/vnd.bint.med-plus":{source:"iana"},"multipart/voice-message":{source:"iana"},"multipart/x-mixed-replace":{source:"iana"},"text/1d-interleaved-parityfec":{source:"iana"},"text/cache-manifest":{source:"iana",compressible:!0,extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/calender":{compressible:!0},"text/cmd":{compressible:!0},"text/coffeescript":{extensions:["coffee","litcoffee"]},"text/cql":{source:"iana"},"text/cql-expression":{source:"iana"},"text/cql-identifier":{source:"iana"},"text/css":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["css"]},"text/csv":{source:"iana",compressible:!0,extensions:["csv"]},"text/csv-schema":{source:"iana"},"text/directory":{source:"iana"},"text/dns":{source:"iana"},"text/ecmascript":{source:"iana"},"text/encaprtp":{source:"iana"},"text/enriched":{source:"iana"},"text/fhirpath":{source:"iana"},"text/flexfec":{source:"iana"},"text/fwdred":{source:"iana"},"text/gff3":{source:"iana"},"text/grammar-ref-list":{source:"iana"},"text/html":{source:"iana",compressible:!0,extensions:["html","htm","shtml"]},"text/jade":{extensions:["jade"]},"text/javascript":{source:"iana",compressible:!0},"text/jcr-cnd":{source:"iana"},"text/jsx":{compressible:!0,extensions:["jsx"]},"text/less":{compressible:!0,extensions:["less"]},"text/markdown":{source:"iana",compressible:!0,extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/mdx":{compressible:!0,extensions:["mdx"]},"text/mizar":{source:"iana"},"text/n3":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["n3"]},"text/parameters":{source:"iana",charset:"UTF-8"},"text/parityfec":{source:"iana"},"text/plain":{source:"iana",compressible:!0,extensions:["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{source:"iana",charset:"UTF-8"},"text/prs.fallenstein.rst":{source:"iana"},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/prs.prop.logic":{source:"iana"},"text/raptorfec":{source:"iana"},"text/red":{source:"iana"},"text/rfc822-headers":{source:"iana"},"text/richtext":{source:"iana",compressible:!0,extensions:["rtx"]},"text/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"text/rtp-enc-aescm128":{source:"iana"},"text/rtploopback":{source:"iana"},"text/rtx":{source:"iana"},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shaclc":{source:"iana"},"text/shex":{source:"iana",extensions:["shex"]},"text/slim":{extensions:["slim","slm"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/strings":{source:"iana"},"text/stylus":{extensions:["stylus","styl"]},"text/t140":{source:"iana"},"text/tab-separated-values":{source:"iana",compressible:!0,extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/ulpfec":{source:"iana"},"text/uri-list":{source:"iana",compressible:!0,extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",compressible:!0,extensions:["vcard"]},"text/vnd.a":{source:"iana"},"text/vnd.abc":{source:"iana"},"text/vnd.ascii-art":{source:"iana"},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.debian.copyright":{source:"iana",charset:"UTF-8"},"text/vnd.dmclientscript":{source:"iana"},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.esmertec.theme-descriptor":{source:"iana",charset:"UTF-8"},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.ficlab.flt":{source:"iana"},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.gml":{source:"iana"},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.hans":{source:"iana"},"text/vnd.hgl":{source:"iana"},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.iptc.newsml":{source:"iana"},"text/vnd.iptc.nitf":{source:"iana"},"text/vnd.latex-z":{source:"iana"},"text/vnd.motorola.reflex":{source:"iana"},"text/vnd.ms-mediapackage":{source:"iana"},"text/vnd.net2phone.commcenter.command":{source:"iana"},"text/vnd.radisys.msml-basic-layout":{source:"iana"},"text/vnd.senx.warpscript":{source:"iana"},"text/vnd.si.uricatalogue":{source:"iana"},"text/vnd.sosi":{source:"iana"},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.trolltech.linguist":{source:"iana",charset:"UTF-8"},"text/vnd.wap.si":{source:"iana"},"text/vnd.wap.sl":{source:"iana"},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-gwt-rpc":{compressible:!0},"text/x-handlebars-template":{extensions:["hbs"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-jquery-tmpl":{compressible:!0},"text/x-lua":{extensions:["lua"]},"text/x-markdown":{compressible:!0,extensions:["mkd"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-org":{compressible:!0,extensions:["org"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-processing":{compressible:!0,extensions:["pde"]},"text/x-sass":{extensions:["sass"]},"text/x-scss":{extensions:["scss"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-suse-ymp":{compressible:!0,extensions:["ymp"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",compressible:!0,extensions:["xml"]},"text/xml-external-parsed-entity":{source:"iana"},"text/yaml":{compressible:!0,extensions:["yaml","yml"]},"video/1d-interleaved-parityfec":{source:"iana"},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp-tt":{source:"iana"},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/av1":{source:"iana"},"video/bmpeg":{source:"iana"},"video/bt656":{source:"iana"},"video/celb":{source:"iana"},"video/dv":{source:"iana"},"video/encaprtp":{source:"iana"},"video/ffv1":{source:"iana"},"video/flexfec":{source:"iana"},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h263-1998":{source:"iana"},"video/h263-2000":{source:"iana"},"video/h264":{source:"iana",extensions:["h264"]},"video/h264-rcdo":{source:"iana"},"video/h264-svc":{source:"iana"},"video/h265":{source:"iana"},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpeg2000":{source:"iana"},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/jxsv":{source:"iana"},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp1s":{source:"iana"},"video/mp2p":{source:"iana"},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",compressible:!1,extensions:["mp4","mp4v","mpg4"]},"video/mp4v-es":{source:"iana"},"video/mpeg":{source:"iana",compressible:!1,extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{source:"iana"},"video/mpv":{source:"iana"},"video/nv":{source:"iana"},"video/ogg":{source:"iana",compressible:!1,extensions:["ogv"]},"video/parityfec":{source:"iana"},"video/pointer":{source:"iana"},"video/quicktime":{source:"iana",compressible:!1,extensions:["qt","mov"]},"video/raptorfec":{source:"iana"},"video/raw":{source:"iana"},"video/rtp-enc-aescm128":{source:"iana"},"video/rtploopback":{source:"iana"},"video/rtx":{source:"iana"},"video/scip":{source:"iana"},"video/smpte291":{source:"iana"},"video/smpte292m":{source:"iana"},"video/ulpfec":{source:"iana"},"video/vc1":{source:"iana"},"video/vc2":{source:"iana"},"video/vnd.cctv":{source:"iana"},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.mp4":{source:"iana"},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.directv.mpeg":{source:"iana"},"video/vnd.directv.mpeg-tts":{source:"iana"},"video/vnd.dlna.mpeg-tts":{source:"iana"},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.hns.video":{source:"iana"},"video/vnd.iptvforum.1dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.1dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.2dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.2dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.ttsavc":{source:"iana"},"video/vnd.iptvforum.ttsmpeg2":{source:"iana"},"video/vnd.motorola.video":{source:"iana"},"video/vnd.motorola.videop":{source:"iana"},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.nokia.interleaved-multimedia":{source:"iana"},"video/vnd.nokia.mp4vr":{source:"iana"},"video/vnd.nokia.videovoip":{source:"iana"},"video/vnd.objectvideo":{source:"iana"},"video/vnd.radgamettools.bink":{source:"iana"},"video/vnd.radgamettools.smacker":{source:"iana"},"video/vnd.sealed.mpeg1":{source:"iana"},"video/vnd.sealed.mpeg4":{source:"iana"},"video/vnd.sealed.swf":{source:"iana"},"video/vnd.sealedmedia.softseal.mov":{source:"iana"},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/vnd.youtube.yt":{source:"iana"},"video/vp8":{source:"iana"},"video/vp9":{source:"iana"},"video/webm":{source:"apache",compressible:!1,extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",compressible:!1,extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",compressible:!1,extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",compressible:!1,extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]},"x-shader/x-fragment":{compressible:!0},"x-shader/x-vertex":{compressible:!0}}});var Su=A((vk,Eu)=>{Eu.exports=wu()});var ku=A(Xe=>{"use strict";var Qs=Su(),Zy=require("path").extname,Pu=/^\s*([^;\s]*)(?:;|\s|$)/,Gy=/^text\//i;Xe.charset=Ru;Xe.charsets={lookup:Ru};Xe.contentType=Jy;Xe.extension=Ky;Xe.extensions=Object.create(null);Xe.lookup=Qy;Xe.types=Object.create(null);Wy(Xe.extensions,Xe.types);function Ru(t){if(!t||typeof t!="string")return!1;var e=Pu.exec(t),a=e&&Qs[e[1].toLowerCase()];return a&&a.charset?a.charset:e&&Gy.test(e[1])?"UTF-8":!1}function Jy(t){if(!t||typeof t!="string")return!1;var e=t.indexOf("/")===-1?Xe.lookup(t):t;if(!e)return!1;if(e.indexOf("charset")===-1){var a=Xe.charset(e);a&&(e+="; charset="+a.toLowerCase())}return e}function Ky(t){if(!t||typeof t!="string")return!1;var e=Pu.exec(t),a=e&&Xe.extensions[e[1].toLowerCase()];return!a||!a.length?!1:a[0]}function Qy(t){if(!t||typeof t!="string")return!1;var e=Zy("x."+t).toLowerCase().substr(1);return e&&Xe.types[e]||!1}function Wy(t,e){var a=["nginx","apache",void 0,"iana"];Object.keys(Qs).forEach(function(r){var n=Qs[r],i=n.extensions;if(!(!i||!i.length)){t[r]=i;for(var o=0;o<i.length;o++){var p=i[o];if(e[p]){var c=a.indexOf(Qs[e[p]].source),l=a.indexOf(n.source);if(e[p]!=="application/octet-stream"&&(c>l||c===l&&e[p].substr(0,12)==="application/"))continue}e[p]=r}}})}});var Au=A((gk,Tu)=>{Tu.exports=Xy;function Xy(t){var e=typeof setImmediate=="function"?setImmediate:typeof process=="object"&&typeof process.nextTick=="function"?process.nextTick:null;e?e(t):setTimeout(t,0)}});var Si=A((yk,Ou)=>{var Cu=Au();Ou.exports=Yy;function Yy(t){var e=!1;return Cu(function(){e=!0}),function(s,r){e?t(s,r):Cu(function(){t(s,r)})}}});var Pi=A((bk,ju)=>{ju.exports=eb;function eb(t){Object.keys(t.jobs).forEach(tb.bind(t)),t.jobs={}}function tb(t){typeof this.jobs[t]=="function"&&this.jobs[t]()}});var Ri=A((_k,Du)=>{var Iu=Si(),ab=Pi();Du.exports=rb;function rb(t,e,a,s){var r=a.keyedList?a.keyedList[a.index]:a.index;a.jobs[r]=sb(e,r,t[r],function(n,i){r in a.jobs&&(delete a.jobs[r],n?ab(a):a.results[r]=i,s(n,a.results))})}function sb(t,e,a,s){var r;return t.length==2?r=t(a,Iu(s)):r=t(a,e,Iu(s)),r}});var ki=A((wk,Nu)=>{Nu.exports=nb;function nb(t,e){var a=!Array.isArray(t),s={index:0,keyedList:a||e?Object.keys(t):null,jobs:{},results:a?{}:[],size:a?Object.keys(t).length:t.length};return e&&s.keyedList.sort(a?e:function(r,n){return e(t[r],t[n])}),s}});var Ti=A((Ek,Fu)=>{var ib=Pi(),ob=Si();Fu.exports=cb;function cb(t){Object.keys(this.jobs).length&&(this.index=this.size,ib(this),ob(t)(null,this.results))}});var $u=A((Sk,Lu)=>{var lb=Ri(),pb=ki(),ub=Ti();Lu.exports=db;function db(t,e,a){for(var s=pb(t);s.index<(s.keyedList||t).length;)lb(t,e,s,function(r,n){if(r){a(r,n);return}if(Object.keys(s.jobs).length===0){a(null,s.results);return}}),s.index++;return ub.bind(s,a)}});var Ai=A((Pk,Ws)=>{var qu=Ri(),mb=ki(),fb=Ti();Ws.exports=hb;Ws.exports.ascending=Uu;Ws.exports.descending=vb;function hb(t,e,a,s){var r=mb(t,a);return qu(t,e,r,function n(i,o){if(i){s(i,o);return}if(r.index++,r.index<(r.keyedList||t).length){qu(t,e,r,n);return}s(null,r.results)}),fb.bind(r,s)}function Uu(t,e){return t<e?-1:t>e?1:0}function vb(t,e){return-1*Uu(t,e)}});var Mu=A((Rk,zu)=>{var xb=Ai();zu.exports=gb;function gb(t,e,a){return xb(t,e,null,a)}});var Hu=A((kk,Bu)=>{Bu.exports={parallel:$u(),serial:Mu(),serialOrdered:Ai()}});var Ci=A((Tk,Vu)=>{"use strict";Vu.exports=Object});var Gu=A((Ak,Zu)=>{"use strict";Zu.exports=Error});var Ku=A((Ck,Ju)=>{"use strict";Ju.exports=EvalError});var Wu=A((Ok,Qu)=>{"use strict";Qu.exports=RangeError});var Yu=A((jk,Xu)=>{"use strict";Xu.exports=ReferenceError});var td=A((Ik,ed)=>{"use strict";ed.exports=SyntaxError});var Xs=A((Dk,ad)=>{"use strict";ad.exports=TypeError});var sd=A((Nk,rd)=>{"use strict";rd.exports=URIError});var id=A((Fk,nd)=>{"use strict";nd.exports=Math.abs});var cd=A((Lk,od)=>{"use strict";od.exports=Math.floor});var pd=A(($k,ld)=>{"use strict";ld.exports=Math.max});var dd=A((qk,ud)=>{"use strict";ud.exports=Math.min});var fd=A((Uk,md)=>{"use strict";md.exports=Math.pow});var vd=A((zk,hd)=>{"use strict";hd.exports=Math.round});var gd=A((Mk,xd)=>{"use strict";xd.exports=Number.isNaN||function(e){return e!==e}});var bd=A((Bk,yd)=>{"use strict";var yb=gd();yd.exports=function(e){return yb(e)||e===0?e:e<0?-1:1}});var wd=A((Hk,_d)=>{"use strict";_d.exports=Object.getOwnPropertyDescriptor});var Oi=A((Vk,Ed)=>{"use strict";var Ys=wd();if(Ys)try{Ys([],"length")}catch{Ys=null}Ed.exports=Ys});var Pd=A((Zk,Sd)=>{"use strict";var en=Object.defineProperty||!1;if(en)try{en({},"a",{value:1})}catch{en=!1}Sd.exports=en});var ji=A((Gk,Rd)=>{"use strict";Rd.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var e={},a=Symbol("test"),s=Object(a);if(typeof a=="string"||Object.prototype.toString.call(a)!=="[object Symbol]"||Object.prototype.toString.call(s)!=="[object Symbol]")return!1;var r=42;e[a]=r;for(var n in e)return!1;if(typeof Object.keys=="function"&&Object.keys(e).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(e).length!==0)return!1;var i=Object.getOwnPropertySymbols(e);if(i.length!==1||i[0]!==a||!Object.prototype.propertyIsEnumerable.call(e,a))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var o=Object.getOwnPropertyDescriptor(e,a);if(o.value!==r||o.enumerable!==!0)return!1}return!0}});var Ad=A((Jk,Td)=>{"use strict";var kd=typeof Symbol<"u"&&Symbol,bb=ji();Td.exports=function(){return typeof kd!="function"||typeof Symbol!="function"||typeof kd("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:bb()}});var Ii=A((Kk,Cd)=>{"use strict";Cd.exports=typeof Reflect<"u"&&Reflect.getPrototypeOf||null});var Di=A((Qk,Od)=>{"use strict";var _b=Ci();Od.exports=_b.getPrototypeOf||null});var Dd=A((Wk,Id)=>{"use strict";var wb="Function.prototype.bind called on incompatible ",Eb=Object.prototype.toString,Sb=Math.max,Pb="[object Function]",jd=function(e,a){for(var s=[],r=0;r<e.length;r+=1)s[r]=e[r];for(var n=0;n<a.length;n+=1)s[n+e.length]=a[n];return s},Rb=function(e,a){for(var s=[],r=a||0,n=0;r<e.length;r+=1,n+=1)s[n]=e[r];return s},kb=function(t,e){for(var a="",s=0;s<t.length;s+=1)a+=t[s],s+1<t.length&&(a+=e);return a};Id.exports=function(e){var a=this;if(typeof a!="function"||Eb.apply(a)!==Pb)throw new TypeError(wb+a);for(var s=Rb(arguments,1),r,n=function(){if(this instanceof r){var l=a.apply(this,jd(s,arguments));return Object(l)===l?l:this}return a.apply(e,jd(s,arguments))},i=Sb(0,a.length-s.length),o=[],p=0;p<i;p++)o[p]="$"+p;if(r=Function("binder","return function ("+kb(o,",")+"){ return binder.apply(this,arguments); }")(n),a.prototype){var c=function(){};c.prototype=a.prototype,r.prototype=new c,c.prototype=null}return r}});var Ar=A((Xk,Nd)=>{"use strict";var Tb=Dd();Nd.exports=Function.prototype.bind||Tb});var tn=A((Yk,Fd)=>{"use strict";Fd.exports=Function.prototype.call});var Ni=A((e1,Ld)=>{"use strict";Ld.exports=Function.prototype.apply});var qd=A((t1,$d)=>{"use strict";$d.exports=typeof Reflect<"u"&&Reflect&&Reflect.apply});var zd=A((a1,Ud)=>{"use strict";var Ab=Ar(),Cb=Ni(),Ob=tn(),jb=qd();Ud.exports=jb||Ab.call(Ob,Cb)});var Bd=A((r1,Md)=>{"use strict";var Ib=Ar(),Db=Xs(),Nb=tn(),Fb=zd();Md.exports=function(e){if(e.length<1||typeof e[0]!="function")throw new Db("a function is required");return Fb(Ib,Nb,e)}});var Kd=A((s1,Jd)=>{"use strict";var Lb=Bd(),Hd=Oi(),Zd;try{Zd=[].__proto__===Array.prototype}catch(t){if(!t||typeof t!="object"||!("code"in t)||t.code!=="ERR_PROTO_ACCESS")throw t}var Fi=!!Zd&&Hd&&Hd(Object.prototype,"__proto__"),Gd=Object,Vd=Gd.getPrototypeOf;Jd.exports=Fi&&typeof Fi.get=="function"?Lb([Fi.get]):typeof Vd=="function"?function(e){return Vd(e==null?e:Gd(e))}:!1});var em=A((n1,Yd)=>{"use strict";var Qd=Ii(),Wd=Di(),Xd=Kd();Yd.exports=Qd?function(e){return Qd(e)}:Wd?function(e){if(!e||typeof e!="object"&&typeof e!="function")throw new TypeError("getProto: not an object");return Wd(e)}:Xd?function(e){return Xd(e)}:null});var an=A((i1,tm)=>{"use strict";var $b=Function.prototype.call,qb=Object.prototype.hasOwnProperty,Ub=Ar();tm.exports=Ub.call($b,qb)});var cm=A((o1,om)=>{"use strict";var ne,zb=Ci(),Mb=Gu(),Bb=Ku(),Hb=Wu(),Vb=Yu(),tr=td(),er=Xs(),Zb=sd(),Gb=id(),Jb=cd(),Kb=pd(),Qb=dd(),Wb=fd(),Xb=vd(),Yb=bd(),nm=Function,Li=function(t){try{return nm('"use strict"; return ('+t+").constructor;")()}catch{}},Cr=Oi(),e0=Pd(),$i=function(){throw new er},t0=Cr?function(){try{return arguments.callee,$i}catch{try{return Cr(arguments,"callee").get}catch{return $i}}}():$i,Xa=Ad()(),Le=em(),a0=Di(),r0=Ii(),im=Ni(),Or=tn(),Ya={},s0=typeof Uint8Array>"u"||!Le?ne:Le(Uint8Array),Pa={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?ne:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?ne:ArrayBuffer,"%ArrayIteratorPrototype%":Xa&&Le?Le([][Symbol.iterator]()):ne,"%AsyncFromSyncIteratorPrototype%":ne,"%AsyncFunction%":Ya,"%AsyncGenerator%":Ya,"%AsyncGeneratorFunction%":Ya,"%AsyncIteratorPrototype%":Ya,"%Atomics%":typeof Atomics>"u"?ne:Atomics,"%BigInt%":typeof BigInt>"u"?ne:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?ne:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?ne:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?ne:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Mb,"%eval%":eval,"%EvalError%":Bb,"%Float16Array%":typeof Float16Array>"u"?ne:Float16Array,"%Float32Array%":typeof Float32Array>"u"?ne:Float32Array,"%Float64Array%":typeof Float64Array>"u"?ne:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?ne:FinalizationRegistry,"%Function%":nm,"%GeneratorFunction%":Ya,"%Int8Array%":typeof Int8Array>"u"?ne:Int8Array,"%Int16Array%":typeof Int16Array>"u"?ne:Int16Array,"%Int32Array%":typeof Int32Array>"u"?ne:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Xa&&Le?Le(Le([][Symbol.iterator]())):ne,"%JSON%":typeof JSON=="object"?JSON:ne,"%Map%":typeof Map>"u"?ne:Map,"%MapIteratorPrototype%":typeof Map>"u"||!Xa||!Le?ne:Le(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":zb,"%Object.getOwnPropertyDescriptor%":Cr,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?ne:Promise,"%Proxy%":typeof Proxy>"u"?ne:Proxy,"%RangeError%":Hb,"%ReferenceError%":Vb,"%Reflect%":typeof Reflect>"u"?ne:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?ne:Set,"%SetIteratorPrototype%":typeof Set>"u"||!Xa||!Le?ne:Le(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?ne:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Xa&&Le?Le(""[Symbol.iterator]()):ne,"%Symbol%":Xa?Symbol:ne,"%SyntaxError%":tr,"%ThrowTypeError%":t0,"%TypedArray%":s0,"%TypeError%":er,"%Uint8Array%":typeof Uint8Array>"u"?ne:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?ne:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?ne:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?ne:Uint32Array,"%URIError%":Zb,"%WeakMap%":typeof WeakMap>"u"?ne:WeakMap,"%WeakRef%":typeof WeakRef>"u"?ne:WeakRef,"%WeakSet%":typeof WeakSet>"u"?ne:WeakSet,"%Function.prototype.call%":Or,"%Function.prototype.apply%":im,"%Object.defineProperty%":e0,"%Object.getPrototypeOf%":a0,"%Math.abs%":Gb,"%Math.floor%":Jb,"%Math.max%":Kb,"%Math.min%":Qb,"%Math.pow%":Wb,"%Math.round%":Xb,"%Math.sign%":Yb,"%Reflect.getPrototypeOf%":r0};if(Le)try{null.error}catch(t){am=Le(Le(t)),Pa["%Error.prototype%"]=am}var am,n0=function t(e){var a;if(e==="%AsyncFunction%")a=Li("async function () {}");else if(e==="%GeneratorFunction%")a=Li("function* () {}");else if(e==="%AsyncGeneratorFunction%")a=Li("async function* () {}");else if(e==="%AsyncGenerator%"){var s=t("%AsyncGeneratorFunction%");s&&(a=s.prototype)}else if(e==="%AsyncIteratorPrototype%"){var r=t("%AsyncGenerator%");r&&Le&&(a=Le(r.prototype))}return Pa[e]=a,a},rm={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},jr=Ar(),rn=an(),i0=jr.call(Or,Array.prototype.concat),o0=jr.call(im,Array.prototype.splice),sm=jr.call(Or,String.prototype.replace),sn=jr.call(Or,String.prototype.slice),c0=jr.call(Or,RegExp.prototype.exec),l0=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,p0=/\\(\\)?/g,u0=function(e){var a=sn(e,0,1),s=sn(e,-1);if(a==="%"&&s!=="%")throw new tr("invalid intrinsic syntax, expected closing `%`");if(s==="%"&&a!=="%")throw new tr("invalid intrinsic syntax, expected opening `%`");var r=[];return sm(e,l0,function(n,i,o,p){r[r.length]=o?sm(p,p0,"$1"):i||n}),r},d0=function(e,a){var s=e,r;if(rn(rm,s)&&(r=rm[s],s="%"+r[0]+"%"),rn(Pa,s)){var n=Pa[s];if(n===Ya&&(n=n0(s)),typeof n>"u"&&!a)throw new er("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:s,value:n}}throw new tr("intrinsic "+e+" does not exist!")};om.exports=function(e,a){if(typeof e!="string"||e.length===0)throw new er("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof a!="boolean")throw new er('"allowMissing" argument must be a boolean');if(c0(/^%?[^%]*%?$/,e)===null)throw new tr("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var s=u0(e),r=s.length>0?s[0]:"",n=d0("%"+r+"%",a),i=n.name,o=n.value,p=!1,c=n.alias;c&&(r=c[0],o0(s,i0([0,1],c)));for(var l=1,m=!0;l<s.length;l+=1){var x=s[l],d=sn(x,0,1),f=sn(x,-1);if((d==='"'||d==="'"||d==="`"||f==='"'||f==="'"||f==="`")&&d!==f)throw new tr("property names with quotes must have matching quotes");if((x==="constructor"||!m)&&(p=!0),r+="."+x,i="%"+r+"%",rn(Pa,i))o=Pa[i];else if(o!=null){if(!(x in o)){if(!a)throw new er("base intrinsic for "+e+" exists, but the property is not available.");return}if(Cr&&l+1>=s.length){var g=Cr(o,x);m=!!g,m&&"get"in g&&!("originalValue"in g.get)?o=g.get:o=o[x]}else m=rn(o,x),o=o[x];m&&!p&&(Pa[i]=o)}}return o}});var pm=A((c1,lm)=>{"use strict";var m0=ji();lm.exports=function(){return m0()&&!!Symbol.toStringTag}});var mm=A((l1,dm)=>{"use strict";var f0=cm(),um=f0("%Object.defineProperty%",!0),h0=pm()(),v0=an(),x0=Xs(),nn=h0?Symbol.toStringTag:null;dm.exports=function(e,a){var s=arguments.length>2&&!!arguments[2]&&arguments[2].force,r=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(typeof s<"u"&&typeof s!="boolean"||typeof r<"u"&&typeof r!="boolean")throw new x0("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");nn&&(s||!v0(e,nn))&&(um?um(e,nn,{configurable:!r,enumerable:!1,value:a,writable:!1}):e[nn]=a)}});var hm=A((p1,fm)=>{"use strict";fm.exports=function(t,e){return Object.keys(e).forEach(function(a){t[a]=t[a]||e[a]}),t}});var xm=A((u1,vm)=>{"use strict";var Mi=_u(),g0=require("util"),qi=require("path"),y0=require("http"),b0=require("https"),_0=require("url").parse,w0=require("fs"),E0=require("stream").Stream,S0=require("crypto"),Ui=ku(),P0=Hu(),R0=mm(),Wt=an(),zi=hm();function ce(t){if(!(this instanceof ce))return new ce(t);this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],Mi.call(this),t=t||{};for(var e in t)this[e]=t[e]}g0.inherits(ce,Mi);ce.LINE_BREAK=`\r
`;ce.DEFAULT_CONTENT_TYPE="application/octet-stream";ce.prototype.append=function(t,e,a){a=a||{},typeof a=="string"&&(a={filename:a});var s=Mi.prototype.append.bind(this);if((typeof e=="number"||e==null)&&(e=String(e)),Array.isArray(e)){this._error(new Error("Arrays are not supported."));return}var r=this._multiPartHeader(t,e,a),n=this._multiPartFooter();s(r),s(e),s(n),this._trackLength(r,e,a)};ce.prototype._trackLength=function(t,e,a){var s=0;a.knownLength!=null?s+=Number(a.knownLength):Buffer.isBuffer(e)?s=e.length:typeof e=="string"&&(s=Buffer.byteLength(e)),this._valueLength+=s,this._overheadLength+=Buffer.byteLength(t)+ce.LINE_BREAK.length,!(!e||!e.path&&!(e.readable&&Wt(e,"httpVersion"))&&!(e instanceof E0))&&(a.knownLength||this._valuesToMeasure.push(e))};ce.prototype._lengthRetriever=function(t,e){Wt(t,"fd")?t.end!=null&&t.end!=1/0&&t.start!=null?e(null,t.end+1-(t.start?t.start:0)):w0.stat(t.path,function(a,s){if(a){e(a);return}var r=s.size-(t.start?t.start:0);e(null,r)}):Wt(t,"httpVersion")?e(null,Number(t.headers["content-length"])):Wt(t,"httpModule")?(t.on("response",function(a){t.pause(),e(null,Number(a.headers["content-length"]))}),t.resume()):e("Unknown stream")};ce.prototype._multiPartHeader=function(t,e,a){if(typeof a.header=="string")return a.header;var s=this._getContentDisposition(e,a),r=this._getContentType(e,a),n="",i={"Content-Disposition":["form-data",'name="'+t+'"'].concat(s||[]),"Content-Type":[].concat(r||[])};typeof a.header=="object"&&zi(i,a.header);var o;for(var p in i)if(Wt(i,p)){if(o=i[p],o==null)continue;Array.isArray(o)||(o=[o]),o.length&&(n+=p+": "+o.join("; ")+ce.LINE_BREAK)}return"--"+this.getBoundary()+ce.LINE_BREAK+n+ce.LINE_BREAK};ce.prototype._getContentDisposition=function(t,e){var a;if(typeof e.filepath=="string"?a=qi.normalize(e.filepath).replace(/\\/g,"/"):e.filename||t&&(t.name||t.path)?a=qi.basename(e.filename||t&&(t.name||t.path)):t&&t.readable&&Wt(t,"httpVersion")&&(a=qi.basename(t.client._httpMessage.path||"")),a)return'filename="'+a+'"'};ce.prototype._getContentType=function(t,e){var a=e.contentType;return!a&&t&&t.name&&(a=Ui.lookup(t.name)),!a&&t&&t.path&&(a=Ui.lookup(t.path)),!a&&t&&t.readable&&Wt(t,"httpVersion")&&(a=t.headers["content-type"]),!a&&(e.filepath||e.filename)&&(a=Ui.lookup(e.filepath||e.filename)),!a&&t&&typeof t=="object"&&(a=ce.DEFAULT_CONTENT_TYPE),a};ce.prototype._multiPartFooter=function(){return function(t){var e=ce.LINE_BREAK,a=this._streams.length===0;a&&(e+=this._lastBoundary()),t(e)}.bind(this)};ce.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+ce.LINE_BREAK};ce.prototype.getHeaders=function(t){var e,a={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(e in t)Wt(t,e)&&(a[e.toLowerCase()]=t[e]);return a};ce.prototype.setBoundary=function(t){if(typeof t!="string")throw new TypeError("FormData boundary must be a string");this._boundary=t};ce.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary};ce.prototype.getBuffer=function(){for(var t=new Buffer.alloc(0),e=this.getBoundary(),a=0,s=this._streams.length;a<s;a++)typeof this._streams[a]!="function"&&(Buffer.isBuffer(this._streams[a])?t=Buffer.concat([t,this._streams[a]]):t=Buffer.concat([t,Buffer.from(this._streams[a])]),(typeof this._streams[a]!="string"||this._streams[a].substring(2,e.length+2)!==e)&&(t=Buffer.concat([t,Buffer.from(ce.LINE_BREAK)])));return Buffer.concat([t,Buffer.from(this._lastBoundary())])};ce.prototype._generateBoundary=function(){this._boundary="--------------------------"+S0.randomBytes(12).toString("hex")};ce.prototype.getLengthSync=function(){var t=this._overheadLength+this._valueLength;return this._streams.length&&(t+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),t};ce.prototype.hasKnownLength=function(){var t=!0;return this._valuesToMeasure.length&&(t=!1),t};ce.prototype.getLength=function(t){var e=this._overheadLength+this._valueLength;if(this._streams.length&&(e+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(t.bind(this,null,e));return}P0.parallel(this._valuesToMeasure,this._lengthRetriever,function(a,s){if(a){t(a);return}s.forEach(function(r){e+=r}),t(null,e)})};ce.prototype.submit=function(t,e){var a,s,r={method:"post"};return typeof t=="string"?(t=_0(t),s=zi({port:t.port,path:t.pathname,host:t.hostname,protocol:t.protocol},r)):(s=zi(t,r),s.port||(s.port=s.protocol==="https:"?443:80)),s.headers=this.getHeaders(t.headers),s.protocol==="https:"?a=b0.request(s):a=y0.request(s),this.getLength(function(n,i){if(n&&n!=="Unknown stream"){this._error(n);return}if(i&&a.setHeader("Content-Length",i),this.pipe(a),e){var o,p=function(c,l){return a.removeListener("error",p),a.removeListener("response",o),e.call(this,c,l)};o=p.bind(this,null),a.on("error",p),a.on("response",o)}}.bind(this)),a};ce.prototype._error=function(t){this.error||(this.error=t,this.pause(),this.emit("error",t))};ce.prototype.toString=function(){return"[object FormData]"};R0(ce,"FormData");vm.exports=ce});var Nm=A(Dm=>{"use strict";var H0=require("url").parse,V0={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},Z0=String.prototype.endsWith||function(t){return t.length<=this.length&&this.indexOf(t,this.length-t.length)!==-1};function G0(t){var e=typeof t=="string"?H0(t):t||{},a=e.protocol,s=e.host,r=e.port;if(typeof s!="string"||!s||typeof a!="string"||(a=a.split(":",1)[0],s=s.replace(/:\d*$/,""),r=parseInt(r)||V0[a]||0,!J0(s,r)))return"";var n=nr("npm_config_"+a+"_proxy")||nr(a+"_proxy")||nr("npm_config_proxy")||nr("all_proxy");return n&&n.indexOf("://")===-1&&(n=a+"://"+n),n}function J0(t,e){var a=(nr("npm_config_no_proxy")||nr("no_proxy")).toLowerCase();return a?a==="*"?!1:a.split(/[,\s]/).every(function(s){if(!s)return!0;var r=s.match(/^(.+):(\d+)$/),n=r?r[1]:s,i=r?parseInt(r[2]):0;return i&&i!==e?!0:/^[.*]/.test(n)?(n.charAt(0)==="*"&&(n=n.slice(1)),!Z0.call(t,n)):t!==n}):!0}function nr(t){return process.env[t.toLowerCase()]||process.env[t.toUpperCase()]||""}Dm.getProxyForUrl=G0});var Lm=A((dT,Fm)=>{var ir=1e3,or=ir*60,cr=or*60,Ta=cr*24,K0=Ta*7,Q0=Ta*365.25;Fm.exports=function(t,e){e=e||{};var a=typeof t;if(a==="string"&&t.length>0)return W0(t);if(a==="number"&&isFinite(t))return e.long?Y0(t):X0(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function W0(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var a=parseFloat(e[1]),s=(e[2]||"ms").toLowerCase();switch(s){case"years":case"year":case"yrs":case"yr":case"y":return a*Q0;case"weeks":case"week":case"w":return a*K0;case"days":case"day":case"d":return a*Ta;case"hours":case"hour":case"hrs":case"hr":case"h":return a*cr;case"minutes":case"minute":case"mins":case"min":case"m":return a*or;case"seconds":case"second":case"secs":case"sec":case"s":return a*ir;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}function X0(t){var e=Math.abs(t);return e>=Ta?Math.round(t/Ta)+"d":e>=cr?Math.round(t/cr)+"h":e>=or?Math.round(t/or)+"m":e>=ir?Math.round(t/ir)+"s":t+"ms"}function Y0(t){var e=Math.abs(t);return e>=Ta?pn(t,e,Ta,"day"):e>=cr?pn(t,e,cr,"hour"):e>=or?pn(t,e,or,"minute"):e>=ir?pn(t,e,ir,"second"):t+" ms"}function pn(t,e,a,s){var r=e>=a*1.5;return Math.round(t/a)+" "+s+(r?"s":"")}});var to=A((mT,$m)=>{function e_(t){a.debug=a,a.default=a,a.coerce=p,a.disable=i,a.enable=r,a.enabled=o,a.humanize=Lm(),a.destroy=c,Object.keys(t).forEach(l=>{a[l]=t[l]}),a.names=[],a.skips=[],a.formatters={};function e(l){let m=0;for(let x=0;x<l.length;x++)m=(m<<5)-m+l.charCodeAt(x),m|=0;return a.colors[Math.abs(m)%a.colors.length]}a.selectColor=e;function a(l){let m,x=null,d,f;function g(...h){if(!g.enabled)return;let w=g,R=Number(new Date),E=R-(m||R);w.diff=E,w.prev=m,w.curr=R,m=R,h[0]=a.coerce(h[0]),typeof h[0]!="string"&&h.unshift("%O");let k=0;h[0]=h[0].replace(/%([a-zA-Z%])/g,(C,B)=>{if(C==="%%")return"%";k++;let U=a.formatters[B];if(typeof U=="function"){let I=h[k];C=U.call(w,I),h.splice(k,1),k--}return C}),a.formatArgs.call(w,h),(w.log||a.log).apply(w,h)}return g.namespace=l,g.useColors=a.useColors(),g.color=a.selectColor(l),g.extend=s,g.destroy=a.destroy,Object.defineProperty(g,"enabled",{enumerable:!0,configurable:!1,get:()=>x!==null?x:(d!==a.namespaces&&(d=a.namespaces,f=a.enabled(l)),f),set:h=>{x=h}}),typeof a.init=="function"&&a.init(g),g}function s(l,m){let x=a(this.namespace+(typeof m>"u"?":":m)+l);return x.log=this.log,x}function r(l){a.save(l),a.namespaces=l,a.names=[],a.skips=[];let m=(typeof l=="string"?l:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(let x of m)x[0]==="-"?a.skips.push(x.slice(1)):a.names.push(x)}function n(l,m){let x=0,d=0,f=-1,g=0;for(;x<l.length;)if(d<m.length&&(m[d]===l[x]||m[d]==="*"))m[d]==="*"?(f=d,g=x,d++):(x++,d++);else if(f!==-1)d=f+1,g++,x=g;else return!1;for(;d<m.length&&m[d]==="*";)d++;return d===m.length}function i(){let l=[...a.names,...a.skips.map(m=>"-"+m)].join(",");return a.enable(""),l}function o(l){for(let m of a.skips)if(n(l,m))return!1;for(let m of a.names)if(n(l,m))return!0;return!1}function p(l){return l instanceof Error?l.stack||l.message:l}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return a.enable(a.load()),a}$m.exports=e_});var qm=A((Ye,un)=>{Ye.formatArgs=a_;Ye.save=r_;Ye.load=s_;Ye.useColors=t_;Ye.storage=n_();Ye.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();Ye.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function t_(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function a_(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+un.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let a=0,s=0;t[0].replace(/%[a-zA-Z%]/g,r=>{r!=="%%"&&(a++,r==="%c"&&(s=a))}),t.splice(s,0,e)}Ye.log=console.debug||console.log||(()=>{});function r_(t){try{t?Ye.storage.setItem("debug",t):Ye.storage.removeItem("debug")}catch{}}function s_(){let t;try{t=Ye.storage.getItem("debug")||Ye.storage.getItem("DEBUG")}catch{}return!t&&typeof process<"u"&&"env"in process&&(t=process.env.DEBUG),t}function n_(){try{return localStorage}catch{}}un.exports=to()(Ye);var{formatters:i_}=un.exports;i_.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var zm=A((fT,Um)=>{"use strict";Um.exports=(t,e=process.argv)=>{let a=t.startsWith("-")?"":t.length===1?"-":"--",s=e.indexOf(a+t),r=e.indexOf("--");return s!==-1&&(r===-1||s<r)}});var Hm=A((hT,Bm)=>{"use strict";var o_=require("os"),Mm=require("tty"),mt=zm(),{env:$e}=process,Yt;mt("no-color")||mt("no-colors")||mt("color=false")||mt("color=never")?Yt=0:(mt("color")||mt("colors")||mt("color=true")||mt("color=always"))&&(Yt=1);"FORCE_COLOR"in $e&&($e.FORCE_COLOR==="true"?Yt=1:$e.FORCE_COLOR==="false"?Yt=0:Yt=$e.FORCE_COLOR.length===0?1:Math.min(parseInt($e.FORCE_COLOR,10),3));function ao(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function ro(t,e){if(Yt===0)return 0;if(mt("color=16m")||mt("color=full")||mt("color=truecolor"))return 3;if(mt("color=256"))return 2;if(t&&!e&&Yt===void 0)return 0;let a=Yt||0;if($e.TERM==="dumb")return a;if(process.platform==="win32"){let s=o_.release().split(".");return Number(s[0])>=10&&Number(s[2])>=10586?Number(s[2])>=14931?3:2:1}if("CI"in $e)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(s=>s in $e)||$e.CI_NAME==="codeship"?1:a;if("TEAMCITY_VERSION"in $e)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test($e.TEAMCITY_VERSION)?1:0;if($e.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in $e){let s=parseInt(($e.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch($e.TERM_PROGRAM){case"iTerm.app":return s>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test($e.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test($e.TERM)||"COLORTERM"in $e?1:a}function c_(t){let e=ro(t,t&&t.isTTY);return ao(e)}Bm.exports={supportsColor:c_,stdout:ao(ro(!0,Mm.isatty(1))),stderr:ao(ro(!0,Mm.isatty(2)))}});var Zm=A((qe,mn)=>{var l_=require("tty"),dn=require("util");qe.init=v_;qe.log=m_;qe.formatArgs=u_;qe.save=f_;qe.load=h_;qe.useColors=p_;qe.destroy=dn.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");qe.colors=[6,2,3,4,5,1];try{let t=Hm();t&&(t.stderr||t).level>=2&&(qe.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}qe.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let a=e.substring(6).toLowerCase().replace(/_([a-z])/g,(r,n)=>n.toUpperCase()),s=process.env[e];return/^(yes|on|true|enabled)$/i.test(s)?s=!0:/^(no|off|false|disabled)$/i.test(s)?s=!1:s==="null"?s=null:s=Number(s),t[a]=s,t},{});function p_(){return"colors"in qe.inspectOpts?!!qe.inspectOpts.colors:l_.isatty(process.stderr.fd)}function u_(t){let{namespace:e,useColors:a}=this;if(a){let s=this.color,r="\x1B[3"+(s<8?s:"8;5;"+s),n=`  ${r};1m${e} \x1B[0m`;t[0]=n+t[0].split(`
`).join(`
`+n),t.push(r+"m+"+mn.exports.humanize(this.diff)+"\x1B[0m")}else t[0]=d_()+e+" "+t[0]}function d_(){return qe.inspectOpts.hideDate?"":new Date().toISOString()+" "}function m_(...t){return process.stderr.write(dn.formatWithOptions(qe.inspectOpts,...t)+`
`)}function f_(t){t?process.env.DEBUG=t:delete process.env.DEBUG}function h_(){return process.env.DEBUG}function v_(t){t.inspectOpts={};let e=Object.keys(qe.inspectOpts);for(let a=0;a<e.length;a++)t.inspectOpts[e[a]]=qe.inspectOpts[e[a]]}mn.exports=to()(qe);var{formatters:Vm}=mn.exports;Vm.o=function(t){return this.inspectOpts.colors=this.useColors,dn.inspect(t,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};Vm.O=function(t){return this.inspectOpts.colors=this.useColors,dn.inspect(t,this.inspectOpts)}});var Gm=A((vT,so)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?so.exports=qm():so.exports=Zm()});var Km=A((xT,Jm)=>{var Fr;Jm.exports=function(){if(!Fr){try{Fr=Gm()("follow-redirects")}catch{}typeof Fr!="function"&&(Fr=function(){})}Fr.apply(null,arguments)}});var ef=A((gT,xo)=>{var $r=require("url"),Lr=$r.URL,x_=require("http"),g_=require("https"),lo=require("stream").Writable,po=require("assert"),Qm=Km();(function(){var e=typeof process<"u",a=typeof window<"u"&&typeof document<"u",s=Ca(Error.captureStackTrace);!e&&(a||!s)&&console.warn("The follow-redirects package should be excluded from browser builds.")})();var uo=!1;try{po(new Lr(""))}catch(t){uo=t.code==="ERR_INVALID_URL"}var y_=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],mo=["abort","aborted","connect","error","socket","timeout"],fo=Object.create(null);mo.forEach(function(t){fo[t]=function(e,a,s){this._redirectable.emit(t,e,a,s)}});var io=qr("ERR_INVALID_URL","Invalid URL",TypeError),oo=qr("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),b_=qr("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",oo),__=qr("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),w_=qr("ERR_STREAM_WRITE_AFTER_END","write after end"),E_=lo.prototype.destroy||Xm;function et(t,e){lo.call(this),this._sanitizeOptions(t),this._options=t,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],e&&this.on("response",e);var a=this;this._onNativeResponse=function(s){try{a._processResponse(s)}catch(r){a.emit("error",r instanceof oo?r:new oo({cause:r}))}},this._performRequest()}et.prototype=Object.create(lo.prototype);et.prototype.abort=function(){vo(this._currentRequest),this._currentRequest.abort(),this.emit("abort")};et.prototype.destroy=function(t){return vo(this._currentRequest,t),E_.call(this,t),this};et.prototype.write=function(t,e,a){if(this._ending)throw new w_;if(!Aa(t)&&!R_(t))throw new TypeError("data should be a string, Buffer or Uint8Array");if(Ca(e)&&(a=e,e=null),t.length===0){a&&a();return}this._requestBodyLength+t.length<=this._options.maxBodyLength?(this._requestBodyLength+=t.length,this._requestBodyBuffers.push({data:t,encoding:e}),this._currentRequest.write(t,e,a)):(this.emit("error",new __),this.abort())};et.prototype.end=function(t,e,a){if(Ca(t)?(a=t,t=e=null):Ca(e)&&(a=e,e=null),!t)this._ended=this._ending=!0,this._currentRequest.end(null,null,a);else{var s=this,r=this._currentRequest;this.write(t,e,function(){s._ended=!0,r.end(null,null,a)}),this._ending=!0}};et.prototype.setHeader=function(t,e){this._options.headers[t]=e,this._currentRequest.setHeader(t,e)};et.prototype.removeHeader=function(t){delete this._options.headers[t],this._currentRequest.removeHeader(t)};et.prototype.setTimeout=function(t,e){var a=this;function s(i){i.setTimeout(t),i.removeListener("timeout",i.destroy),i.addListener("timeout",i.destroy)}function r(i){a._timeout&&clearTimeout(a._timeout),a._timeout=setTimeout(function(){a.emit("timeout"),n()},t),s(i)}function n(){a._timeout&&(clearTimeout(a._timeout),a._timeout=null),a.removeListener("abort",n),a.removeListener("error",n),a.removeListener("response",n),a.removeListener("close",n),e&&a.removeListener("timeout",e),a.socket||a._currentRequest.removeListener("socket",r)}return e&&this.on("timeout",e),this.socket?r(this.socket):this._currentRequest.once("socket",r),this.on("socket",s),this.on("abort",n),this.on("error",n),this.on("response",n),this.on("close",n),this};["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(t){et.prototype[t]=function(e,a){return this._currentRequest[t](e,a)}});["aborted","connection","socket"].forEach(function(t){Object.defineProperty(et.prototype,t,{get:function(){return this._currentRequest[t]}})});et.prototype._sanitizeOptions=function(t){if(t.headers||(t.headers={}),t.host&&(t.hostname||(t.hostname=t.host),delete t.host),!t.pathname&&t.path){var e=t.path.indexOf("?");e<0?t.pathname=t.path:(t.pathname=t.path.substring(0,e),t.search=t.path.substring(e))}};et.prototype._performRequest=function(){var t=this._options.protocol,e=this._options.nativeProtocols[t];if(!e)throw new TypeError("Unsupported protocol "+t);if(this._options.agents){var a=t.slice(0,-1);this._options.agent=this._options.agents[a]}var s=this._currentRequest=e.request(this._options,this._onNativeResponse);s._redirectable=this;for(var r of mo)s.on(r,fo[r]);if(this._currentUrl=/^\//.test(this._options.path)?$r.format(this._options):this._options.path,this._isRedirect){var n=0,i=this,o=this._requestBodyBuffers;(function p(c){if(s===i._currentRequest)if(c)i.emit("error",c);else if(n<o.length){var l=o[n++];s.finished||s.write(l.data,l.encoding,p)}else i._ended&&s.end()})()}};et.prototype._processResponse=function(t){var e=t.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:t.headers,statusCode:e});var a=t.headers.location;if(!a||this._options.followRedirects===!1||e<300||e>=400){t.responseUrl=this._currentUrl,t.redirects=this._redirects,this.emit("response",t),this._requestBodyBuffers=[];return}if(vo(this._currentRequest),t.destroy(),++this._redirectCount>this._options.maxRedirects)throw new b_;var s,r=this._options.beforeRedirect;r&&(s=Object.assign({Host:t.req.getHeader("host")},this._options.headers));var n=this._options.method;((e===301||e===302)&&this._options.method==="POST"||e===303&&!/^(?:GET|HEAD)$/.test(this._options.method))&&(this._options.method="GET",this._requestBodyBuffers=[],no(/^content-/i,this._options.headers));var i=no(/^host$/i,this._options.headers),o=ho(this._currentUrl),p=i||o.host,c=/^\w+:/.test(a)?this._currentUrl:$r.format(Object.assign(o,{host:p})),l=S_(a,c);if(Qm("redirecting to",l.href),this._isRedirect=!0,co(l,this._options),(l.protocol!==o.protocol&&l.protocol!=="https:"||l.host!==p&&!P_(l.host,p))&&no(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),Ca(r)){var m={headers:t.headers,statusCode:e},x={url:c,method:n,headers:s};r(this._options,m,x),this._sanitizeOptions(this._options)}this._performRequest()};function Wm(t){var e={maxRedirects:21,maxBodyLength:10485760},a={};return Object.keys(t).forEach(function(s){var r=s+":",n=a[r]=t[s],i=e[s]=Object.create(n);function o(c,l,m){return k_(c)?c=co(c):Aa(c)?c=co(ho(c)):(m=l,l=Ym(c),c={protocol:r}),Ca(l)&&(m=l,l=null),l=Object.assign({maxRedirects:e.maxRedirects,maxBodyLength:e.maxBodyLength},c,l),l.nativeProtocols=a,!Aa(l.host)&&!Aa(l.hostname)&&(l.hostname="::1"),po.equal(l.protocol,r,"protocol mismatch"),Qm("options",l),new et(l,m)}function p(c,l,m){var x=i.request(c,l,m);return x.end(),x}Object.defineProperties(i,{request:{value:o,configurable:!0,enumerable:!0,writable:!0},get:{value:p,configurable:!0,enumerable:!0,writable:!0}})}),e}function Xm(){}function ho(t){var e;if(uo)e=new Lr(t);else if(e=Ym($r.parse(t)),!Aa(e.protocol))throw new io({input:t});return e}function S_(t,e){return uo?new Lr(t,e):ho($r.resolve(e,t))}function Ym(t){if(/^\[/.test(t.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(t.hostname))throw new io({input:t.href||t});if(/^\[/.test(t.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(t.host))throw new io({input:t.href||t});return t}function co(t,e){var a=e||{};for(var s of y_)a[s]=t[s];return a.hostname.startsWith("[")&&(a.hostname=a.hostname.slice(1,-1)),a.port!==""&&(a.port=Number(a.port)),a.path=a.search?a.pathname+a.search:a.pathname,a}function no(t,e){var a;for(var s in e)t.test(s)&&(a=e[s],delete e[s]);return a===null||typeof a>"u"?void 0:String(a).trim()}function qr(t,e,a){function s(r){Ca(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,r||{}),this.code=t,this.message=this.cause?e+": "+this.cause.message:e}return s.prototype=new(a||Error),Object.defineProperties(s.prototype,{constructor:{value:s,enumerable:!1},name:{value:"Error ["+t+"]",enumerable:!1}}),s}function vo(t,e){for(var a of mo)t.removeListener(a,fo[a]);t.on("error",Xm),t.destroy(e)}function P_(t,e){po(Aa(t)&&Aa(e));var a=t.length-e.length-1;return a>0&&t[a]==="."&&t.endsWith(e)}function Aa(t){return typeof t=="string"||t instanceof String}function Ca(t){return typeof t=="function"}function R_(t){return typeof t=="object"&&"length"in t}function k_(t){return Lr&&t instanceof Lr}xo.exports=Wm({http:x_,https:g_});xo.exports.wrap=Wm});var u={};Ho(u,{BRAND:()=>$h,DIRTY:()=>ia,EMPTY_PATH:()=>vh,INVALID:()=>W,NEVER:()=>wv,OK:()=>He,ParseStatus:()=>ze,Schema:()=>te,ZodAny:()=>Zt,ZodArray:()=>Ut,ZodBigInt:()=>ca,ZodBoolean:()=>la,ZodBranded:()=>br,ZodCatch:()=>ba,ZodDate:()=>pa,ZodDefault:()=>ya,ZodDiscriminatedUnion:()=>Wr,ZodEffects:()=>pt,ZodEnum:()=>xa,ZodError:()=>Je,ZodFirstPartyTypeKind:()=>N,ZodFunction:()=>Yr,ZodIntersection:()=>fa,ZodIssueCode:()=>j,ZodLazy:()=>ha,ZodLiteral:()=>va,ZodMap:()=>Za,ZodNaN:()=>Ja,ZodNativeEnum:()=>ga,ZodNever:()=>vt,ZodNull:()=>da,ZodNullable:()=>kt,ZodNumber:()=>oa,ZodObject:()=>Ke,ZodOptional:()=>ct,ZodParsedType:()=>z,ZodPipeline:()=>_r,ZodPromise:()=>Gt,ZodReadonly:()=>_a,ZodRecord:()=>Xr,ZodSchema:()=>te,ZodSet:()=>Ga,ZodString:()=>Vt,ZodSymbol:()=>Ha,ZodTransformer:()=>pt,ZodTuple:()=>Rt,ZodType:()=>te,ZodUndefined:()=>ua,ZodUnion:()=>ma,ZodUnknown:()=>qt,ZodVoid:()=>Va,addIssueToContext:()=>$,any:()=>Gh,array:()=>Wh,bigint:()=>Mh,boolean:()=>tc,coerce:()=>_v,custom:()=>Xo,date:()=>Bh,datetimeRegex:()=>Qo,defaultErrorMap:()=>Lt,discriminatedUnion:()=>tv,effect:()=>mv,enum:()=>pv,function:()=>ov,getErrorMap:()=>za,getParsedType:()=>Pt,instanceof:()=>Uh,intersection:()=>av,isAborted:()=>Kr,isAsync:()=>Ma,isDirty:()=>Qr,isValid:()=>Ht,late:()=>qh,lazy:()=>cv,literal:()=>lv,makeIssue:()=>yr,map:()=>nv,nan:()=>zh,nativeEnum:()=>uv,never:()=>Kh,null:()=>Zh,nullable:()=>hv,number:()=>ec,object:()=>Xh,objectUtil:()=>In,oboolean:()=>bv,onumber:()=>yv,optional:()=>fv,ostring:()=>gv,pipeline:()=>xv,preprocess:()=>vv,promise:()=>dv,quotelessJson:()=>mh,record:()=>sv,set:()=>iv,setErrorMap:()=>hh,strictObject:()=>Yh,string:()=>Yo,symbol:()=>Hh,transformer:()=>mv,tuple:()=>rv,undefined:()=>Vh,union:()=>ev,unknown:()=>Jh,util:()=>se,void:()=>Qh});var se;(function(t){t.assertEqual=r=>{};function e(r){}t.assertIs=e;function a(r){throw new Error}t.assertNever=a,t.arrayToEnum=r=>{let n={};for(let i of r)n[i]=i;return n},t.getValidEnumValues=r=>{let n=t.objectKeys(r).filter(o=>typeof r[r[o]]!="number"),i={};for(let o of n)i[o]=r[o];return t.objectValues(i)},t.objectValues=r=>t.objectKeys(r).map(function(n){return r[n]}),t.objectKeys=typeof Object.keys=="function"?r=>Object.keys(r):r=>{let n=[];for(let i in r)Object.prototype.hasOwnProperty.call(r,i)&&n.push(i);return n},t.find=(r,n)=>{for(let i of r)if(n(i))return i},t.isInteger=typeof Number.isInteger=="function"?r=>Number.isInteger(r):r=>typeof r=="number"&&Number.isFinite(r)&&Math.floor(r)===r;function s(r,n=" | "){return r.map(i=>typeof i=="string"?`'${i}'`:i).join(n)}t.joinValues=s,t.jsonStringifyReplacer=(r,n)=>typeof n=="bigint"?n.toString():n})(se||(se={}));var In;(function(t){t.mergeShapes=(e,a)=>({...e,...a})})(In||(In={}));var z=se.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Pt=t=>{switch(typeof t){case"undefined":return z.undefined;case"string":return z.string;case"number":return Number.isNaN(t)?z.nan:z.number;case"boolean":return z.boolean;case"function":return z.function;case"bigint":return z.bigint;case"symbol":return z.symbol;case"object":return Array.isArray(t)?z.array:t===null?z.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?z.promise:typeof Map<"u"&&t instanceof Map?z.map:typeof Set<"u"&&t instanceof Set?z.set:typeof Date<"u"&&t instanceof Date?z.date:z.object;default:return z.unknown}};var j=se.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),mh=t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:"),Je=class t extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};let a=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,a):this.__proto__=a,this.name="ZodError",this.issues=e}format(e){let a=e||function(n){return n.message},s={_errors:[]},r=n=>{for(let i of n.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)s._errors.push(a(i));else{let o=s,p=0;for(;p<i.path.length;){let c=i.path[p];p===i.path.length-1?(o[c]=o[c]||{_errors:[]},o[c]._errors.push(a(i))):o[c]=o[c]||{_errors:[]},o=o[c],p++}}};return r(this),s}static assert(e){if(!(e instanceof t))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,se.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=a=>a.message){let a={},s=[];for(let r of this.issues)if(r.path.length>0){let n=r.path[0];a[n]=a[n]||[],a[n].push(e(r))}else s.push(e(r));return{formErrors:s,fieldErrors:a}}get formErrors(){return this.flatten()}};Je.create=t=>new Je(t);var fh=(t,e)=>{let a;switch(t.code){case j.invalid_type:t.received===z.undefined?a="Required":a=`Expected ${t.expected}, received ${t.received}`;break;case j.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(t.expected,se.jsonStringifyReplacer)}`;break;case j.unrecognized_keys:a=`Unrecognized key(s) in object: ${se.joinValues(t.keys,", ")}`;break;case j.invalid_union:a="Invalid input";break;case j.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${se.joinValues(t.options)}`;break;case j.invalid_enum_value:a=`Invalid enum value. Expected ${se.joinValues(t.options)}, received '${t.received}'`;break;case j.invalid_arguments:a="Invalid function arguments";break;case j.invalid_return_type:a="Invalid function return type";break;case j.invalid_date:a="Invalid date";break;case j.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(a=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(a=`${a} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?a=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?a=`Invalid input: must end with "${t.validation.endsWith}"`:se.assertNever(t.validation):t.validation!=="regex"?a=`Invalid ${t.validation}`:a="Invalid";break;case j.too_small:t.type==="array"?a=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?a=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?a=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="bigint"?a=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?a=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:a="Invalid input";break;case j.too_big:t.type==="array"?a=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?a=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?a=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?a=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?a=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:a="Invalid input";break;case j.custom:a="Invalid input";break;case j.invalid_intersection_types:a="Intersection results could not be merged";break;case j.not_multiple_of:a=`Number must be a multiple of ${t.multipleOf}`;break;case j.not_finite:a="Number must be finite";break;default:a=e.defaultError,se.assertNever(t)}return{message:a}},Lt=fh;var Vo=Lt;function hh(t){Vo=t}function za(){return Vo}var yr=t=>{let{data:e,path:a,errorMaps:s,issueData:r}=t,n=[...a,...r.path||[]],i={...r,path:n};if(r.message!==void 0)return{...r,path:n,message:r.message};let o="",p=s.filter(c=>!!c).slice().reverse();for(let c of p)o=c(i,{data:e,defaultError:o}).message;return{...r,path:n,message:o}},vh=[];function $(t,e){let a=za(),s=yr({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,a,a===Lt?void 0:Lt].filter(r=>!!r)});t.common.issues.push(s)}var ze=class t{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,a){let s=[];for(let r of a){if(r.status==="aborted")return W;r.status==="dirty"&&e.dirty(),s.push(r.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,a){let s=[];for(let r of a){let n=await r.key,i=await r.value;s.push({key:n,value:i})}return t.mergeObjectSync(e,s)}static mergeObjectSync(e,a){let s={};for(let r of a){let{key:n,value:i}=r;if(n.status==="aborted"||i.status==="aborted")return W;n.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),n.value!=="__proto__"&&(typeof i.value<"u"||r.alwaysSet)&&(s[n.value]=i.value)}return{status:e.value,value:s}}},W=Object.freeze({status:"aborted"}),ia=t=>({status:"dirty",value:t}),He=t=>({status:"valid",value:t}),Kr=t=>t.status==="aborted",Qr=t=>t.status==="dirty",Ht=t=>t.status==="valid",Ma=t=>typeof Promise<"u"&&t instanceof Promise;var J;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(J||(J={}));var lt=class{constructor(e,a,s,r){this._cachedPath=[],this.parent=e,this.data=a,this._path=s,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}},Zo=(t,e)=>{if(Ht(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let a=new Je(t.common.issues);return this._error=a,this._error}}};function ee(t){if(!t)return{};let{errorMap:e,invalid_type_error:a,required_error:s,description:r}=t;if(e&&(a||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(i,o)=>{let{message:p}=t;return i.code==="invalid_enum_value"?{message:p??o.defaultError}:typeof o.data>"u"?{message:p??s??o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:p??a??o.defaultError}},description:r}}var te=class{get description(){return this._def.description}_getType(e){return Pt(e.data)}_getOrReturnCtx(e,a){return a||{common:e.parent.common,data:e.data,parsedType:Pt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ze,ctx:{common:e.parent.common,data:e.data,parsedType:Pt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let a=this._parse(e);if(Ma(a))throw new Error("Synchronous parse encountered promise.");return a}_parseAsync(e){let a=this._parse(e);return Promise.resolve(a)}parse(e,a){let s=this.safeParse(e,a);if(s.success)return s.data;throw s.error}safeParse(e,a){let s={common:{issues:[],async:(a==null?void 0:a.async)??!1,contextualErrorMap:a==null?void 0:a.errorMap},path:(a==null?void 0:a.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)},r=this._parseSync({data:e,path:s.path,parent:s});return Zo(s,r)}"~validate"(e){var s,r;let a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)};if(!this["~standard"].async)try{let n=this._parseSync({data:e,path:[],parent:a});return Ht(n)?{value:n.value}:{issues:a.common.issues}}catch(n){(r=(s=n==null?void 0:n.message)==null?void 0:s.toLowerCase())!=null&&r.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then(n=>Ht(n)?{value:n.value}:{issues:a.common.issues})}async parseAsync(e,a){let s=await this.safeParseAsync(e,a);if(s.success)return s.data;throw s.error}async safeParseAsync(e,a){let s={common:{issues:[],contextualErrorMap:a==null?void 0:a.errorMap,async:!0},path:(a==null?void 0:a.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)},r=this._parse({data:e,path:s.path,parent:s}),n=await(Ma(r)?r:Promise.resolve(r));return Zo(s,n)}refine(e,a){let s=r=>typeof a=="string"||typeof a>"u"?{message:a}:typeof a=="function"?a(r):a;return this._refinement((r,n)=>{let i=e(r),o=()=>n.addIssue({code:j.custom,...s(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(p=>p?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,a){return this._refinement((s,r)=>e(s)?!0:(r.addIssue(typeof a=="function"?a(s,r):a),!1))}_refinement(e){return new pt({schema:this,typeName:N.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:a=>this["~validate"](a)}}optional(){return ct.create(this,this._def)}nullable(){return kt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Ut.create(this)}promise(){return Gt.create(this,this._def)}or(e){return ma.create([this,e],this._def)}and(e){return fa.create(this,e,this._def)}transform(e){return new pt({...ee(this._def),schema:this,typeName:N.ZodEffects,effect:{type:"transform",transform:e}})}default(e){let a=typeof e=="function"?e:()=>e;return new ya({...ee(this._def),innerType:this,defaultValue:a,typeName:N.ZodDefault})}brand(){return new br({typeName:N.ZodBranded,type:this,...ee(this._def)})}catch(e){let a=typeof e=="function"?e:()=>e;return new ba({...ee(this._def),innerType:this,catchValue:a,typeName:N.ZodCatch})}describe(e){let a=this.constructor;return new a({...this._def,description:e})}pipe(e){return _r.create(this,e)}readonly(){return _a.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}},xh=/^c[^\s-]{8,}$/i,gh=/^[0-9a-z]+$/,yh=/^[0-9A-HJKMNP-TV-Z]{26}$/i,bh=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,_h=/^[a-z0-9_-]{21}$/i,wh=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Eh=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Sh=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Ph="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",Dn,Rh=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,kh=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Th=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ah=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Ch=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Oh=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Jo="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",jh=new RegExp(`^${Jo}$`);function Ko(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);let a=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${a}`}function Ih(t){return new RegExp(`^${Ko(t)}$`)}function Qo(t){let e=`${Jo}T${Ko(t)}`,a=[];return a.push(t.local?"Z?":"Z"),t.offset&&a.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${a.join("|")})`,new RegExp(`^${e}$`)}function Dh(t,e){return!!((e==="v4"||!e)&&Rh.test(t)||(e==="v6"||!e)&&Th.test(t))}function Nh(t,e){if(!wh.test(t))return!1;try{let[a]=t.split(".");if(!a)return!1;let s=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),r=JSON.parse(atob(s));return!(typeof r!="object"||r===null||"typ"in r&&(r==null?void 0:r.typ)!=="JWT"||!r.alg||e&&r.alg!==e)}catch{return!1}}function Fh(t,e){return!!((e==="v4"||!e)&&kh.test(t)||(e==="v6"||!e)&&Ah.test(t))}var Vt=class t extends te{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==z.string){let n=this._getOrReturnCtx(e);return $(n,{code:j.invalid_type,expected:z.string,received:n.parsedType}),W}let s=new ze,r;for(let n of this._def.checks)if(n.kind==="min")e.data.length<n.value&&(r=this._getOrReturnCtx(e,r),$(r,{code:j.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),s.dirty());else if(n.kind==="max")e.data.length>n.value&&(r=this._getOrReturnCtx(e,r),$(r,{code:j.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),s.dirty());else if(n.kind==="length"){let i=e.data.length>n.value,o=e.data.length<n.value;(i||o)&&(r=this._getOrReturnCtx(e,r),i?$(r,{code:j.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}):o&&$(r,{code:j.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}),s.dirty())}else if(n.kind==="email")Sh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"email",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="emoji")Dn||(Dn=new RegExp(Ph,"u")),Dn.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"emoji",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="uuid")bh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"uuid",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="nanoid")_h.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"nanoid",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="cuid")xh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"cuid",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="cuid2")gh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"cuid2",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="ulid")yh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"ulid",code:j.invalid_string,message:n.message}),s.dirty());else if(n.kind==="url")try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),$(r,{validation:"url",code:j.invalid_string,message:n.message}),s.dirty()}else n.kind==="regex"?(n.regex.lastIndex=0,n.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"regex",code:j.invalid_string,message:n.message}),s.dirty())):n.kind==="trim"?e.data=e.data.trim():n.kind==="includes"?e.data.includes(n.value,n.position)||(r=this._getOrReturnCtx(e,r),$(r,{code:j.invalid_string,validation:{includes:n.value,position:n.position},message:n.message}),s.dirty()):n.kind==="toLowerCase"?e.data=e.data.toLowerCase():n.kind==="toUpperCase"?e.data=e.data.toUpperCase():n.kind==="startsWith"?e.data.startsWith(n.value)||(r=this._getOrReturnCtx(e,r),$(r,{code:j.invalid_string,validation:{startsWith:n.value},message:n.message}),s.dirty()):n.kind==="endsWith"?e.data.endsWith(n.value)||(r=this._getOrReturnCtx(e,r),$(r,{code:j.invalid_string,validation:{endsWith:n.value},message:n.message}),s.dirty()):n.kind==="datetime"?Qo(n).test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{code:j.invalid_string,validation:"datetime",message:n.message}),s.dirty()):n.kind==="date"?jh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{code:j.invalid_string,validation:"date",message:n.message}),s.dirty()):n.kind==="time"?Ih(n).test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{code:j.invalid_string,validation:"time",message:n.message}),s.dirty()):n.kind==="duration"?Eh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"duration",code:j.invalid_string,message:n.message}),s.dirty()):n.kind==="ip"?Dh(e.data,n.version)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"ip",code:j.invalid_string,message:n.message}),s.dirty()):n.kind==="jwt"?Nh(e.data,n.alg)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"jwt",code:j.invalid_string,message:n.message}),s.dirty()):n.kind==="cidr"?Fh(e.data,n.version)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"cidr",code:j.invalid_string,message:n.message}),s.dirty()):n.kind==="base64"?Ch.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"base64",code:j.invalid_string,message:n.message}),s.dirty()):n.kind==="base64url"?Oh.test(e.data)||(r=this._getOrReturnCtx(e,r),$(r,{validation:"base64url",code:j.invalid_string,message:n.message}),s.dirty()):se.assertNever(n);return{status:s.value,value:e.data}}_regex(e,a,s){return this.refinement(r=>e.test(r),{validation:a,code:j.invalid_string,...J.errToObj(s)})}_addCheck(e){return new t({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...J.errToObj(e)})}url(e){return this._addCheck({kind:"url",...J.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...J.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...J.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...J.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...J.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...J.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...J.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...J.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...J.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...J.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...J.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...J.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(e==null?void 0:e.offset)??!1,local:(e==null?void 0:e.local)??!1,...J.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...J.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...J.errToObj(e)})}regex(e,a){return this._addCheck({kind:"regex",regex:e,...J.errToObj(a)})}includes(e,a){return this._addCheck({kind:"includes",value:e,position:a==null?void 0:a.position,...J.errToObj(a==null?void 0:a.message)})}startsWith(e,a){return this._addCheck({kind:"startsWith",value:e,...J.errToObj(a)})}endsWith(e,a){return this._addCheck({kind:"endsWith",value:e,...J.errToObj(a)})}min(e,a){return this._addCheck({kind:"min",value:e,...J.errToObj(a)})}max(e,a){return this._addCheck({kind:"max",value:e,...J.errToObj(a)})}length(e,a){return this._addCheck({kind:"length",value:e,...J.errToObj(a)})}nonempty(e){return this.min(1,J.errToObj(e))}trim(){return new t({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new t({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new t({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(let a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e}get maxLength(){let e=null;for(let a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e}};Vt.create=t=>new Vt({checks:[],typeName:N.ZodString,coerce:(t==null?void 0:t.coerce)??!1,...ee(t)});function Lh(t,e){let a=(t.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,r=a>s?a:s,n=Number.parseInt(t.toFixed(r).replace(".","")),i=Number.parseInt(e.toFixed(r).replace(".",""));return n%i/10**r}var oa=class t extends te{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==z.number){let n=this._getOrReturnCtx(e);return $(n,{code:j.invalid_type,expected:z.number,received:n.parsedType}),W}let s,r=new ze;for(let n of this._def.checks)n.kind==="int"?se.isInteger(e.data)||(s=this._getOrReturnCtx(e,s),$(s,{code:j.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(s=this._getOrReturnCtx(e,s),$(s,{code:j.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(s=this._getOrReturnCtx(e,s),$(s,{code:j.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):n.kind==="multipleOf"?Lh(e.data,n.value)!==0&&(s=this._getOrReturnCtx(e,s),$(s,{code:j.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):n.kind==="finite"?Number.isFinite(e.data)||(s=this._getOrReturnCtx(e,s),$(s,{code:j.not_finite,message:n.message}),r.dirty()):se.assertNever(n);return{status:r.value,value:e.data}}gte(e,a){return this.setLimit("min",e,!0,J.toString(a))}gt(e,a){return this.setLimit("min",e,!1,J.toString(a))}lte(e,a){return this.setLimit("max",e,!0,J.toString(a))}lt(e,a){return this.setLimit("max",e,!1,J.toString(a))}setLimit(e,a,s,r){return new t({...this._def,checks:[...this._def.checks,{kind:e,value:a,inclusive:s,message:J.toString(r)}]})}_addCheck(e){return new t({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:J.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:J.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:J.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:J.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:J.toString(e)})}multipleOf(e,a){return this._addCheck({kind:"multipleOf",value:e,message:J.toString(a)})}finite(e){return this._addCheck({kind:"finite",message:J.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:J.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:J.toString(e)})}get minValue(){let e=null;for(let a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e}get maxValue(){let e=null;for(let a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&se.isInteger(e.value))}get isFinite(){let e=null,a=null;for(let s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(a===null||s.value>a)&&(a=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(a)&&Number.isFinite(e)}};oa.create=t=>new oa({checks:[],typeName:N.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...ee(t)});var ca=class t extends te{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==z.bigint)return this._getInvalidInput(e);let s,r=new ze;for(let n of this._def.checks)n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(s=this._getOrReturnCtx(e,s),$(s,{code:j.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(s=this._getOrReturnCtx(e,s),$(s,{code:j.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):n.kind==="multipleOf"?e.data%n.value!==BigInt(0)&&(s=this._getOrReturnCtx(e,s),$(s,{code:j.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):se.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let a=this._getOrReturnCtx(e);return $(a,{code:j.invalid_type,expected:z.bigint,received:a.parsedType}),W}gte(e,a){return this.setLimit("min",e,!0,J.toString(a))}gt(e,a){return this.setLimit("min",e,!1,J.toString(a))}lte(e,a){return this.setLimit("max",e,!0,J.toString(a))}lt(e,a){return this.setLimit("max",e,!1,J.toString(a))}setLimit(e,a,s,r){return new t({...this._def,checks:[...this._def.checks,{kind:e,value:a,inclusive:s,message:J.toString(r)}]})}_addCheck(e){return new t({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:J.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:J.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:J.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:J.toString(e)})}multipleOf(e,a){return this._addCheck({kind:"multipleOf",value:e,message:J.toString(a)})}get minValue(){let e=null;for(let a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e}get maxValue(){let e=null;for(let a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e}};ca.create=t=>new ca({checks:[],typeName:N.ZodBigInt,coerce:(t==null?void 0:t.coerce)??!1,...ee(t)});var la=class extends te{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==z.boolean){let s=this._getOrReturnCtx(e);return $(s,{code:j.invalid_type,expected:z.boolean,received:s.parsedType}),W}return He(e.data)}};la.create=t=>new la({typeName:N.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...ee(t)});var pa=class t extends te{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==z.date){let n=this._getOrReturnCtx(e);return $(n,{code:j.invalid_type,expected:z.date,received:n.parsedType}),W}if(Number.isNaN(e.data.getTime())){let n=this._getOrReturnCtx(e);return $(n,{code:j.invalid_date}),W}let s=new ze,r;for(let n of this._def.checks)n.kind==="min"?e.data.getTime()<n.value&&(r=this._getOrReturnCtx(e,r),$(r,{code:j.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),s.dirty()):n.kind==="max"?e.data.getTime()>n.value&&(r=this._getOrReturnCtx(e,r),$(r,{code:j.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),s.dirty()):se.assertNever(n);return{status:s.value,value:new Date(e.data.getTime())}}_addCheck(e){return new t({...this._def,checks:[...this._def.checks,e]})}min(e,a){return this._addCheck({kind:"min",value:e.getTime(),message:J.toString(a)})}max(e,a){return this._addCheck({kind:"max",value:e.getTime(),message:J.toString(a)})}get minDate(){let e=null;for(let a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(let a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e!=null?new Date(e):null}};pa.create=t=>new pa({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:N.ZodDate,...ee(t)});var Ha=class extends te{_parse(e){if(this._getType(e)!==z.symbol){let s=this._getOrReturnCtx(e);return $(s,{code:j.invalid_type,expected:z.symbol,received:s.parsedType}),W}return He(e.data)}};Ha.create=t=>new Ha({typeName:N.ZodSymbol,...ee(t)});var ua=class extends te{_parse(e){if(this._getType(e)!==z.undefined){let s=this._getOrReturnCtx(e);return $(s,{code:j.invalid_type,expected:z.undefined,received:s.parsedType}),W}return He(e.data)}};ua.create=t=>new ua({typeName:N.ZodUndefined,...ee(t)});var da=class extends te{_parse(e){if(this._getType(e)!==z.null){let s=this._getOrReturnCtx(e);return $(s,{code:j.invalid_type,expected:z.null,received:s.parsedType}),W}return He(e.data)}};da.create=t=>new da({typeName:N.ZodNull,...ee(t)});var Zt=class extends te{constructor(){super(...arguments),this._any=!0}_parse(e){return He(e.data)}};Zt.create=t=>new Zt({typeName:N.ZodAny,...ee(t)});var qt=class extends te{constructor(){super(...arguments),this._unknown=!0}_parse(e){return He(e.data)}};qt.create=t=>new qt({typeName:N.ZodUnknown,...ee(t)});var vt=class extends te{_parse(e){let a=this._getOrReturnCtx(e);return $(a,{code:j.invalid_type,expected:z.never,received:a.parsedType}),W}};vt.create=t=>new vt({typeName:N.ZodNever,...ee(t)});var Va=class extends te{_parse(e){if(this._getType(e)!==z.undefined){let s=this._getOrReturnCtx(e);return $(s,{code:j.invalid_type,expected:z.void,received:s.parsedType}),W}return He(e.data)}};Va.create=t=>new Va({typeName:N.ZodVoid,...ee(t)});var Ut=class t extends te{_parse(e){let{ctx:a,status:s}=this._processInputParams(e),r=this._def;if(a.parsedType!==z.array)return $(a,{code:j.invalid_type,expected:z.array,received:a.parsedType}),W;if(r.exactLength!==null){let i=a.data.length>r.exactLength.value,o=a.data.length<r.exactLength.value;(i||o)&&($(a,{code:i?j.too_big:j.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),s.dirty())}if(r.minLength!==null&&a.data.length<r.minLength.value&&($(a,{code:j.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),s.dirty()),r.maxLength!==null&&a.data.length>r.maxLength.value&&($(a,{code:j.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),s.dirty()),a.common.async)return Promise.all([...a.data].map((i,o)=>r.type._parseAsync(new lt(a,i,a.path,o)))).then(i=>ze.mergeArray(s,i));let n=[...a.data].map((i,o)=>r.type._parseSync(new lt(a,i,a.path,o)));return ze.mergeArray(s,n)}get element(){return this._def.type}min(e,a){return new t({...this._def,minLength:{value:e,message:J.toString(a)}})}max(e,a){return new t({...this._def,maxLength:{value:e,message:J.toString(a)}})}length(e,a){return new t({...this._def,exactLength:{value:e,message:J.toString(a)}})}nonempty(e){return this.min(1,e)}};Ut.create=(t,e)=>new Ut({type:t,minLength:null,maxLength:null,exactLength:null,typeName:N.ZodArray,...ee(e)});function Ba(t){if(t instanceof Ke){let e={};for(let a in t.shape){let s=t.shape[a];e[a]=ct.create(Ba(s))}return new Ke({...t._def,shape:()=>e})}else return t instanceof Ut?new Ut({...t._def,type:Ba(t.element)}):t instanceof ct?ct.create(Ba(t.unwrap())):t instanceof kt?kt.create(Ba(t.unwrap())):t instanceof Rt?Rt.create(t.items.map(e=>Ba(e))):t}var Ke=class t extends te{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;let e=this._def.shape(),a=se.objectKeys(e);return this._cached={shape:e,keys:a},this._cached}_parse(e){if(this._getType(e)!==z.object){let c=this._getOrReturnCtx(e);return $(c,{code:j.invalid_type,expected:z.object,received:c.parsedType}),W}let{status:s,ctx:r}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof vt&&this._def.unknownKeys==="strip"))for(let c in r.data)i.includes(c)||o.push(c);let p=[];for(let c of i){let l=n[c],m=r.data[c];p.push({key:{status:"valid",value:c},value:l._parse(new lt(r,m,r.path,c)),alwaysSet:c in r.data})}if(this._def.catchall instanceof vt){let c=this._def.unknownKeys;if(c==="passthrough")for(let l of o)p.push({key:{status:"valid",value:l},value:{status:"valid",value:r.data[l]}});else if(c==="strict")o.length>0&&($(r,{code:j.unrecognized_keys,keys:o}),s.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{let c=this._def.catchall;for(let l of o){let m=r.data[l];p.push({key:{status:"valid",value:l},value:c._parse(new lt(r,m,r.path,l)),alwaysSet:l in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let c=[];for(let l of p){let m=await l.key,x=await l.value;c.push({key:m,value:x,alwaysSet:l.alwaysSet})}return c}).then(c=>ze.mergeObjectSync(s,c)):ze.mergeObjectSync(s,p)}get shape(){return this._def.shape()}strict(e){return J.errToObj,new t({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(a,s)=>{var n,i;let r=((i=(n=this._def).errorMap)==null?void 0:i.call(n,a,s).message)??s.defaultError;return a.code==="unrecognized_keys"?{message:J.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new t({...this._def,unknownKeys:"strip"})}passthrough(){return new t({...this._def,unknownKeys:"passthrough"})}extend(e){return new t({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new t({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:N.ZodObject})}setKey(e,a){return this.augment({[e]:a})}catchall(e){return new t({...this._def,catchall:e})}pick(e){let a={};for(let s of se.objectKeys(e))e[s]&&this.shape[s]&&(a[s]=this.shape[s]);return new t({...this._def,shape:()=>a})}omit(e){let a={};for(let s of se.objectKeys(this.shape))e[s]||(a[s]=this.shape[s]);return new t({...this._def,shape:()=>a})}deepPartial(){return Ba(this)}partial(e){let a={};for(let s of se.objectKeys(this.shape)){let r=this.shape[s];e&&!e[s]?a[s]=r:a[s]=r.optional()}return new t({...this._def,shape:()=>a})}required(e){let a={};for(let s of se.objectKeys(this.shape))if(e&&!e[s])a[s]=this.shape[s];else{let n=this.shape[s];for(;n instanceof ct;)n=n._def.innerType;a[s]=n}return new t({...this._def,shape:()=>a})}keyof(){return Wo(se.objectKeys(this.shape))}};Ke.create=(t,e)=>new Ke({shape:()=>t,unknownKeys:"strip",catchall:vt.create(),typeName:N.ZodObject,...ee(e)});Ke.strictCreate=(t,e)=>new Ke({shape:()=>t,unknownKeys:"strict",catchall:vt.create(),typeName:N.ZodObject,...ee(e)});Ke.lazycreate=(t,e)=>new Ke({shape:t,unknownKeys:"strip",catchall:vt.create(),typeName:N.ZodObject,...ee(e)});var ma=class extends te{_parse(e){let{ctx:a}=this._processInputParams(e),s=this._def.options;function r(n){for(let o of n)if(o.result.status==="valid")return o.result;for(let o of n)if(o.result.status==="dirty")return a.common.issues.push(...o.ctx.common.issues),o.result;let i=n.map(o=>new Je(o.ctx.common.issues));return $(a,{code:j.invalid_union,unionErrors:i}),W}if(a.common.async)return Promise.all(s.map(async n=>{let i={...a,common:{...a.common,issues:[]},parent:null};return{result:await n._parseAsync({data:a.data,path:a.path,parent:i}),ctx:i}})).then(r);{let n,i=[];for(let p of s){let c={...a,common:{...a.common,issues:[]},parent:null},l=p._parseSync({data:a.data,path:a.path,parent:c});if(l.status==="valid")return l;l.status==="dirty"&&!n&&(n={result:l,ctx:c}),c.common.issues.length&&i.push(c.common.issues)}if(n)return a.common.issues.push(...n.ctx.common.issues),n.result;let o=i.map(p=>new Je(p));return $(a,{code:j.invalid_union,unionErrors:o}),W}}get options(){return this._def.options}};ma.create=(t,e)=>new ma({options:t,typeName:N.ZodUnion,...ee(e)});var $t=t=>t instanceof ha?$t(t.schema):t instanceof pt?$t(t.innerType()):t instanceof va?[t.value]:t instanceof xa?t.options:t instanceof ga?se.objectValues(t.enum):t instanceof ya?$t(t._def.innerType):t instanceof ua?[void 0]:t instanceof da?[null]:t instanceof ct?[void 0,...$t(t.unwrap())]:t instanceof kt?[null,...$t(t.unwrap())]:t instanceof br||t instanceof _a?$t(t.unwrap()):t instanceof ba?$t(t._def.innerType):[],Wr=class t extends te{_parse(e){let{ctx:a}=this._processInputParams(e);if(a.parsedType!==z.object)return $(a,{code:j.invalid_type,expected:z.object,received:a.parsedType}),W;let s=this.discriminator,r=a.data[s],n=this.optionsMap.get(r);return n?a.common.async?n._parseAsync({data:a.data,path:a.path,parent:a}):n._parseSync({data:a.data,path:a.path,parent:a}):($(a,{code:j.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),W)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,a,s){let r=new Map;for(let n of a){let i=$t(n.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,n)}}return new t({typeName:N.ZodDiscriminatedUnion,discriminator:e,options:a,optionsMap:r,...ee(s)})}};function Nn(t,e){let a=Pt(t),s=Pt(e);if(t===e)return{valid:!0,data:t};if(a===z.object&&s===z.object){let r=se.objectKeys(e),n=se.objectKeys(t).filter(o=>r.indexOf(o)!==-1),i={...t,...e};for(let o of n){let p=Nn(t[o],e[o]);if(!p.valid)return{valid:!1};i[o]=p.data}return{valid:!0,data:i}}else if(a===z.array&&s===z.array){if(t.length!==e.length)return{valid:!1};let r=[];for(let n=0;n<t.length;n++){let i=t[n],o=e[n],p=Nn(i,o);if(!p.valid)return{valid:!1};r.push(p.data)}return{valid:!0,data:r}}else return a===z.date&&s===z.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}var fa=class extends te{_parse(e){let{status:a,ctx:s}=this._processInputParams(e),r=(n,i)=>{if(Kr(n)||Kr(i))return W;let o=Nn(n.value,i.value);return o.valid?((Qr(n)||Qr(i))&&a.dirty(),{status:a.value,value:o.data}):($(s,{code:j.invalid_intersection_types}),W)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([n,i])=>r(n,i)):r(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}};fa.create=(t,e,a)=>new fa({left:t,right:e,typeName:N.ZodIntersection,...ee(a)});var Rt=class t extends te{_parse(e){let{status:a,ctx:s}=this._processInputParams(e);if(s.parsedType!==z.array)return $(s,{code:j.invalid_type,expected:z.array,received:s.parsedType}),W;if(s.data.length<this._def.items.length)return $(s,{code:j.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),W;!this._def.rest&&s.data.length>this._def.items.length&&($(s,{code:j.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),a.dirty());let n=[...s.data].map((i,o)=>{let p=this._def.items[o]||this._def.rest;return p?p._parse(new lt(s,i,s.path,o)):null}).filter(i=>!!i);return s.common.async?Promise.all(n).then(i=>ze.mergeArray(a,i)):ze.mergeArray(a,n)}get items(){return this._def.items}rest(e){return new t({...this._def,rest:e})}};Rt.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Rt({items:t,typeName:N.ZodTuple,rest:null,...ee(e)})};var Xr=class t extends te{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:a,ctx:s}=this._processInputParams(e);if(s.parsedType!==z.object)return $(s,{code:j.invalid_type,expected:z.object,received:s.parsedType}),W;let r=[],n=this._def.keyType,i=this._def.valueType;for(let o in s.data)r.push({key:n._parse(new lt(s,o,s.path,o)),value:i._parse(new lt(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?ze.mergeObjectAsync(a,r):ze.mergeObjectSync(a,r)}get element(){return this._def.valueType}static create(e,a,s){return a instanceof te?new t({keyType:e,valueType:a,typeName:N.ZodRecord,...ee(s)}):new t({keyType:Vt.create(),valueType:e,typeName:N.ZodRecord,...ee(a)})}},Za=class extends te{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:a,ctx:s}=this._processInputParams(e);if(s.parsedType!==z.map)return $(s,{code:j.invalid_type,expected:z.map,received:s.parsedType}),W;let r=this._def.keyType,n=this._def.valueType,i=[...s.data.entries()].map(([o,p],c)=>({key:r._parse(new lt(s,o,s.path,[c,"key"])),value:n._parse(new lt(s,p,s.path,[c,"value"]))}));if(s.common.async){let o=new Map;return Promise.resolve().then(async()=>{for(let p of i){let c=await p.key,l=await p.value;if(c.status==="aborted"||l.status==="aborted")return W;(c.status==="dirty"||l.status==="dirty")&&a.dirty(),o.set(c.value,l.value)}return{status:a.value,value:o}})}else{let o=new Map;for(let p of i){let c=p.key,l=p.value;if(c.status==="aborted"||l.status==="aborted")return W;(c.status==="dirty"||l.status==="dirty")&&a.dirty(),o.set(c.value,l.value)}return{status:a.value,value:o}}}};Za.create=(t,e,a)=>new Za({valueType:e,keyType:t,typeName:N.ZodMap,...ee(a)});var Ga=class t extends te{_parse(e){let{status:a,ctx:s}=this._processInputParams(e);if(s.parsedType!==z.set)return $(s,{code:j.invalid_type,expected:z.set,received:s.parsedType}),W;let r=this._def;r.minSize!==null&&s.data.size<r.minSize.value&&($(s,{code:j.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),a.dirty()),r.maxSize!==null&&s.data.size>r.maxSize.value&&($(s,{code:j.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),a.dirty());let n=this._def.valueType;function i(p){let c=new Set;for(let l of p){if(l.status==="aborted")return W;l.status==="dirty"&&a.dirty(),c.add(l.value)}return{status:a.value,value:c}}let o=[...s.data.values()].map((p,c)=>n._parse(new lt(s,p,s.path,c)));return s.common.async?Promise.all(o).then(p=>i(p)):i(o)}min(e,a){return new t({...this._def,minSize:{value:e,message:J.toString(a)}})}max(e,a){return new t({...this._def,maxSize:{value:e,message:J.toString(a)}})}size(e,a){return this.min(e,a).max(e,a)}nonempty(e){return this.min(1,e)}};Ga.create=(t,e)=>new Ga({valueType:t,minSize:null,maxSize:null,typeName:N.ZodSet,...ee(e)});var Yr=class t extends te{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:a}=this._processInputParams(e);if(a.parsedType!==z.function)return $(a,{code:j.invalid_type,expected:z.function,received:a.parsedType}),W;function s(o,p){return yr({data:o,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,za(),Lt].filter(c=>!!c),issueData:{code:j.invalid_arguments,argumentsError:p}})}function r(o,p){return yr({data:o,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,za(),Lt].filter(c=>!!c),issueData:{code:j.invalid_return_type,returnTypeError:p}})}let n={errorMap:a.common.contextualErrorMap},i=a.data;if(this._def.returns instanceof Gt){let o=this;return He(async function(...p){let c=new Je([]),l=await o._def.args.parseAsync(p,n).catch(d=>{throw c.addIssue(s(p,d)),c}),m=await Reflect.apply(i,this,l);return await o._def.returns._def.type.parseAsync(m,n).catch(d=>{throw c.addIssue(r(m,d)),c})})}else{let o=this;return He(function(...p){let c=o._def.args.safeParse(p,n);if(!c.success)throw new Je([s(p,c.error)]);let l=Reflect.apply(i,this,c.data),m=o._def.returns.safeParse(l,n);if(!m.success)throw new Je([r(l,m.error)]);return m.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new t({...this._def,args:Rt.create(e).rest(qt.create())})}returns(e){return new t({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,a,s){return new t({args:e||Rt.create([]).rest(qt.create()),returns:a||qt.create(),typeName:N.ZodFunction,...ee(s)})}},ha=class extends te{get schema(){return this._def.getter()}_parse(e){let{ctx:a}=this._processInputParams(e);return this._def.getter()._parse({data:a.data,path:a.path,parent:a})}};ha.create=(t,e)=>new ha({getter:t,typeName:N.ZodLazy,...ee(e)});var va=class extends te{_parse(e){if(e.data!==this._def.value){let a=this._getOrReturnCtx(e);return $(a,{received:a.data,code:j.invalid_literal,expected:this._def.value}),W}return{status:"valid",value:e.data}}get value(){return this._def.value}};va.create=(t,e)=>new va({value:t,typeName:N.ZodLiteral,...ee(e)});function Wo(t,e){return new xa({values:t,typeName:N.ZodEnum,...ee(e)})}var xa=class t extends te{_parse(e){if(typeof e.data!="string"){let a=this._getOrReturnCtx(e),s=this._def.values;return $(a,{expected:se.joinValues(s),received:a.parsedType,code:j.invalid_type}),W}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let a=this._getOrReturnCtx(e),s=this._def.values;return $(a,{received:a.data,code:j.invalid_enum_value,options:s}),W}return He(e.data)}get options(){return this._def.values}get enum(){let e={};for(let a of this._def.values)e[a]=a;return e}get Values(){let e={};for(let a of this._def.values)e[a]=a;return e}get Enum(){let e={};for(let a of this._def.values)e[a]=a;return e}extract(e,a=this._def){return t.create(e,{...this._def,...a})}exclude(e,a=this._def){return t.create(this.options.filter(s=>!e.includes(s)),{...this._def,...a})}};xa.create=Wo;var ga=class extends te{_parse(e){let a=se.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==z.string&&s.parsedType!==z.number){let r=se.objectValues(a);return $(s,{expected:se.joinValues(r),received:s.parsedType,code:j.invalid_type}),W}if(this._cache||(this._cache=new Set(se.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let r=se.objectValues(a);return $(s,{received:s.data,code:j.invalid_enum_value,options:r}),W}return He(e.data)}get enum(){return this._def.values}};ga.create=(t,e)=>new ga({values:t,typeName:N.ZodNativeEnum,...ee(e)});var Gt=class extends te{unwrap(){return this._def.type}_parse(e){let{ctx:a}=this._processInputParams(e);if(a.parsedType!==z.promise&&a.common.async===!1)return $(a,{code:j.invalid_type,expected:z.promise,received:a.parsedType}),W;let s=a.parsedType===z.promise?a.data:Promise.resolve(a.data);return He(s.then(r=>this._def.type.parseAsync(r,{path:a.path,errorMap:a.common.contextualErrorMap})))}};Gt.create=(t,e)=>new Gt({type:t,typeName:N.ZodPromise,...ee(e)});var pt=class extends te{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===N.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:a,ctx:s}=this._processInputParams(e),r=this._def.effect||null,n={addIssue:i=>{$(s,i),i.fatal?a.abort():a.dirty()},get path(){return s.path}};if(n.addIssue=n.addIssue.bind(n),r.type==="preprocess"){let i=r.transform(s.data,n);if(s.common.async)return Promise.resolve(i).then(async o=>{if(a.value==="aborted")return W;let p=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return p.status==="aborted"?W:p.status==="dirty"?ia(p.value):a.value==="dirty"?ia(p.value):p});{if(a.value==="aborted")return W;let o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?W:o.status==="dirty"?ia(o.value):a.value==="dirty"?ia(o.value):o}}if(r.type==="refinement"){let i=o=>{let p=r.refinement(o,n);if(s.common.async)return Promise.resolve(p);if(p instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){let o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?W:(o.status==="dirty"&&a.dirty(),i(o.value),{status:a.value,value:o.value})}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?W:(o.status==="dirty"&&a.dirty(),i(o.value).then(()=>({status:a.value,value:o.value}))))}if(r.type==="transform")if(s.common.async===!1){let i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!Ht(i))return W;let o=r.transform(i.value,n);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:a.value,value:o}}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>Ht(i)?Promise.resolve(r.transform(i.value,n)).then(o=>({status:a.value,value:o})):W);se.assertNever(r)}};pt.create=(t,e,a)=>new pt({schema:t,typeName:N.ZodEffects,effect:e,...ee(a)});pt.createWithPreprocess=(t,e,a)=>new pt({schema:e,effect:{type:"preprocess",transform:t},typeName:N.ZodEffects,...ee(a)});var ct=class extends te{_parse(e){return this._getType(e)===z.undefined?He(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};ct.create=(t,e)=>new ct({innerType:t,typeName:N.ZodOptional,...ee(e)});var kt=class extends te{_parse(e){return this._getType(e)===z.null?He(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};kt.create=(t,e)=>new kt({innerType:t,typeName:N.ZodNullable,...ee(e)});var ya=class extends te{_parse(e){let{ctx:a}=this._processInputParams(e),s=a.data;return a.parsedType===z.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:a.path,parent:a})}removeDefault(){return this._def.innerType}};ya.create=(t,e)=>new ya({innerType:t,typeName:N.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ee(e)});var ba=class extends te{_parse(e){let{ctx:a}=this._processInputParams(e),s={...a,common:{...a.common,issues:[]}},r=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return Ma(r)?r.then(n=>({status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new Je(s.common.issues)},input:s.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new Je(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}};ba.create=(t,e)=>new ba({innerType:t,typeName:N.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ee(e)});var Ja=class extends te{_parse(e){if(this._getType(e)!==z.nan){let s=this._getOrReturnCtx(e);return $(s,{code:j.invalid_type,expected:z.nan,received:s.parsedType}),W}return{status:"valid",value:e.data}}};Ja.create=t=>new Ja({typeName:N.ZodNaN,...ee(t)});var $h=Symbol("zod_brand"),br=class extends te{_parse(e){let{ctx:a}=this._processInputParams(e),s=a.data;return this._def.type._parse({data:s,path:a.path,parent:a})}unwrap(){return this._def.type}},_r=class t extends te{_parse(e){let{status:a,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{let n=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return n.status==="aborted"?W:n.status==="dirty"?(a.dirty(),ia(n.value)):this._def.out._parseAsync({data:n.value,path:s.path,parent:s})})();{let r=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return r.status==="aborted"?W:r.status==="dirty"?(a.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:s.path,parent:s})}}static create(e,a){return new t({in:e,out:a,typeName:N.ZodPipeline})}},_a=class extends te{_parse(e){let a=this._def.innerType._parse(e),s=r=>(Ht(r)&&(r.value=Object.freeze(r.value)),r);return Ma(a)?a.then(r=>s(r)):s(a)}unwrap(){return this._def.innerType}};_a.create=(t,e)=>new _a({innerType:t,typeName:N.ZodReadonly,...ee(e)});function Go(t,e){let a=typeof t=="function"?t(e):typeof t=="string"?{message:t}:t;return typeof a=="string"?{message:a}:a}function Xo(t,e={},a){return t?Zt.create().superRefine((s,r)=>{let n=t(s);if(n instanceof Promise)return n.then(i=>{if(!i){let o=Go(e,s),p=o.fatal??a??!0;r.addIssue({code:"custom",...o,fatal:p})}});if(!n){let i=Go(e,s),o=i.fatal??a??!0;r.addIssue({code:"custom",...i,fatal:o})}}):Zt.create()}var qh={object:Ke.lazycreate},N;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(N||(N={}));var Uh=(t,e={message:`Input not instance of ${t.name}`})=>Xo(a=>a instanceof t,e),Yo=Vt.create,ec=oa.create,zh=Ja.create,Mh=ca.create,tc=la.create,Bh=pa.create,Hh=Ha.create,Vh=ua.create,Zh=da.create,Gh=Zt.create,Jh=qt.create,Kh=vt.create,Qh=Va.create,Wh=Ut.create,Xh=Ke.create,Yh=Ke.strictCreate,ev=ma.create,tv=Wr.create,av=fa.create,rv=Rt.create,sv=Xr.create,nv=Za.create,iv=Ga.create,ov=Yr.create,cv=ha.create,lv=va.create,pv=xa.create,uv=ga.create,dv=Gt.create,mv=pt.create,fv=ct.create,hv=kt.create,vv=pt.createWithPreprocess,xv=_r.create,gv=()=>Yo().optional(),yv=()=>ec().optional(),bv=()=>tc().optional(),_v={string:t=>Vt.create({...t,coerce:!0}),number:t=>oa.create({...t,coerce:!0}),boolean:t=>la.create({...t,coerce:!0}),bigint:t=>ca.create({...t,coerce:!0}),date:t=>pa.create({...t,coerce:!0})};var wv=W;var Fn="2025-06-18";var ac=[Fn,"2025-03-26","2024-11-05","2024-10-07"],es="2.0",rc=u.union([u.string(),u.number().int()]),sc=u.string(),Ev=u.object({progressToken:u.optional(rc)}).passthrough(),ut=u.object({_meta:u.optional(Ev)}).passthrough(),Qe=u.object({method:u.string(),params:u.optional(ut)}),wr=u.object({_meta:u.optional(u.object({}).passthrough())}).passthrough(),Tt=u.object({method:u.string(),params:u.optional(wr)}),dt=u.object({_meta:u.optional(u.object({}).passthrough())}).passthrough(),ts=u.union([u.string(),u.number().int()]),nc=u.object({jsonrpc:u.literal(es),id:ts}).merge(Qe).strict(),ic=t=>nc.safeParse(t).success,oc=u.object({jsonrpc:u.literal(es)}).merge(Tt).strict(),cc=t=>oc.safeParse(t).success,lc=u.object({jsonrpc:u.literal(es),id:ts,result:dt}).strict(),Ln=t=>lc.safeParse(t).success,ge;(function(t){t[t.ConnectionClosed=-32e3]="ConnectionClosed",t[t.RequestTimeout=-32001]="RequestTimeout",t[t.ParseError=-32700]="ParseError",t[t.InvalidRequest=-32600]="InvalidRequest",t[t.MethodNotFound=-32601]="MethodNotFound",t[t.InvalidParams=-32602]="InvalidParams",t[t.InternalError=-32603]="InternalError"})(ge||(ge={}));var pc=u.object({jsonrpc:u.literal(es),id:ts,error:u.object({code:u.number().int(),message:u.string(),data:u.optional(u.unknown())})}).strict(),uc=t=>pc.safeParse(t).success,dc=u.union([nc,oc,lc,pc]),as=dt.strict(),rs=Tt.extend({method:u.literal("notifications/cancelled"),params:wr.extend({requestId:ts,reason:u.string().optional()})}),Er=u.object({name:u.string(),title:u.optional(u.string())}).passthrough(),mc=Er.extend({version:u.string()}),Sv=u.object({experimental:u.optional(u.object({}).passthrough()),sampling:u.optional(u.object({}).passthrough()),elicitation:u.optional(u.object({}).passthrough()),roots:u.optional(u.object({listChanged:u.optional(u.boolean())}).passthrough())}).passthrough(),$n=Qe.extend({method:u.literal("initialize"),params:ut.extend({protocolVersion:u.string(),capabilities:Sv,clientInfo:mc})});var Pv=u.object({experimental:u.optional(u.object({}).passthrough()),logging:u.optional(u.object({}).passthrough()),completions:u.optional(u.object({}).passthrough()),prompts:u.optional(u.object({listChanged:u.optional(u.boolean())}).passthrough()),resources:u.optional(u.object({subscribe:u.optional(u.boolean()),listChanged:u.optional(u.boolean())}).passthrough()),tools:u.optional(u.object({listChanged:u.optional(u.boolean())}).passthrough())}).passthrough(),Rv=dt.extend({protocolVersion:u.string(),capabilities:Pv,serverInfo:mc,instructions:u.optional(u.string())}),qn=Tt.extend({method:u.literal("notifications/initialized")});var ss=Qe.extend({method:u.literal("ping")}),kv=u.object({progress:u.number(),total:u.optional(u.number()),message:u.optional(u.string())}).passthrough(),ns=Tt.extend({method:u.literal("notifications/progress"),params:wr.merge(kv).extend({progressToken:rc})}),is=Qe.extend({params:ut.extend({cursor:u.optional(sc)}).optional()}),os=dt.extend({nextCursor:u.optional(sc)}),fc=u.object({uri:u.string(),mimeType:u.optional(u.string()),_meta:u.optional(u.object({}).passthrough())}).passthrough(),hc=fc.extend({text:u.string()}),vc=fc.extend({blob:u.string().base64()}),xc=Er.extend({uri:u.string(),description:u.optional(u.string()),mimeType:u.optional(u.string()),_meta:u.optional(u.object({}).passthrough())}),Tv=Er.extend({uriTemplate:u.string(),description:u.optional(u.string()),mimeType:u.optional(u.string()),_meta:u.optional(u.object({}).passthrough())}),cs=is.extend({method:u.literal("resources/list")}),Av=os.extend({resources:u.array(xc)}),ls=is.extend({method:u.literal("resources/templates/list")}),Cv=os.extend({resourceTemplates:u.array(Tv)}),ps=Qe.extend({method:u.literal("resources/read"),params:ut.extend({uri:u.string()})}),Ov=dt.extend({contents:u.array(u.union([hc,vc]))}),jv=Tt.extend({method:u.literal("notifications/resources/list_changed")}),Iv=Qe.extend({method:u.literal("resources/subscribe"),params:ut.extend({uri:u.string()})}),Dv=Qe.extend({method:u.literal("resources/unsubscribe"),params:ut.extend({uri:u.string()})}),Nv=Tt.extend({method:u.literal("notifications/resources/updated"),params:wr.extend({uri:u.string()})}),Fv=u.object({name:u.string(),description:u.optional(u.string()),required:u.optional(u.boolean())}).passthrough(),Lv=Er.extend({description:u.optional(u.string()),arguments:u.optional(u.array(Fv)),_meta:u.optional(u.object({}).passthrough())}),us=is.extend({method:u.literal("prompts/list")}),$v=os.extend({prompts:u.array(Lv)}),ds=Qe.extend({method:u.literal("prompts/get"),params:ut.extend({name:u.string(),arguments:u.optional(u.record(u.string()))})}),Un=u.object({type:u.literal("text"),text:u.string(),_meta:u.optional(u.object({}).passthrough())}).passthrough(),zn=u.object({type:u.literal("image"),data:u.string().base64(),mimeType:u.string(),_meta:u.optional(u.object({}).passthrough())}).passthrough(),Mn=u.object({type:u.literal("audio"),data:u.string().base64(),mimeType:u.string(),_meta:u.optional(u.object({}).passthrough())}).passthrough(),qv=u.object({type:u.literal("resource"),resource:u.union([hc,vc]),_meta:u.optional(u.object({}).passthrough())}).passthrough(),Uv=xc.extend({type:u.literal("resource_link")}),gc=u.union([Un,zn,Mn,Uv,qv]),zv=u.object({role:u.enum(["user","assistant"]),content:gc}).passthrough(),Mv=dt.extend({description:u.optional(u.string()),messages:u.array(zv)}),Bv=Tt.extend({method:u.literal("notifications/prompts/list_changed")}),Hv=u.object({title:u.optional(u.string()),readOnlyHint:u.optional(u.boolean()),destructiveHint:u.optional(u.boolean()),idempotentHint:u.optional(u.boolean()),openWorldHint:u.optional(u.boolean())}).passthrough(),Vv=Er.extend({description:u.optional(u.string()),inputSchema:u.object({type:u.literal("object"),properties:u.optional(u.object({}).passthrough()),required:u.optional(u.array(u.string()))}).passthrough(),outputSchema:u.optional(u.object({type:u.literal("object"),properties:u.optional(u.object({}).passthrough()),required:u.optional(u.array(u.string()))}).passthrough()),annotations:u.optional(Hv),_meta:u.optional(u.object({}).passthrough())}),ms=is.extend({method:u.literal("tools/list")}),Zv=os.extend({tools:u.array(Vv)}),yc=dt.extend({content:u.array(gc).default([]),structuredContent:u.object({}).passthrough().optional(),isError:u.optional(u.boolean())}),Xw=yc.or(dt.extend({toolResult:u.unknown()})),fs=Qe.extend({method:u.literal("tools/call"),params:ut.extend({name:u.string(),arguments:u.optional(u.record(u.unknown()))})}),Gv=Tt.extend({method:u.literal("notifications/tools/list_changed")}),bc=u.enum(["debug","info","notice","warning","error","critical","alert","emergency"]),Jv=Qe.extend({method:u.literal("logging/setLevel"),params:ut.extend({level:bc})}),Kv=Tt.extend({method:u.literal("notifications/message"),params:wr.extend({level:bc,logger:u.optional(u.string()),data:u.unknown()})}),Qv=u.object({name:u.string().optional()}).passthrough(),Wv=u.object({hints:u.optional(u.array(Qv)),costPriority:u.optional(u.number().min(0).max(1)),speedPriority:u.optional(u.number().min(0).max(1)),intelligencePriority:u.optional(u.number().min(0).max(1))}).passthrough(),Xv=u.object({role:u.enum(["user","assistant"]),content:u.union([Un,zn,Mn])}).passthrough(),Yv=Qe.extend({method:u.literal("sampling/createMessage"),params:ut.extend({messages:u.array(Xv),systemPrompt:u.optional(u.string()),includeContext:u.optional(u.enum(["none","thisServer","allServers"])),temperature:u.optional(u.number()),maxTokens:u.number().int(),stopSequences:u.optional(u.array(u.string())),metadata:u.optional(u.object({}).passthrough()),modelPreferences:u.optional(Wv)})}),Bn=dt.extend({model:u.string(),stopReason:u.optional(u.enum(["endTurn","stopSequence","maxTokens"]).or(u.string())),role:u.enum(["user","assistant"]),content:u.discriminatedUnion("type",[Un,zn,Mn])}),ex=u.object({type:u.literal("boolean"),title:u.optional(u.string()),description:u.optional(u.string()),default:u.optional(u.boolean())}).passthrough(),tx=u.object({type:u.literal("string"),title:u.optional(u.string()),description:u.optional(u.string()),minLength:u.optional(u.number()),maxLength:u.optional(u.number()),format:u.optional(u.enum(["email","uri","date","date-time"]))}).passthrough(),ax=u.object({type:u.enum(["number","integer"]),title:u.optional(u.string()),description:u.optional(u.string()),minimum:u.optional(u.number()),maximum:u.optional(u.number())}).passthrough(),rx=u.object({type:u.literal("string"),title:u.optional(u.string()),description:u.optional(u.string()),enum:u.array(u.string()),enumNames:u.optional(u.array(u.string()))}).passthrough(),sx=u.union([ex,tx,ax,rx]),nx=Qe.extend({method:u.literal("elicitation/create"),params:ut.extend({message:u.string(),requestedSchema:u.object({type:u.literal("object"),properties:u.record(u.string(),sx),required:u.optional(u.array(u.string()))}).passthrough()})}),Hn=dt.extend({action:u.enum(["accept","decline","cancel"]),content:u.optional(u.record(u.string(),u.unknown()))}),ix=u.object({type:u.literal("ref/resource"),uri:u.string()}).passthrough();var ox=u.object({type:u.literal("ref/prompt"),name:u.string()}).passthrough(),hs=Qe.extend({method:u.literal("completion/complete"),params:ut.extend({ref:u.union([ox,ix]),argument:u.object({name:u.string(),value:u.string()}).passthrough(),context:u.optional(u.object({arguments:u.optional(u.record(u.string(),u.string()))}))})}),cx=dt.extend({completion:u.object({values:u.array(u.string()).max(100),total:u.optional(u.number().int()),hasMore:u.optional(u.boolean())}).passthrough()}),lx=u.object({uri:u.string().startsWith("file://"),name:u.optional(u.string()),_meta:u.optional(u.object({}).passthrough())}).passthrough(),px=Qe.extend({method:u.literal("roots/list")}),Vn=dt.extend({roots:u.array(lx)}),ux=Tt.extend({method:u.literal("notifications/roots/list_changed")}),Yw=u.union([ss,$n,hs,Jv,ds,us,cs,ls,ps,Iv,Dv,fs,ms]),eE=u.union([rs,ns,qn,ux]),tE=u.union([as,Bn,Hn,Vn]),aE=u.union([ss,Yv,nx,px]),rE=u.union([rs,ns,Kv,Nv,jv,Gv,Bv]),sE=u.union([as,Rv,cx,Mv,$v,Av,Cv,Ov,yc,Zv]),ye=class extends Error{constructor(e,a,s){super(`MCP error ${e}: ${a}`),this.code=e,this.data=s,this.name="McpError"}};var dx=6e4,vs=class{constructor(e){this._options=e,this._requestMessageId=0,this._requestHandlers=new Map,this._requestHandlerAbortControllers=new Map,this._notificationHandlers=new Map,this._responseHandlers=new Map,this._progressHandlers=new Map,this._timeoutInfo=new Map,this._pendingDebouncedNotifications=new Set,this.setNotificationHandler(rs,a=>{let s=this._requestHandlerAbortControllers.get(a.params.requestId);s==null||s.abort(a.params.reason)}),this.setNotificationHandler(ns,a=>{this._onprogress(a)}),this.setRequestHandler(ss,a=>({}))}_setupTimeout(e,a,s,r,n=!1){this._timeoutInfo.set(e,{timeoutId:setTimeout(r,a),startTime:Date.now(),timeout:a,maxTotalTimeout:s,resetTimeoutOnProgress:n,onTimeout:r})}_resetTimeout(e){let a=this._timeoutInfo.get(e);if(!a)return!1;let s=Date.now()-a.startTime;if(a.maxTotalTimeout&&s>=a.maxTotalTimeout)throw this._timeoutInfo.delete(e),new ye(ge.RequestTimeout,"Maximum total timeout exceeded",{maxTotalTimeout:a.maxTotalTimeout,totalElapsed:s});return clearTimeout(a.timeoutId),a.timeoutId=setTimeout(a.onTimeout,a.timeout),!0}_cleanupTimeout(e){let a=this._timeoutInfo.get(e);a&&(clearTimeout(a.timeoutId),this._timeoutInfo.delete(e))}async connect(e){var a,s,r;this._transport=e;let n=(a=this.transport)===null||a===void 0?void 0:a.onclose;this._transport.onclose=()=>{n==null||n(),this._onclose()};let i=(s=this.transport)===null||s===void 0?void 0:s.onerror;this._transport.onerror=p=>{i==null||i(p),this._onerror(p)};let o=(r=this._transport)===null||r===void 0?void 0:r.onmessage;this._transport.onmessage=(p,c)=>{o==null||o(p,c),Ln(p)||uc(p)?this._onresponse(p):ic(p)?this._onrequest(p,c):cc(p)?this._onnotification(p):this._onerror(new Error(`Unknown message type: ${JSON.stringify(p)}`))},await this._transport.start()}_onclose(){var e;let a=this._responseHandlers;this._responseHandlers=new Map,this._progressHandlers.clear(),this._pendingDebouncedNotifications.clear(),this._transport=void 0,(e=this.onclose)===null||e===void 0||e.call(this);let s=new ye(ge.ConnectionClosed,"Connection closed");for(let r of a.values())r(s)}_onerror(e){var a;(a=this.onerror)===null||a===void 0||a.call(this,e)}_onnotification(e){var a;let s=(a=this._notificationHandlers.get(e.method))!==null&&a!==void 0?a:this.fallbackNotificationHandler;s!==void 0&&Promise.resolve().then(()=>s(e)).catch(r=>this._onerror(new Error(`Uncaught error in notification handler: ${r}`)))}_onrequest(e,a){var s,r,n,i;let o=(s=this._requestHandlers.get(e.method))!==null&&s!==void 0?s:this.fallbackRequestHandler;if(o===void 0){(r=this._transport)===null||r===void 0||r.send({jsonrpc:"2.0",id:e.id,error:{code:ge.MethodNotFound,message:"Method not found"}}).catch(l=>this._onerror(new Error(`Failed to send an error response: ${l}`)));return}let p=new AbortController;this._requestHandlerAbortControllers.set(e.id,p);let c={signal:p.signal,sessionId:(n=this._transport)===null||n===void 0?void 0:n.sessionId,_meta:(i=e.params)===null||i===void 0?void 0:i._meta,sendNotification:l=>this.notification(l,{relatedRequestId:e.id}),sendRequest:(l,m,x)=>this.request(l,m,{...x,relatedRequestId:e.id}),authInfo:a==null?void 0:a.authInfo,requestId:e.id,requestInfo:a==null?void 0:a.requestInfo};Promise.resolve().then(()=>o(e,c)).then(l=>{var m;if(!p.signal.aborted)return(m=this._transport)===null||m===void 0?void 0:m.send({result:l,jsonrpc:"2.0",id:e.id})},l=>{var m,x;if(!p.signal.aborted)return(m=this._transport)===null||m===void 0?void 0:m.send({jsonrpc:"2.0",id:e.id,error:{code:Number.isSafeInteger(l.code)?l.code:ge.InternalError,message:(x=l.message)!==null&&x!==void 0?x:"Internal error"}})}).catch(l=>this._onerror(new Error(`Failed to send response: ${l}`))).finally(()=>{this._requestHandlerAbortControllers.delete(e.id)})}_onprogress(e){let{progressToken:a,...s}=e.params,r=Number(a),n=this._progressHandlers.get(r);if(!n){this._onerror(new Error(`Received a progress notification for an unknown token: ${JSON.stringify(e)}`));return}let i=this._responseHandlers.get(r),o=this._timeoutInfo.get(r);if(o&&i&&o.resetTimeoutOnProgress)try{this._resetTimeout(r)}catch(p){i(p);return}n(s)}_onresponse(e){let a=Number(e.id),s=this._responseHandlers.get(a);if(s===void 0){this._onerror(new Error(`Received a response for an unknown message ID: ${JSON.stringify(e)}`));return}if(this._responseHandlers.delete(a),this._progressHandlers.delete(a),this._cleanupTimeout(a),Ln(e))s(e);else{let r=new ye(e.error.code,e.error.message,e.error.data);s(r)}}get transport(){return this._transport}async close(){var e;await((e=this._transport)===null||e===void 0?void 0:e.close())}request(e,a,s){let{relatedRequestId:r,resumptionToken:n,onresumptiontoken:i}=s??{};return new Promise((o,p)=>{var c,l,m,x,d,f;if(!this._transport){p(new Error("Not connected"));return}((c=this._options)===null||c===void 0?void 0:c.enforceStrictCapabilities)===!0&&this.assertCapabilityForMethod(e.method),(l=s==null?void 0:s.signal)===null||l===void 0||l.throwIfAborted();let g=this._requestMessageId++,h={...e,jsonrpc:"2.0",id:g};s!=null&&s.onprogress&&(this._progressHandlers.set(g,s.onprogress),h.params={...e.params,_meta:{...((m=e.params)===null||m===void 0?void 0:m._meta)||{},progressToken:g}});let w=k=>{var T;this._responseHandlers.delete(g),this._progressHandlers.delete(g),this._cleanupTimeout(g),(T=this._transport)===null||T===void 0||T.send({jsonrpc:"2.0",method:"notifications/cancelled",params:{requestId:g,reason:String(k)}},{relatedRequestId:r,resumptionToken:n,onresumptiontoken:i}).catch(C=>this._onerror(new Error(`Failed to send cancellation: ${C}`))),p(k)};this._responseHandlers.set(g,k=>{var T;if(!(!((T=s==null?void 0:s.signal)===null||T===void 0)&&T.aborted)){if(k instanceof Error)return p(k);try{let C=a.parse(k.result);o(C)}catch(C){p(C)}}}),(x=s==null?void 0:s.signal)===null||x===void 0||x.addEventListener("abort",()=>{var k;w((k=s==null?void 0:s.signal)===null||k===void 0?void 0:k.reason)});let R=(d=s==null?void 0:s.timeout)!==null&&d!==void 0?d:dx,E=()=>w(new ye(ge.RequestTimeout,"Request timed out",{timeout:R}));this._setupTimeout(g,R,s==null?void 0:s.maxTotalTimeout,E,(f=s==null?void 0:s.resetTimeoutOnProgress)!==null&&f!==void 0?f:!1),this._transport.send(h,{relatedRequestId:r,resumptionToken:n,onresumptiontoken:i}).catch(k=>{this._cleanupTimeout(g),p(k)})})}async notification(e,a){var s,r;if(!this._transport)throw new Error("Not connected");if(this.assertNotificationCapability(e.method),((r=(s=this._options)===null||s===void 0?void 0:s.debouncedNotificationMethods)!==null&&r!==void 0?r:[]).includes(e.method)&&!e.params&&!(a!=null&&a.relatedRequestId)){if(this._pendingDebouncedNotifications.has(e.method))return;this._pendingDebouncedNotifications.add(e.method),Promise.resolve().then(()=>{var p;if(this._pendingDebouncedNotifications.delete(e.method),!this._transport)return;let c={...e,jsonrpc:"2.0"};(p=this._transport)===null||p===void 0||p.send(c,a).catch(l=>this._onerror(l))});return}let o={...e,jsonrpc:"2.0"};await this._transport.send(o,a)}setRequestHandler(e,a){let s=e.shape.method.value;this.assertRequestHandlerCapability(s),this._requestHandlers.set(s,(r,n)=>Promise.resolve(a(e.parse(r),n)))}removeRequestHandler(e){this._requestHandlers.delete(e)}assertCanSetRequestHandler(e){if(this._requestHandlers.has(e))throw new Error(`A request handler for ${e} already exists, which would be overridden`)}setNotificationHandler(e,a){this._notificationHandlers.set(e.shape.method.value,s=>Promise.resolve(a(e.parse(s))))}removeNotificationHandler(e){this._notificationHandlers.delete(e)}};function _c(t,e){return Object.entries(e).reduce((a,[s,r])=>(r&&typeof r=="object"?a[s]=a[s]?{...a[s],...r}:r:a[s]=r,a),{...t})}var Ep=Be(wp(),1),Ns=class extends vs{constructor(e,a){var s;super(a),this._serverInfo=e,this._capabilities=(s=a==null?void 0:a.capabilities)!==null&&s!==void 0?s:{},this._instructions=a==null?void 0:a.instructions,this.setRequestHandler($n,r=>this._oninitialize(r)),this.setNotificationHandler(qn,()=>{var r;return(r=this.oninitialized)===null||r===void 0?void 0:r.call(this)})}registerCapabilities(e){if(this.transport)throw new Error("Cannot register capabilities after connecting to transport");this._capabilities=_c(this._capabilities,e)}assertCapabilityForMethod(e){var a,s,r;switch(e){case"sampling/createMessage":if(!(!((a=this._clientCapabilities)===null||a===void 0)&&a.sampling))throw new Error(`Client does not support sampling (required for ${e})`);break;case"elicitation/create":if(!(!((s=this._clientCapabilities)===null||s===void 0)&&s.elicitation))throw new Error(`Client does not support elicitation (required for ${e})`);break;case"roots/list":if(!(!((r=this._clientCapabilities)===null||r===void 0)&&r.roots))throw new Error(`Client does not support listing roots (required for ${e})`);break;case"ping":break}}assertNotificationCapability(e){switch(e){case"notifications/message":if(!this._capabilities.logging)throw new Error(`Server does not support logging (required for ${e})`);break;case"notifications/resources/updated":case"notifications/resources/list_changed":if(!this._capabilities.resources)throw new Error(`Server does not support notifying about resources (required for ${e})`);break;case"notifications/tools/list_changed":if(!this._capabilities.tools)throw new Error(`Server does not support notifying of tool list changes (required for ${e})`);break;case"notifications/prompts/list_changed":if(!this._capabilities.prompts)throw new Error(`Server does not support notifying of prompt list changes (required for ${e})`);break;case"notifications/cancelled":break;case"notifications/progress":break}}assertRequestHandlerCapability(e){switch(e){case"sampling/createMessage":if(!this._capabilities.sampling)throw new Error(`Server does not support sampling (required for ${e})`);break;case"logging/setLevel":if(!this._capabilities.logging)throw new Error(`Server does not support logging (required for ${e})`);break;case"prompts/get":case"prompts/list":if(!this._capabilities.prompts)throw new Error(`Server does not support prompts (required for ${e})`);break;case"resources/list":case"resources/templates/list":case"resources/read":if(!this._capabilities.resources)throw new Error(`Server does not support resources (required for ${e})`);break;case"tools/call":case"tools/list":if(!this._capabilities.tools)throw new Error(`Server does not support tools (required for ${e})`);break;case"ping":case"initialize":break}}async _oninitialize(e){let a=e.params.protocolVersion;return this._clientCapabilities=e.params.capabilities,this._clientVersion=e.params.clientInfo,{protocolVersion:ac.includes(a)?a:Fn,capabilities:this.getCapabilities(),serverInfo:this._serverInfo,...this._instructions&&{instructions:this._instructions}}}getClientCapabilities(){return this._clientCapabilities}getClientVersion(){return this._clientVersion}getCapabilities(){return this._capabilities}async ping(){return this.request({method:"ping"},as)}async createMessage(e,a){return this.request({method:"sampling/createMessage",params:e},Bn,a)}async elicitInput(e,a){let s=await this.request({method:"elicitation/create",params:e},Hn,a);if(s.action==="accept"&&s.content)try{let r=new Ep.default,n=r.compile(e.requestedSchema);if(!n(s.content))throw new ye(ge.InvalidParams,`Elicitation response content does not match requested schema: ${r.errorsText(n.errors)}`)}catch(r){throw r instanceof ye?r:new ye(ge.InternalError,`Error validating elicitation response: ${r}`)}return s}async listRoots(e,a){return this.request({method:"roots/list",params:e},Vn,a)}async sendLoggingMessage(e){return this.notification({method:"notifications/message",params:e})}async sendResourceUpdated(e){return this.notification({method:"notifications/resources/updated",params:e})}async sendResourceListChanged(){return this.notification({method:"notifications/resources/list_changed"})}async sendToolListChanged(){return this.notification({method:"notifications/tools/list_changed"})}async sendPromptListChanged(){return this.notification({method:"notifications/prompts/list_changed"})}};var Pp=Symbol("Let zodToJsonSchema decide on which parser to use");var Sp={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},Rp=t=>typeof t=="string"?{...Sp,name:t}:{...Sp,...t};var kp=t=>{let e=Rp(t),a=e.name!==void 0?[...e.basePath,e.definitionPath,e.name]:e.basePath;return{...e,flags:{hasReferencedOpenAiAnyType:!1},currentPath:a,propertyPath:void 0,seen:new Map(Object.entries(e.definitions).map(([s,r])=>[r._def,{def:r._def,path:[...e.basePath,e.definitionPath,s],jsonSchema:void 0}]))}};function vi(t,e,a,s){s!=null&&s.errorMessages&&a&&(t.errorMessage={...t.errorMessage,[e]:a})}function ie(t,e,a,s,r){t[e]=a,vi(t,e,s,r)}var Fs=(t,e)=>{let a=0;for(;a<t.length&&a<e.length&&t[a]===e[a];a++);return[(t.length-a).toString(),...e.slice(a)].join("/")};function Ee(t){if(t.target!=="openAi")return{};let e=[...t.basePath,t.definitionPath,t.openAiAnyTypeName];return t.flags.hasReferencedOpenAiAnyType=!0,{$ref:t.$refStrategy==="relative"?Fs(e,t.currentPath):e.join("/")}}function Tp(t,e){var s,r,n;let a={type:"array"};return(s=t.type)!=null&&s._def&&((n=(r=t.type)==null?void 0:r._def)==null?void 0:n.typeName)!==N.ZodAny&&(a.items=X(t.type._def,{...e,currentPath:[...e.currentPath,"items"]})),t.minLength&&ie(a,"minItems",t.minLength.value,t.minLength.message,e),t.maxLength&&ie(a,"maxItems",t.maxLength.value,t.maxLength.message,e),t.exactLength&&(ie(a,"minItems",t.exactLength.value,t.exactLength.message,e),ie(a,"maxItems",t.exactLength.value,t.exactLength.message,e)),a}function Ap(t,e){let a={type:"integer",format:"int64"};if(!t.checks)return a;for(let s of t.checks)switch(s.kind){case"min":e.target==="jsonSchema7"?s.inclusive?ie(a,"minimum",s.value,s.message,e):ie(a,"exclusiveMinimum",s.value,s.message,e):(s.inclusive||(a.exclusiveMinimum=!0),ie(a,"minimum",s.value,s.message,e));break;case"max":e.target==="jsonSchema7"?s.inclusive?ie(a,"maximum",s.value,s.message,e):ie(a,"exclusiveMaximum",s.value,s.message,e):(s.inclusive||(a.exclusiveMaximum=!0),ie(a,"maximum",s.value,s.message,e));break;case"multipleOf":ie(a,"multipleOf",s.value,s.message,e);break}return a}function Cp(){return{type:"boolean"}}function Ls(t,e){return X(t.type._def,e)}var Op=(t,e)=>X(t.innerType._def,e);function xi(t,e,a){let s=a??e.dateStrategy;if(Array.isArray(s))return{anyOf:s.map((r,n)=>xi(t,e,r))};switch(s){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return Vg(t,e)}}var Vg=(t,e)=>{let a={type:"integer",format:"unix-time"};if(e.target==="openApi3")return a;for(let s of t.checks)switch(s.kind){case"min":ie(a,"minimum",s.value,s.message,e);break;case"max":ie(a,"maximum",s.value,s.message,e);break}return a};function jp(t,e){return{...X(t.innerType._def,e),default:t.defaultValue()}}function Ip(t,e){return e.effectStrategy==="input"?X(t.schema._def,e):Ee(e)}function Dp(t){return{type:"string",enum:Array.from(t.values)}}var Zg=t=>"type"in t&&t.type==="string"?!1:"allOf"in t;function Np(t,e){let a=[X(t.left._def,{...e,currentPath:[...e.currentPath,"allOf","0"]}),X(t.right._def,{...e,currentPath:[...e.currentPath,"allOf","1"]})].filter(n=>!!n),s=e.target==="jsonSchema2019-09"?{unevaluatedProperties:!1}:void 0,r=[];return a.forEach(n=>{if(Zg(n))r.push(...n.allOf),n.unevaluatedProperties===void 0&&(s=void 0);else{let i=n;if("additionalProperties"in n&&n.additionalProperties===!1){let{additionalProperties:o,...p}=n;i=p}else s=void 0;r.push(i)}}),r.length?{allOf:r,...s}:void 0}function Fp(t,e){let a=typeof t.value;return a!=="bigint"&&a!=="number"&&a!=="boolean"&&a!=="string"?{type:Array.isArray(t.value)?"array":"object"}:e.target==="openApi3"?{type:a==="bigint"?"integer":a,enum:[t.value]}:{type:a==="bigint"?"integer":a,const:t.value}}var gi,xt={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(gi===void 0&&(gi=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),gi),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function $s(t,e){let a={type:"string"};if(t.checks)for(let s of t.checks)switch(s.kind){case"min":ie(a,"minLength",typeof a.minLength=="number"?Math.max(a.minLength,s.value):s.value,s.message,e);break;case"max":ie(a,"maxLength",typeof a.maxLength=="number"?Math.min(a.maxLength,s.value):s.value,s.message,e);break;case"email":switch(e.emailStrategy){case"format:email":gt(a,"email",s.message,e);break;case"format:idn-email":gt(a,"idn-email",s.message,e);break;case"pattern:zod":Ze(a,xt.email,s.message,e);break}break;case"url":gt(a,"uri",s.message,e);break;case"uuid":gt(a,"uuid",s.message,e);break;case"regex":Ze(a,s.regex,s.message,e);break;case"cuid":Ze(a,xt.cuid,s.message,e);break;case"cuid2":Ze(a,xt.cuid2,s.message,e);break;case"startsWith":Ze(a,RegExp(`^${yi(s.value,e)}`),s.message,e);break;case"endsWith":Ze(a,RegExp(`${yi(s.value,e)}$`),s.message,e);break;case"datetime":gt(a,"date-time",s.message,e);break;case"date":gt(a,"date",s.message,e);break;case"time":gt(a,"time",s.message,e);break;case"duration":gt(a,"duration",s.message,e);break;case"length":ie(a,"minLength",typeof a.minLength=="number"?Math.max(a.minLength,s.value):s.value,s.message,e),ie(a,"maxLength",typeof a.maxLength=="number"?Math.min(a.maxLength,s.value):s.value,s.message,e);break;case"includes":{Ze(a,RegExp(yi(s.value,e)),s.message,e);break}case"ip":{s.version!=="v6"&&gt(a,"ipv4",s.message,e),s.version!=="v4"&&gt(a,"ipv6",s.message,e);break}case"base64url":Ze(a,xt.base64url,s.message,e);break;case"jwt":Ze(a,xt.jwt,s.message,e);break;case"cidr":{s.version!=="v6"&&Ze(a,xt.ipv4Cidr,s.message,e),s.version!=="v4"&&Ze(a,xt.ipv6Cidr,s.message,e);break}case"emoji":Ze(a,xt.emoji(),s.message,e);break;case"ulid":{Ze(a,xt.ulid,s.message,e);break}case"base64":{switch(e.base64Strategy){case"format:binary":{gt(a,"binary",s.message,e);break}case"contentEncoding:base64":{ie(a,"contentEncoding","base64",s.message,e);break}case"pattern:zod":{Ze(a,xt.base64,s.message,e);break}}break}case"nanoid":Ze(a,xt.nanoid,s.message,e);case"toLowerCase":case"toUpperCase":case"trim":break;default:}return a}function yi(t,e){return e.patternStrategy==="escape"?Jg(t):t}var Gg=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function Jg(t){let e="";for(let a=0;a<t.length;a++)Gg.has(t[a])||(e+="\\"),e+=t[a];return e}function gt(t,e,a,s){var r;t.format||(r=t.anyOf)!=null&&r.some(n=>n.format)?(t.anyOf||(t.anyOf=[]),t.format&&(t.anyOf.push({format:t.format,...t.errorMessage&&s.errorMessages&&{errorMessage:{format:t.errorMessage.format}}}),delete t.format,t.errorMessage&&(delete t.errorMessage.format,Object.keys(t.errorMessage).length===0&&delete t.errorMessage)),t.anyOf.push({format:e,...a&&s.errorMessages&&{errorMessage:{format:a}}})):ie(t,"format",e,a,s)}function Ze(t,e,a,s){var r;t.pattern||(r=t.allOf)!=null&&r.some(n=>n.pattern)?(t.allOf||(t.allOf=[]),t.pattern&&(t.allOf.push({pattern:t.pattern,...t.errorMessage&&s.errorMessages&&{errorMessage:{pattern:t.errorMessage.pattern}}}),delete t.pattern,t.errorMessage&&(delete t.errorMessage.pattern,Object.keys(t.errorMessage).length===0&&delete t.errorMessage)),t.allOf.push({pattern:Lp(e,s),...a&&s.errorMessages&&{errorMessage:{pattern:a}}})):ie(t,"pattern",Lp(e,s),a,s)}function Lp(t,e){var p;if(!e.applyRegexFlags||!t.flags)return t.source;let a={i:t.flags.includes("i"),m:t.flags.includes("m"),s:t.flags.includes("s")},s=a.i?t.source.toLowerCase():t.source,r="",n=!1,i=!1,o=!1;for(let c=0;c<s.length;c++){if(n){r+=s[c],n=!1;continue}if(a.i){if(i){if(s[c].match(/[a-z]/)){o?(r+=s[c],r+=`${s[c-2]}-${s[c]}`.toUpperCase(),o=!1):s[c+1]==="-"&&((p=s[c+2])!=null&&p.match(/[a-z]/))?(r+=s[c],o=!0):r+=`${s[c]}${s[c].toUpperCase()}`;continue}}else if(s[c].match(/[a-z]/)){r+=`[${s[c]}${s[c].toUpperCase()}]`;continue}}if(a.m){if(s[c]==="^"){r+=`(^|(?<=[\r
]))`;continue}else if(s[c]==="$"){r+=`($|(?=[\r
]))`;continue}}if(a.s&&s[c]==="."){r+=i?`${s[c]}\r
`:`[${s[c]}\r
]`;continue}r+=s[c],s[c]==="\\"?n=!0:i&&s[c]==="]"?i=!1:!i&&s[c]==="["&&(i=!0)}try{new RegExp(r)}catch{return console.warn(`Could not convert regex pattern at ${e.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),t.source}return r}function qs(t,e){var s,r,n,i,o,p;if(e.target==="openAi"&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),e.target==="openApi3"&&((s=t.keyType)==null?void 0:s._def.typeName)===N.ZodEnum)return{type:"object",required:t.keyType._def.values,properties:t.keyType._def.values.reduce((c,l)=>({...c,[l]:X(t.valueType._def,{...e,currentPath:[...e.currentPath,"properties",l]})??Ee(e)}),{}),additionalProperties:e.rejectedAdditionalProperties};let a={type:"object",additionalProperties:X(t.valueType._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]})??e.allowedAdditionalProperties};if(e.target==="openApi3")return a;if(((r=t.keyType)==null?void 0:r._def.typeName)===N.ZodString&&((n=t.keyType._def.checks)!=null&&n.length)){let{type:c,...l}=$s(t.keyType._def,e);return{...a,propertyNames:l}}else{if(((i=t.keyType)==null?void 0:i._def.typeName)===N.ZodEnum)return{...a,propertyNames:{enum:t.keyType._def.values}};if(((o=t.keyType)==null?void 0:o._def.typeName)===N.ZodBranded&&t.keyType._def.type._def.typeName===N.ZodString&&((p=t.keyType._def.type._def.checks)!=null&&p.length)){let{type:c,...l}=Ls(t.keyType._def,e);return{...a,propertyNames:l}}}return a}function $p(t,e){if(e.mapStrategy==="record")return qs(t,e);let a=X(t.keyType._def,{...e,currentPath:[...e.currentPath,"items","items","0"]})||Ee(e),s=X(t.valueType._def,{...e,currentPath:[...e.currentPath,"items","items","1"]})||Ee(e);return{type:"array",maxItems:125,items:{type:"array",items:[a,s],minItems:2,maxItems:2}}}function qp(t){let e=t.values,s=Object.keys(t.values).filter(n=>typeof e[e[n]]!="number").map(n=>e[n]),r=Array.from(new Set(s.map(n=>typeof n)));return{type:r.length===1?r[0]==="string"?"string":"number":["string","number"],enum:s}}function Up(t){return t.target==="openAi"?void 0:{not:Ee({...t,currentPath:[...t.currentPath,"not"]})}}function zp(t){return t.target==="openApi3"?{enum:["null"],nullable:!0}:{type:"null"}}var Pr={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};function Bp(t,e){if(e.target==="openApi3")return Mp(t,e);let a=t.options instanceof Map?Array.from(t.options.values()):t.options;if(a.every(s=>s._def.typeName in Pr&&(!s._def.checks||!s._def.checks.length))){let s=a.reduce((r,n)=>{let i=Pr[n._def.typeName];return i&&!r.includes(i)?[...r,i]:r},[]);return{type:s.length>1?s:s[0]}}else if(a.every(s=>s._def.typeName==="ZodLiteral"&&!s.description)){let s=a.reduce((r,n)=>{let i=typeof n._def.value;switch(i){case"string":case"number":case"boolean":return[...r,i];case"bigint":return[...r,"integer"];case"object":if(n._def.value===null)return[...r,"null"];case"symbol":case"undefined":case"function":default:return r}},[]);if(s.length===a.length){let r=s.filter((n,i,o)=>o.indexOf(n)===i);return{type:r.length>1?r:r[0],enum:a.reduce((n,i)=>n.includes(i._def.value)?n:[...n,i._def.value],[])}}}else if(a.every(s=>s._def.typeName==="ZodEnum"))return{type:"string",enum:a.reduce((s,r)=>[...s,...r._def.values.filter(n=>!s.includes(n))],[])};return Mp(t,e)}var Mp=(t,e)=>{let a=(t.options instanceof Map?Array.from(t.options.values()):t.options).map((s,r)=>X(s._def,{...e,currentPath:[...e.currentPath,"anyOf",`${r}`]})).filter(s=>!!s&&(!e.strictUnions||typeof s=="object"&&Object.keys(s).length>0));return a.length?{anyOf:a}:void 0};function Hp(t,e){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(t.innerType._def.typeName)&&(!t.innerType._def.checks||!t.innerType._def.checks.length))return e.target==="openApi3"?{type:Pr[t.innerType._def.typeName],nullable:!0}:{type:[Pr[t.innerType._def.typeName],"null"]};if(e.target==="openApi3"){let s=X(t.innerType._def,{...e,currentPath:[...e.currentPath]});return s&&"$ref"in s?{allOf:[s],nullable:!0}:s&&{...s,nullable:!0}}let a=X(t.innerType._def,{...e,currentPath:[...e.currentPath,"anyOf","0"]});return a&&{anyOf:[a,{type:"null"}]}}function Vp(t,e){let a={type:"number"};if(!t.checks)return a;for(let s of t.checks)switch(s.kind){case"int":a.type="integer",vi(a,"type",s.message,e);break;case"min":e.target==="jsonSchema7"?s.inclusive?ie(a,"minimum",s.value,s.message,e):ie(a,"exclusiveMinimum",s.value,s.message,e):(s.inclusive||(a.exclusiveMinimum=!0),ie(a,"minimum",s.value,s.message,e));break;case"max":e.target==="jsonSchema7"?s.inclusive?ie(a,"maximum",s.value,s.message,e):ie(a,"exclusiveMaximum",s.value,s.message,e):(s.inclusive||(a.exclusiveMaximum=!0),ie(a,"maximum",s.value,s.message,e));break;case"multipleOf":ie(a,"multipleOf",s.value,s.message,e);break}return a}function Zp(t,e){let a=e.target==="openAi",s={type:"object",properties:{}},r=[],n=t.shape();for(let o in n){let p=n[o];if(p===void 0||p._def===void 0)continue;let c=Qg(p);c&&a&&(p._def.typeName==="ZodOptional"&&(p=p._def.innerType),p.isNullable()||(p=p.nullable()),c=!1);let l=X(p._def,{...e,currentPath:[...e.currentPath,"properties",o],propertyPath:[...e.currentPath,"properties",o]});l!==void 0&&(s.properties[o]=l,c||r.push(o))}r.length&&(s.required=r);let i=Kg(t,e);return i!==void 0&&(s.additionalProperties=i),s}function Kg(t,e){if(t.catchall._def.typeName!=="ZodNever")return X(t.catchall._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]});switch(t.unknownKeys){case"passthrough":return e.allowedAdditionalProperties;case"strict":return e.rejectedAdditionalProperties;case"strip":return e.removeAdditionalStrategy==="strict"?e.allowedAdditionalProperties:e.rejectedAdditionalProperties}}function Qg(t){try{return t.isOptional()}catch{return!0}}var Gp=(t,e)=>{var s;if(e.currentPath.toString()===((s=e.propertyPath)==null?void 0:s.toString()))return X(t.innerType._def,e);let a=X(t.innerType._def,{...e,currentPath:[...e.currentPath,"anyOf","1"]});return a?{anyOf:[{not:Ee(e)},a]}:Ee(e)};var Jp=(t,e)=>{if(e.pipeStrategy==="input")return X(t.in._def,e);if(e.pipeStrategy==="output")return X(t.out._def,e);let a=X(t.in._def,{...e,currentPath:[...e.currentPath,"allOf","0"]}),s=X(t.out._def,{...e,currentPath:[...e.currentPath,"allOf",a?"1":"0"]});return{allOf:[a,s].filter(r=>r!==void 0)}};function Kp(t,e){return X(t.type._def,e)}function Qp(t,e){let s={type:"array",uniqueItems:!0,items:X(t.valueType._def,{...e,currentPath:[...e.currentPath,"items"]})};return t.minSize&&ie(s,"minItems",t.minSize.value,t.minSize.message,e),t.maxSize&&ie(s,"maxItems",t.maxSize.value,t.maxSize.message,e),s}function Wp(t,e){return t.rest?{type:"array",minItems:t.items.length,items:t.items.map((a,s)=>X(a._def,{...e,currentPath:[...e.currentPath,"items",`${s}`]})).reduce((a,s)=>s===void 0?a:[...a,s],[]),additionalItems:X(t.rest._def,{...e,currentPath:[...e.currentPath,"additionalItems"]})}:{type:"array",minItems:t.items.length,maxItems:t.items.length,items:t.items.map((a,s)=>X(a._def,{...e,currentPath:[...e.currentPath,"items",`${s}`]})).reduce((a,s)=>s===void 0?a:[...a,s],[])}}function Xp(t){return{not:Ee(t)}}function Yp(t){return Ee(t)}var eu=(t,e)=>X(t.innerType._def,e);var tu=(t,e,a)=>{switch(e){case N.ZodString:return $s(t,a);case N.ZodNumber:return Vp(t,a);case N.ZodObject:return Zp(t,a);case N.ZodBigInt:return Ap(t,a);case N.ZodBoolean:return Cp();case N.ZodDate:return xi(t,a);case N.ZodUndefined:return Xp(a);case N.ZodNull:return zp(a);case N.ZodArray:return Tp(t,a);case N.ZodUnion:case N.ZodDiscriminatedUnion:return Bp(t,a);case N.ZodIntersection:return Np(t,a);case N.ZodTuple:return Wp(t,a);case N.ZodRecord:return qs(t,a);case N.ZodLiteral:return Fp(t,a);case N.ZodEnum:return Dp(t);case N.ZodNativeEnum:return qp(t);case N.ZodNullable:return Hp(t,a);case N.ZodOptional:return Gp(t,a);case N.ZodMap:return $p(t,a);case N.ZodSet:return Qp(t,a);case N.ZodLazy:return()=>t.getter()._def;case N.ZodPromise:return Kp(t,a);case N.ZodNaN:case N.ZodNever:return Up(a);case N.ZodEffects:return Ip(t,a);case N.ZodAny:return Ee(a);case N.ZodUnknown:return Yp(a);case N.ZodDefault:return jp(t,a);case N.ZodBranded:return Ls(t,a);case N.ZodReadonly:return eu(t,a);case N.ZodCatch:return Op(t,a);case N.ZodPipeline:return Jp(t,a);case N.ZodFunction:case N.ZodVoid:case N.ZodSymbol:return;default:return(s=>{})(e)}};function X(t,e,a=!1){var o;let s=e.seen.get(t);if(e.override){let p=(o=e.override)==null?void 0:o.call(e,t,e,s,a);if(p!==Pp)return p}if(s&&!a){let p=Wg(s,e);if(p!==void 0)return p}let r={def:t,path:e.currentPath,jsonSchema:void 0};e.seen.set(t,r);let n=tu(t,t.typeName,e),i=typeof n=="function"?X(n(),e):n;if(i&&Xg(t,e,i),e.postProcess){let p=e.postProcess(i,t,e);return r.jsonSchema=i,p}return r.jsonSchema=i,i}var Wg=(t,e)=>{switch(e.$refStrategy){case"root":return{$ref:t.path.join("/")};case"relative":return{$ref:Fs(e.currentPath,t.path)};case"none":case"seen":return t.path.length<e.currentPath.length&&t.path.every((a,s)=>e.currentPath[s]===a)?(console.warn(`Recursive reference detected at ${e.currentPath.join("/")}! Defaulting to any`),Ee(e)):e.$refStrategy==="seen"?Ee(e):void 0}},Xg=(t,e,a)=>(t.description&&(a.description=t.description,e.markdownDescription&&(a.markdownDescription=t.description)),a);var Us=(t,e)=>{let a=kp(e),s=typeof e=="object"&&e.definitions?Object.entries(e.definitions).reduce((p,[c,l])=>({...p,[c]:X(l._def,{...a,currentPath:[...a.basePath,a.definitionPath,c]},!0)??Ee(a)}),{}):void 0,r=typeof e=="string"?e:(e==null?void 0:e.nameStrategy)==="title"||e==null?void 0:e.name,n=X(t._def,r===void 0?a:{...a,currentPath:[...a.basePath,a.definitionPath,r]},!1)??Ee(a),i=typeof e=="object"&&e.name!==void 0&&e.nameStrategy==="title"?e.name:void 0;i!==void 0&&(n.title=i),a.flags.hasReferencedOpenAiAnyType&&(s||(s={}),s[a.openAiAnyTypeName]||(s[a.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:a.$refStrategy==="relative"?"1":[...a.basePath,a.definitionPath,a.openAiAnyTypeName].join("/")}}));let o=r===void 0?s?{...n,[a.definitionPath]:s}:n:{$ref:[...a.$refStrategy==="relative"?[]:a.basePath,a.definitionPath,r].join("/"),[a.definitionPath]:{...s,[r]:n}};return a.target==="jsonSchema7"?o.$schema="http://json-schema.org/draft-07/schema#":(a.target==="jsonSchema2019-09"||a.target==="openAi")&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),a.target==="openAi"&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};var bi;(function(t){t.Completable="McpCompletable"})(bi||(bi={}));var Ka=class extends te{_parse(e){let{ctx:a}=this._processInputParams(e),s=a.data;return this._def.type._parse({data:s,path:a.path,parent:a})}unwrap(){return this._def.type}};Ka.create=(t,e)=>new Ka({type:t,typeName:bi.Completable,complete:e.complete,...Yg(e)});function Yg(t){if(!t)return{};let{errorMap:e,invalid_type_error:a,required_error:s,description:r}=t;if(e&&(a||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(i,o)=>{var p,c;let{message:l}=t;return i.code==="invalid_enum_value"?{message:l??o.defaultError}:typeof o.data>"u"?{message:(p=l??s)!==null&&p!==void 0?p:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(c=l??a)!==null&&c!==void 0?c:o.defaultError}},description:r}}var Ms=class{constructor(e,a){this._registeredResources={},this._registeredResourceTemplates={},this._registeredTools={},this._registeredPrompts={},this._toolHandlersInitialized=!1,this._completionHandlerInitialized=!1,this._resourceHandlersInitialized=!1,this._promptHandlersInitialized=!1,this.server=new Ns(e,a)}async connect(e){return await this.server.connect(e)}async close(){await this.server.close()}setToolRequestHandlers(){this._toolHandlersInitialized||(this.server.assertCanSetRequestHandler(ms.shape.method.value),this.server.assertCanSetRequestHandler(fs.shape.method.value),this.server.registerCapabilities({tools:{listChanged:!0}}),this.server.setRequestHandler(ms,()=>({tools:Object.entries(this._registeredTools).filter(([,e])=>e.enabled).map(([e,a])=>{let s={name:e,title:a.title,description:a.description,inputSchema:a.inputSchema?Us(a.inputSchema,{strictUnions:!0}):ey,annotations:a.annotations};return a.outputSchema&&(s.outputSchema=Us(a.outputSchema,{strictUnions:!0})),s})})),this.server.setRequestHandler(fs,async(e,a)=>{let s=this._registeredTools[e.params.name];if(!s)throw new ye(ge.InvalidParams,`Tool ${e.params.name} not found`);if(!s.enabled)throw new ye(ge.InvalidParams,`Tool ${e.params.name} disabled`);let r;if(s.inputSchema){let n=await s.inputSchema.safeParseAsync(e.params.arguments);if(!n.success)throw new ye(ge.InvalidParams,`Invalid arguments for tool ${e.params.name}: ${n.error.message}`);let i=n.data,o=s.callback;try{r=await Promise.resolve(o(i,a))}catch(p){r={content:[{type:"text",text:p instanceof Error?p.message:String(p)}],isError:!0}}}else{let n=s.callback;try{r=await Promise.resolve(n(a))}catch(i){r={content:[{type:"text",text:i instanceof Error?i.message:String(i)}],isError:!0}}}if(s.outputSchema&&!r.isError){if(!r.structuredContent)throw new ye(ge.InvalidParams,`Tool ${e.params.name} has an output schema but no structured content was provided`);let n=await s.outputSchema.safeParseAsync(r.structuredContent);if(!n.success)throw new ye(ge.InvalidParams,`Invalid structured content for tool ${e.params.name}: ${n.error.message}`)}return r}),this._toolHandlersInitialized=!0)}setCompletionRequestHandler(){this._completionHandlerInitialized||(this.server.assertCanSetRequestHandler(hs.shape.method.value),this.server.registerCapabilities({completions:{}}),this.server.setRequestHandler(hs,async e=>{switch(e.params.ref.type){case"ref/prompt":return this.handlePromptCompletion(e,e.params.ref);case"ref/resource":return this.handleResourceCompletion(e,e.params.ref);default:throw new ye(ge.InvalidParams,`Invalid completion reference: ${e.params.ref}`)}}),this._completionHandlerInitialized=!0)}async handlePromptCompletion(e,a){let s=this._registeredPrompts[a.name];if(!s)throw new ye(ge.InvalidParams,`Prompt ${a.name} not found`);if(!s.enabled)throw new ye(ge.InvalidParams,`Prompt ${a.name} disabled`);if(!s.argsSchema)return zs;let r=s.argsSchema.shape[e.params.argument.name];if(!(r instanceof Ka))return zs;let i=await r._def.complete(e.params.argument.value,e.params.context);return ru(i)}async handleResourceCompletion(e,a){let s=Object.values(this._registeredResourceTemplates).find(i=>i.resourceTemplate.uriTemplate.toString()===a.uri);if(!s){if(this._registeredResources[a.uri])return zs;throw new ye(ge.InvalidParams,`Resource template ${e.params.ref.uri} not found`)}let r=s.resourceTemplate.completeCallback(e.params.argument.name);if(!r)return zs;let n=await r(e.params.argument.value,e.params.context);return ru(n)}setResourceRequestHandlers(){this._resourceHandlersInitialized||(this.server.assertCanSetRequestHandler(cs.shape.method.value),this.server.assertCanSetRequestHandler(ls.shape.method.value),this.server.assertCanSetRequestHandler(ps.shape.method.value),this.server.registerCapabilities({resources:{listChanged:!0}}),this.server.setRequestHandler(cs,async(e,a)=>{let s=Object.entries(this._registeredResources).filter(([n,i])=>i.enabled).map(([n,i])=>({uri:n,name:i.name,...i.metadata})),r=[];for(let n of Object.values(this._registeredResourceTemplates)){if(!n.resourceTemplate.listCallback)continue;let i=await n.resourceTemplate.listCallback(a);for(let o of i.resources)r.push({...n.metadata,...o})}return{resources:[...s,...r]}}),this.server.setRequestHandler(ls,async()=>({resourceTemplates:Object.entries(this._registeredResourceTemplates).map(([a,s])=>({name:a,uriTemplate:s.resourceTemplate.uriTemplate.toString(),...s.metadata}))})),this.server.setRequestHandler(ps,async(e,a)=>{let s=new URL(e.params.uri),r=this._registeredResources[s.toString()];if(r){if(!r.enabled)throw new ye(ge.InvalidParams,`Resource ${s} disabled`);return r.readCallback(s,a)}for(let n of Object.values(this._registeredResourceTemplates)){let i=n.resourceTemplate.uriTemplate.match(s.toString());if(i)return n.readCallback(s,i,a)}throw new ye(ge.InvalidParams,`Resource ${s} not found`)}),this.setCompletionRequestHandler(),this._resourceHandlersInitialized=!0)}setPromptRequestHandlers(){this._promptHandlersInitialized||(this.server.assertCanSetRequestHandler(us.shape.method.value),this.server.assertCanSetRequestHandler(ds.shape.method.value),this.server.registerCapabilities({prompts:{listChanged:!0}}),this.server.setRequestHandler(us,()=>({prompts:Object.entries(this._registeredPrompts).filter(([,e])=>e.enabled).map(([e,a])=>({name:e,title:a.title,description:a.description,arguments:a.argsSchema?ay(a.argsSchema):void 0}))})),this.server.setRequestHandler(ds,async(e,a)=>{let s=this._registeredPrompts[e.params.name];if(!s)throw new ye(ge.InvalidParams,`Prompt ${e.params.name} not found`);if(!s.enabled)throw new ye(ge.InvalidParams,`Prompt ${e.params.name} disabled`);if(s.argsSchema){let r=await s.argsSchema.safeParseAsync(e.params.arguments);if(!r.success)throw new ye(ge.InvalidParams,`Invalid arguments for prompt ${e.params.name}: ${r.error.message}`);let n=r.data,i=s.callback;return await Promise.resolve(i(n,a))}else{let r=s.callback;return await Promise.resolve(r(a))}}),this.setCompletionRequestHandler(),this._promptHandlersInitialized=!0)}resource(e,a,...s){let r;typeof s[0]=="object"&&(r=s.shift());let n=s[0];if(typeof a=="string"){if(this._registeredResources[a])throw new Error(`Resource ${a} is already registered`);let i=this._createRegisteredResource(e,void 0,a,r,n);return this.setResourceRequestHandlers(),this.sendResourceListChanged(),i}else{if(this._registeredResourceTemplates[e])throw new Error(`Resource template ${e} is already registered`);let i=this._createRegisteredResourceTemplate(e,void 0,a,r,n);return this.setResourceRequestHandlers(),this.sendResourceListChanged(),i}}registerResource(e,a,s,r){if(typeof a=="string"){if(this._registeredResources[a])throw new Error(`Resource ${a} is already registered`);let n=this._createRegisteredResource(e,s.title,a,s,r);return this.setResourceRequestHandlers(),this.sendResourceListChanged(),n}else{if(this._registeredResourceTemplates[e])throw new Error(`Resource template ${e} is already registered`);let n=this._createRegisteredResourceTemplate(e,s.title,a,s,r);return this.setResourceRequestHandlers(),this.sendResourceListChanged(),n}}_createRegisteredResource(e,a,s,r,n){let i={name:e,title:a,metadata:r,readCallback:n,enabled:!0,disable:()=>i.update({enabled:!1}),enable:()=>i.update({enabled:!0}),remove:()=>i.update({uri:null}),update:o=>{typeof o.uri<"u"&&o.uri!==s&&(delete this._registeredResources[s],o.uri&&(this._registeredResources[o.uri]=i)),typeof o.name<"u"&&(i.name=o.name),typeof o.title<"u"&&(i.title=o.title),typeof o.metadata<"u"&&(i.metadata=o.metadata),typeof o.callback<"u"&&(i.readCallback=o.callback),typeof o.enabled<"u"&&(i.enabled=o.enabled),this.sendResourceListChanged()}};return this._registeredResources[s]=i,i}_createRegisteredResourceTemplate(e,a,s,r,n){let i={resourceTemplate:s,title:a,metadata:r,readCallback:n,enabled:!0,disable:()=>i.update({enabled:!1}),enable:()=>i.update({enabled:!0}),remove:()=>i.update({name:null}),update:o=>{typeof o.name<"u"&&o.name!==e&&(delete this._registeredResourceTemplates[e],o.name&&(this._registeredResourceTemplates[o.name]=i)),typeof o.title<"u"&&(i.title=o.title),typeof o.template<"u"&&(i.resourceTemplate=o.template),typeof o.metadata<"u"&&(i.metadata=o.metadata),typeof o.callback<"u"&&(i.readCallback=o.callback),typeof o.enabled<"u"&&(i.enabled=o.enabled),this.sendResourceListChanged()}};return this._registeredResourceTemplates[e]=i,i}_createRegisteredPrompt(e,a,s,r,n){let i={title:a,description:s,argsSchema:r===void 0?void 0:u.object(r),callback:n,enabled:!0,disable:()=>i.update({enabled:!1}),enable:()=>i.update({enabled:!0}),remove:()=>i.update({name:null}),update:o=>{typeof o.name<"u"&&o.name!==e&&(delete this._registeredPrompts[e],o.name&&(this._registeredPrompts[o.name]=i)),typeof o.title<"u"&&(i.title=o.title),typeof o.description<"u"&&(i.description=o.description),typeof o.argsSchema<"u"&&(i.argsSchema=u.object(o.argsSchema)),typeof o.callback<"u"&&(i.callback=o.callback),typeof o.enabled<"u"&&(i.enabled=o.enabled),this.sendPromptListChanged()}};return this._registeredPrompts[e]=i,i}_createRegisteredTool(e,a,s,r,n,i,o){let p={title:a,description:s,inputSchema:r===void 0?void 0:u.object(r),outputSchema:n===void 0?void 0:u.object(n),annotations:i,callback:o,enabled:!0,disable:()=>p.update({enabled:!1}),enable:()=>p.update({enabled:!0}),remove:()=>p.update({name:null}),update:c=>{typeof c.name<"u"&&c.name!==e&&(delete this._registeredTools[e],c.name&&(this._registeredTools[c.name]=p)),typeof c.title<"u"&&(p.title=c.title),typeof c.description<"u"&&(p.description=c.description),typeof c.paramsSchema<"u"&&(p.inputSchema=u.object(c.paramsSchema)),typeof c.callback<"u"&&(p.callback=c.callback),typeof c.annotations<"u"&&(p.annotations=c.annotations),typeof c.enabled<"u"&&(p.enabled=c.enabled),this.sendToolListChanged()}};return this._registeredTools[e]=p,this.setToolRequestHandlers(),this.sendToolListChanged(),p}tool(e,...a){if(this._registeredTools[e])throw new Error(`Tool ${e} is already registered`);let s,r,n,i;if(typeof a[0]=="string"&&(s=a.shift()),a.length>1){let p=a[0];au(p)?(r=a.shift(),a.length>1&&typeof a[0]=="object"&&a[0]!==null&&!au(a[0])&&(i=a.shift())):typeof p=="object"&&p!==null&&(i=a.shift())}let o=a[0];return this._createRegisteredTool(e,void 0,s,r,n,i,o)}registerTool(e,a,s){if(this._registeredTools[e])throw new Error(`Tool ${e} is already registered`);let{title:r,description:n,inputSchema:i,outputSchema:o,annotations:p}=a;return this._createRegisteredTool(e,r,n,i,o,p,s)}prompt(e,...a){if(this._registeredPrompts[e])throw new Error(`Prompt ${e} is already registered`);let s;typeof a[0]=="string"&&(s=a.shift());let r;a.length>1&&(r=a.shift());let n=a[0],i=this._createRegisteredPrompt(e,void 0,s,r,n);return this.setPromptRequestHandlers(),this.sendPromptListChanged(),i}registerPrompt(e,a,s){if(this._registeredPrompts[e])throw new Error(`Prompt ${e} is already registered`);let{title:r,description:n,argsSchema:i}=a,o=this._createRegisteredPrompt(e,r,n,i,s);return this.setPromptRequestHandlers(),this.sendPromptListChanged(),o}isConnected(){return this.server.transport!==void 0}sendResourceListChanged(){this.isConnected()&&this.server.sendResourceListChanged()}sendToolListChanged(){this.isConnected()&&this.server.sendToolListChanged()}sendPromptListChanged(){this.isConnected()&&this.server.sendPromptListChanged()}};var ey={type:"object",properties:{}};function au(t){return typeof t!="object"||t===null?!1:Object.keys(t).length===0||Object.values(t).some(ty)}function ty(t){return t!==null&&typeof t=="object"&&"parse"in t&&typeof t.parse=="function"&&"safeParse"in t&&typeof t.safeParse=="function"}function ay(t){return Object.entries(t.shape).map(([e,a])=>({name:e,description:a.description,required:!a.isOptional()}))}function ru(t){return{completion:{values:t.slice(0,100),total:t.length,hasMore:t.length>100}}}var zs={completion:{values:[],hasMore:!1}};var _i=Be(require("node:process"),1);var Bs=class{append(e){this._buffer=this._buffer?Buffer.concat([this._buffer,e]):e}readMessage(){if(!this._buffer)return null;let e=this._buffer.indexOf(`
`);if(e===-1)return null;let a=this._buffer.toString("utf8",0,e).replace(/\r$/,"");return this._buffer=this._buffer.subarray(e+1),ry(a)}clear(){this._buffer=void 0}};function ry(t){return dc.parse(JSON.parse(t))}function su(t){return JSON.stringify(t)+`
`}var Hs=class{constructor(e=_i.default.stdin,a=_i.default.stdout){this._stdin=e,this._stdout=a,this._readBuffer=new Bs,this._started=!1,this._ondata=s=>{this._readBuffer.append(s),this.processReadBuffer()},this._onerror=s=>{var r;(r=this.onerror)===null||r===void 0||r.call(this,s)}}async start(){if(this._started)throw new Error("StdioServerTransport already started! If using Server class, note that connect() calls start() automatically.");this._started=!0,this._stdin.on("data",this._ondata),this._stdin.on("error",this._onerror)}processReadBuffer(){for(var e,a;;)try{let s=this._readBuffer.readMessage();if(s===null)break;(e=this.onmessage)===null||e===void 0||e.call(this,s)}catch(s){(a=this.onerror)===null||a===void 0||a.call(this,s)}}async close(){var e;this._stdin.off("data",this._ondata),this._stdin.off("error",this._onerror),this._stdin.listenerCount("data")===0&&this._stdin.pause(),this._readBuffer.clear(),(e=this.onclose)===null||e===void 0||e.call(this)}send(e){return new Promise(a=>{let s=su(e);this._stdout.write(s)?a():this._stdout.once("drain",a)})}};function Rr(t,e){return function(){return t.apply(e,arguments)}}var{toString:sy}=Object.prototype,{getPrototypeOf:Ei}=Object,{iterator:Zs,toStringTag:iu}=Symbol,Gs=(t=>e=>{let a=sy.call(e);return t[a]||(t[a]=a.slice(8,-1).toLowerCase())})(Object.create(null)),yt=t=>(t=t.toLowerCase(),e=>Gs(e)===t),Js=t=>e=>typeof e===t,{isArray:Qa}=Array,kr=Js("undefined");function ny(t){return t!==null&&!kr(t)&&t.constructor!==null&&!kr(t.constructor)&&We(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}var ou=yt("ArrayBuffer");function iy(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&ou(t.buffer),e}var oy=Js("string"),We=Js("function"),cu=Js("number"),Ks=t=>t!==null&&typeof t=="object",cy=t=>t===!0||t===!1,Vs=t=>{if(Gs(t)!=="object")return!1;let e=Ei(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(iu in t)&&!(Zs in t)},ly=yt("Date"),py=yt("File"),uy=yt("Blob"),dy=yt("FileList"),my=t=>Ks(t)&&We(t.pipe),fy=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||We(t.append)&&((e=Gs(t))==="formdata"||e==="object"&&We(t.toString)&&t.toString()==="[object FormData]"))},hy=yt("URLSearchParams"),[vy,xy,gy,yy]=["ReadableStream","Request","Response","Headers"].map(yt),by=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Tr(t,e,{allOwnKeys:a=!1}={}){if(t===null||typeof t>"u")return;let s,r;if(typeof t!="object"&&(t=[t]),Qa(t))for(s=0,r=t.length;s<r;s++)e.call(null,t[s],s,t);else{let n=a?Object.getOwnPropertyNames(t):Object.keys(t),i=n.length,o;for(s=0;s<i;s++)o=n[s],e.call(null,t[o],o,t)}}function lu(t,e){e=e.toLowerCase();let a=Object.keys(t),s=a.length,r;for(;s-- >0;)if(r=a[s],e===r.toLowerCase())return r;return null}var Sa=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,pu=t=>!kr(t)&&t!==Sa;function wi(){let{caseless:t}=pu(this)&&this||{},e={},a=(s,r)=>{let n=t&&lu(e,r)||r;Vs(e[n])&&Vs(s)?e[n]=wi(e[n],s):Vs(s)?e[n]=wi({},s):Qa(s)?e[n]=s.slice():e[n]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Tr(arguments[s],a);return e}var _y=(t,e,a,{allOwnKeys:s}={})=>(Tr(e,(r,n)=>{a&&We(r)?t[n]=Rr(r,a):t[n]=r},{allOwnKeys:s}),t),wy=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Ey=(t,e,a,s)=>{t.prototype=Object.create(e.prototype,s),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),a&&Object.assign(t.prototype,a)},Sy=(t,e,a,s)=>{let r,n,i,o={};if(e=e||{},t==null)return e;do{for(r=Object.getOwnPropertyNames(t),n=r.length;n-- >0;)i=r[n],(!s||s(i,t,e))&&!o[i]&&(e[i]=t[i],o[i]=!0);t=a!==!1&&Ei(t)}while(t&&(!a||a(t,e))&&t!==Object.prototype);return e},Py=(t,e,a)=>{t=String(t),(a===void 0||a>t.length)&&(a=t.length),a-=e.length;let s=t.indexOf(e,a);return s!==-1&&s===a},Ry=t=>{if(!t)return null;if(Qa(t))return t;let e=t.length;if(!cu(e))return null;let a=new Array(e);for(;e-- >0;)a[e]=t[e];return a},ky=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&Ei(Uint8Array)),Ty=(t,e)=>{let s=(t&&t[Zs]).call(t),r;for(;(r=s.next())&&!r.done;){let n=r.value;e.call(t,n[0],n[1])}},Ay=(t,e)=>{let a,s=[];for(;(a=t.exec(e))!==null;)s.push(a);return s},Cy=yt("HTMLFormElement"),Oy=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(a,s,r){return s.toUpperCase()+r}),nu=(({hasOwnProperty:t})=>(e,a)=>t.call(e,a))(Object.prototype),jy=yt("RegExp"),uu=(t,e)=>{let a=Object.getOwnPropertyDescriptors(t),s={};Tr(a,(r,n)=>{let i;(i=e(r,n,t))!==!1&&(s[n]=i||r)}),Object.defineProperties(t,s)},Iy=t=>{uu(t,(e,a)=>{if(We(t)&&["arguments","caller","callee"].indexOf(a)!==-1)return!1;let s=t[a];if(We(s)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+a+"'")})}})},Dy=(t,e)=>{let a={},s=r=>{r.forEach(n=>{a[n]=!0})};return Qa(t)?s(t):s(String(t).split(e)),a},Ny=()=>{},Fy=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function Ly(t){return!!(t&&We(t.append)&&t[iu]==="FormData"&&t[Zs])}var $y=t=>{let e=new Array(10),a=(s,r)=>{if(Ks(s)){if(e.indexOf(s)>=0)return;if(!("toJSON"in s)){e[r]=s;let n=Qa(s)?[]:{};return Tr(s,(i,o)=>{let p=a(i,r+1);!kr(p)&&(n[o]=p)}),e[r]=void 0,n}}return s};return a(t,0)},qy=yt("AsyncFunction"),Uy=t=>t&&(Ks(t)||We(t))&&We(t.then)&&We(t.catch),du=((t,e)=>t?setImmediate:e?((a,s)=>(Sa.addEventListener("message",({source:r,data:n})=>{r===Sa&&n===a&&s.length&&s.shift()()},!1),r=>{s.push(r),Sa.postMessage(a,"*")}))(`axios@${Math.random()}`,[]):a=>setTimeout(a))(typeof setImmediate=="function",We(Sa.postMessage)),zy=typeof queueMicrotask<"u"?queueMicrotask.bind(Sa):typeof process<"u"&&process.nextTick||du,My=t=>t!=null&&We(t[Zs]),b={isArray:Qa,isArrayBuffer:ou,isBuffer:ny,isFormData:fy,isArrayBufferView:iy,isString:oy,isNumber:cu,isBoolean:cy,isObject:Ks,isPlainObject:Vs,isReadableStream:vy,isRequest:xy,isResponse:gy,isHeaders:yy,isUndefined:kr,isDate:ly,isFile:py,isBlob:uy,isRegExp:jy,isFunction:We,isStream:my,isURLSearchParams:hy,isTypedArray:ky,isFileList:dy,forEach:Tr,merge:wi,extend:_y,trim:by,stripBOM:wy,inherits:Ey,toFlatObject:Sy,kindOf:Gs,kindOfTest:yt,endsWith:Py,toArray:Ry,forEachEntry:Ty,matchAll:Ay,isHTMLForm:Cy,hasOwnProperty:nu,hasOwnProp:nu,reduceDescriptors:uu,freezeMethods:Iy,toObjectSet:Dy,toCamelCase:Oy,noop:Ny,toFiniteNumber:Fy,findKey:lu,global:Sa,isContextDefined:pu,isSpecCompliantForm:Ly,toJSONObject:$y,isAsyncFn:qy,isThenable:Uy,setImmediate:du,asap:zy,isIterable:My};function Wa(t,e,a,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),a&&(this.config=a),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}b.inherits(Wa,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});var mu=Wa.prototype,fu={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{fu[t]={value:t}});Object.defineProperties(Wa,fu);Object.defineProperty(mu,"isAxiosError",{value:!0});Wa.from=(t,e,a,s,r,n)=>{let i=Object.create(mu);return b.toFlatObject(t,i,function(p){return p!==Error.prototype},o=>o!=="isAxiosError"),Wa.call(i,t.message,e,a,s,r),i.cause=t,i.name=t.name,n&&Object.assign(i,n),i};var q=Wa;var gm=Be(xm(),1),on=gm.default;function Bi(t){return b.isPlainObject(t)||b.isArray(t)}function bm(t){return b.endsWith(t,"[]")?t.slice(0,-2):t}function ym(t,e,a){return t?t.concat(e).map(function(r,n){return r=bm(r),!a&&n?"["+r+"]":r}).join(a?".":""):e}function k0(t){return b.isArray(t)&&!t.some(Bi)}var T0=b.toFlatObject(b,{},null,function(e){return/^is[A-Z]/.test(e)});function A0(t,e,a){if(!b.isObject(t))throw new TypeError("target must be an object");e=e||new(on||FormData),a=b.toFlatObject(a,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,h){return!b.isUndefined(h[g])});let s=a.metaTokens,r=a.visitor||l,n=a.dots,i=a.indexes,p=(a.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(e);if(!b.isFunction(r))throw new TypeError("visitor must be a function");function c(f){if(f===null)return"";if(b.isDate(f))return f.toISOString();if(b.isBoolean(f))return f.toString();if(!p&&b.isBlob(f))throw new q("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(f)||b.isTypedArray(f)?p&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function l(f,g,h){let w=f;if(f&&!h&&typeof f=="object"){if(b.endsWith(g,"{}"))g=s?g:g.slice(0,-2),f=JSON.stringify(f);else if(b.isArray(f)&&k0(f)||(b.isFileList(f)||b.endsWith(g,"[]"))&&(w=b.toArray(f)))return g=bm(g),w.forEach(function(E,k){!(b.isUndefined(E)||E===null)&&e.append(i===!0?ym([g],k,n):i===null?g:g+"[]",c(E))}),!1}return Bi(f)?!0:(e.append(ym(h,g,n),c(f)),!1)}let m=[],x=Object.assign(T0,{defaultVisitor:l,convertValue:c,isVisitable:Bi});function d(f,g){if(!b.isUndefined(f)){if(m.indexOf(f)!==-1)throw Error("Circular reference detected in "+g.join("."));m.push(f),b.forEach(f,function(w,R){(!(b.isUndefined(w)||w===null)&&r.call(e,w,b.isString(R)?R.trim():R,g,x))===!0&&d(w,g?g.concat(R):[R])}),m.pop()}}if(!b.isObject(t))throw new TypeError("data must be an object");return d(t),e}var Xt=A0;function _m(t){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(s){return e[s]})}function wm(t,e){this._pairs=[],t&&Xt(t,this,e)}var Em=wm.prototype;Em.append=function(e,a){this._pairs.push([e,a])};Em.toString=function(e){let a=e?function(s){return e.call(this,s,_m)}:_m;return this._pairs.map(function(r){return a(r[0])+"="+a(r[1])},"").join("&")};var Sm=wm;function C0(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ra(t,e,a){if(!e)return t;let s=a&&a.encode||C0;b.isFunction(a)&&(a={serialize:a});let r=a&&a.serialize,n;if(r?n=r(e,a):n=b.isURLSearchParams(e)?e.toString():new Sm(e,a).toString(s),n){let i=t.indexOf("#");i!==-1&&(t=t.slice(0,i)),t+=(t.indexOf("?")===-1?"?":"&")+n}return t}var Hi=class{constructor(){this.handlers=[]}use(e,a,s){return this.handlers.push({fulfilled:e,rejected:a,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){b.forEach(this.handlers,function(s){s!==null&&e(s)})}},Vi=Hi;var ar={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var Tm=Be(require("crypto"),1);var Pm=Be(require("url"),1),Rm=Pm.default.URLSearchParams;var Zi="abcdefghijklmnopqrstuvwxyz",km="0123456789",Am={DIGIT:km,ALPHA:Zi,ALPHA_DIGIT:Zi+Zi.toUpperCase()+km},O0=(t=16,e=Am.ALPHA_DIGIT)=>{let a="",{length:s}=e,r=new Uint32Array(t);Tm.default.randomFillSync(r);for(let n=0;n<t;n++)a+=e[r[n]%s];return a},Cm={isNode:!0,classes:{URLSearchParams:Rm,FormData:on,Blob:typeof Blob<"u"&&Blob||null},ALPHABET:Am,generateString:O0,protocols:["http","https","file","data"]};var Ki={};Ho(Ki,{hasBrowserEnv:()=>Ji,hasStandardBrowserEnv:()=>j0,hasStandardBrowserWebWorkerEnv:()=>I0,navigator:()=>Gi,origin:()=>D0});var Ji=typeof window<"u"&&typeof document<"u",Gi=typeof navigator=="object"&&navigator||void 0,j0=Ji&&(!Gi||["ReactNative","NativeScript","NS"].indexOf(Gi.product)<0),I0=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",D0=Ji&&window.location.href||"http://localhost";var me={...Ki,...Cm};function Qi(t,e){return Xt(t,new me.classes.URLSearchParams,Object.assign({visitor:function(a,s,r,n){return me.isNode&&b.isBuffer(a)?(this.append(s,a.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},e))}function N0(t){return b.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function F0(t){let e={},a=Object.keys(t),s,r=a.length,n;for(s=0;s<r;s++)n=a[s],e[n]=t[n];return e}function L0(t){function e(a,s,r,n){let i=a[n++];if(i==="__proto__")return!0;let o=Number.isFinite(+i),p=n>=a.length;return i=!i&&b.isArray(r)?r.length:i,p?(b.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!o):((!r[i]||!b.isObject(r[i]))&&(r[i]=[]),e(a,s,r[i],n)&&b.isArray(r[i])&&(r[i]=F0(r[i])),!o)}if(b.isFormData(t)&&b.isFunction(t.entries)){let a={};return b.forEachEntry(t,(s,r)=>{e(N0(s),r,a,0)}),a}return null}var cn=L0;function $0(t,e,a){if(b.isString(t))try{return(e||JSON.parse)(t),b.trim(t)}catch(s){if(s.name!=="SyntaxError")throw s}return(a||JSON.stringify)(t)}var Wi={transitional:ar,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let s=a.getContentType()||"",r=s.indexOf("application/json")>-1,n=b.isObject(e);if(n&&b.isHTMLForm(e)&&(e=new FormData(e)),b.isFormData(e))return r?JSON.stringify(cn(e)):e;if(b.isArrayBuffer(e)||b.isBuffer(e)||b.isStream(e)||b.isFile(e)||b.isBlob(e)||b.isReadableStream(e))return e;if(b.isArrayBufferView(e))return e.buffer;if(b.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(n){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Qi(e,this.formSerializer).toString();if((o=b.isFileList(e))||s.indexOf("multipart/form-data")>-1){let p=this.env&&this.env.FormData;return Xt(o?{"files[]":e}:e,p&&new p,this.formSerializer)}}return n||r?(a.setContentType("application/json",!1),$0(e)):e}],transformResponse:[function(e){let a=this.transitional||Wi.transitional,s=a&&a.forcedJSONParsing,r=this.responseType==="json";if(b.isResponse(e)||b.isReadableStream(e))return e;if(e&&b.isString(e)&&(s&&!this.responseType||r)){let i=!(a&&a.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(i)throw o.name==="SyntaxError"?q.from(o,q.ERR_BAD_RESPONSE,this,null,this.response):o}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:me.classes.FormData,Blob:me.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],t=>{Wi.headers[t]={}});var rr=Wi;var q0=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Om=t=>{let e={},a,s,r;return t&&t.split(`
`).forEach(function(i){r=i.indexOf(":"),a=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!a||e[a]&&q0[a])&&(a==="set-cookie"?e[a]?e[a].push(s):e[a]=[s]:e[a]=e[a]?e[a]+", "+s:s)}),e};var jm=Symbol("internals");function Ir(t){return t&&String(t).trim().toLowerCase()}function ln(t){return t===!1||t==null?t:b.isArray(t)?t.map(ln):String(t)}function U0(t){let e=Object.create(null),a=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,s;for(;s=a.exec(t);)e[s[1]]=s[2];return e}var z0=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function Xi(t,e,a,s,r){if(b.isFunction(s))return s.call(this,e,a);if(r&&(e=a),!!b.isString(e)){if(b.isString(s))return e.indexOf(s)!==-1;if(b.isRegExp(s))return s.test(e)}}function M0(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,s)=>a.toUpperCase()+s)}function B0(t,e){let a=b.toCamelCase(" "+e);["get","set","has"].forEach(s=>{Object.defineProperty(t,s+a,{value:function(r,n,i){return this[s].call(this,e,r,n,i)},configurable:!0})})}var sr=class{constructor(e){e&&this.set(e)}set(e,a,s){let r=this;function n(o,p,c){let l=Ir(p);if(!l)throw new Error("header name must be a non-empty string");let m=b.findKey(r,l);(!m||r[m]===void 0||c===!0||c===void 0&&r[m]!==!1)&&(r[m||p]=ln(o))}let i=(o,p)=>b.forEach(o,(c,l)=>n(c,l,p));if(b.isPlainObject(e)||e instanceof this.constructor)i(e,a);else if(b.isString(e)&&(e=e.trim())&&!z0(e))i(Om(e),a);else if(b.isObject(e)&&b.isIterable(e)){let o={},p,c;for(let l of e){if(!b.isArray(l))throw TypeError("Object iterator must return a key-value pair");o[c=l[0]]=(p=o[c])?b.isArray(p)?[...p,l[1]]:[p,l[1]]:l[1]}i(o,a)}else e!=null&&n(a,e,s);return this}get(e,a){if(e=Ir(e),e){let s=b.findKey(this,e);if(s){let r=this[s];if(!a)return r;if(a===!0)return U0(r);if(b.isFunction(a))return a.call(this,r,s);if(b.isRegExp(a))return a.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=Ir(e),e){let s=b.findKey(this,e);return!!(s&&this[s]!==void 0&&(!a||Xi(this,this[s],s,a)))}return!1}delete(e,a){let s=this,r=!1;function n(i){if(i=Ir(i),i){let o=b.findKey(s,i);o&&(!a||Xi(s,s[o],o,a))&&(delete s[o],r=!0)}}return b.isArray(e)?e.forEach(n):n(e),r}clear(e){let a=Object.keys(this),s=a.length,r=!1;for(;s--;){let n=a[s];(!e||Xi(this,this[n],n,e,!0))&&(delete this[n],r=!0)}return r}normalize(e){let a=this,s={};return b.forEach(this,(r,n)=>{let i=b.findKey(s,n);if(i){a[i]=ln(r),delete a[n];return}let o=e?M0(n):String(n).trim();o!==n&&delete a[n],a[o]=ln(r),s[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return b.forEach(this,(s,r)=>{s!=null&&s!==!1&&(a[r]=e&&b.isArray(s)?s.join(", "):s)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let s=new this(e);return a.forEach(r=>s.set(r)),s}static accessor(e){let s=(this[jm]=this[jm]={accessors:{}}).accessors,r=this.prototype;function n(i){let o=Ir(i);s[o]||(B0(r,i),s[o]=!0)}return b.isArray(e)?e.forEach(n):n(e),this}};sr.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(sr.prototype,({value:t},e)=>{let a=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(s){this[a]=s}}});b.freezeMethods(sr);var Re=sr;function Dr(t,e){let a=this||rr,s=e||a,r=Re.from(s.headers),n=s.data;return b.forEach(t,function(o){n=o.call(a,n,r.normalize(),e?e.status:void 0)}),r.normalize(),n}function Nr(t){return!!(t&&t.__CANCEL__)}function Im(t,e,a){q.call(this,t??"canceled",q.ERR_CANCELED,e,a),this.name="CanceledError"}b.inherits(Im,q,{__CANCEL__:!0});var st=Im;function At(t,e,a){let s=a.config.validateStatus;!a.status||!s||s(a.status)?t(a):e(new q("Request failed with status code "+a.status,[q.ERR_BAD_REQUEST,q.ERR_BAD_RESPONSE][Math.floor(a.status/100)-4],a.config,a.request,a))}function Yi(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function eo(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function ka(t,e,a){let s=!Yi(e);return t&&(s||a==!1)?eo(t,e):e}var xf=Be(Nm(),1),gf=Be(require("http"),1),yf=Be(require("https"),1),bf=Be(require("util"),1),_f=Be(ef(),1),Mt=Be(require("zlib"),1);var Oa="1.10.0";function Ur(t){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}var T_=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;function go(t,e,a){let s=a&&a.Blob||me.classes.Blob,r=Ur(t);if(e===void 0&&s&&(e=!0),r==="data"){t=r.length?t.slice(r.length+1):t;let n=T_.exec(t);if(!n)throw new q("Invalid URL",q.ERR_INVALID_URL);let i=n[1],o=n[2],p=n[3],c=Buffer.from(decodeURIComponent(p),o?"base64":"utf8");if(e){if(!s)throw new q("Blob is not supported",q.ERR_NOT_SUPPORT);return new s([c],{type:i})}return c}throw new q("Unsupported protocol "+r,q.ERR_NOT_SUPPORT)}var Ia=Be(require("stream"),1);var tf=Be(require("stream"),1);var yo=Symbol("internals"),bo=class extends tf.default.Transform{constructor(e){e=b.toFlatObject(e,{maxRate:0,chunkSize:64*1024,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(s,r)=>!b.isUndefined(r[s])),super({readableHighWaterMark:e.chunkSize});let a=this[yo]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",s=>{s==="progress"&&(a.isCaptured||(a.isCaptured=!0))})}_read(e){let a=this[yo];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,s){let r=this[yo],n=r.maxRate,i=this.readableHighWaterMark,o=r.timeWindow,p=1e3/o,c=n/p,l=r.minChunkSize!==!1?Math.max(r.minChunkSize,c*.01):0,m=(d,f)=>{let g=Buffer.byteLength(d);r.bytesSeen+=g,r.bytes+=g,r.isCaptured&&this.emit("progress",r.bytesSeen),this.push(d)?process.nextTick(f):r.onReadCallback=()=>{r.onReadCallback=null,process.nextTick(f)}},x=(d,f)=>{let g=Buffer.byteLength(d),h=null,w=i,R,E=0;if(n){let k=Date.now();(!r.ts||(E=k-r.ts)>=o)&&(r.ts=k,R=c-r.bytes,r.bytes=R<0?-R:0,E=0),R=c-r.bytes}if(n){if(R<=0)return setTimeout(()=>{f(null,d)},o-E);R<w&&(w=R)}w&&g>w&&g-w>l&&(h=d.subarray(w),d=d.subarray(0,w)),m(d,h?()=>{process.nextTick(f,null,h)}:f)};x(e,function d(f,g){if(f)return s(f);g?x(g,d):s(null)})}},_o=bo;var wf=require("events");var rf=Be(require("util"),1),sf=require("stream");var{asyncIterator:af}=Symbol,A_=async function*(t){t.stream?yield*t.stream():t.arrayBuffer?yield await t.arrayBuffer():t[af]?yield*t[af]():yield t},fn=A_;var C_=me.ALPHABET.ALPHA_DIGIT+"-_",zr=typeof TextEncoder=="function"?new TextEncoder:new rf.default.TextEncoder,ja=`\r
`,O_=zr.encode(ja),j_=2,wo=class{constructor(e,a){let{escapeName:s}=this.constructor,r=b.isString(a),n=`Content-Disposition: form-data; name="${s(e)}"${!r&&a.name?`; filename="${s(a.name)}"`:""}${ja}`;r?a=zr.encode(String(a).replace(/\r?\n|\r\n?/g,ja)):n+=`Content-Type: ${a.type||"application/octet-stream"}${ja}`,this.headers=zr.encode(n+ja),this.contentLength=r?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+j_,this.name=e,this.value=a}async*encode(){yield this.headers;let{value:e}=this;b.isTypedArray(e)?yield e:yield*fn(e),yield O_}static escapeName(e){return String(e).replace(/[\r\n"]/g,a=>({"\r":"%0D","\n":"%0A",'"':"%22"})[a])}},I_=(t,e,a)=>{let{tag:s="form-data-boundary",size:r=25,boundary:n=s+"-"+me.generateString(r,C_)}=a||{};if(!b.isFormData(t))throw TypeError("FormData instance required");if(n.length<1||n.length>70)throw Error("boundary must be 10-70 characters long");let i=zr.encode("--"+n+ja),o=zr.encode("--"+n+"--"+ja),p=o.byteLength,c=Array.from(t.entries()).map(([m,x])=>{let d=new wo(m,x);return p+=d.size,d});p+=i.byteLength*c.length,p=b.toFiniteNumber(p);let l={"Content-Type":`multipart/form-data; boundary=${n}`};return Number.isFinite(p)&&(l["Content-Length"]=p),e&&e(l),sf.Readable.from(async function*(){for(let m of c)yield i,yield*m.encode();yield o}())},nf=I_;var of=Be(require("stream"),1),Eo=class extends of.default.Transform{__transform(e,a,s){this.push(e),s()}_transform(e,a,s){if(e.length!==0&&(this._transform=this.__transform,e[0]!==120)){let r=Buffer.alloc(2);r[0]=120,r[1]=156,this.push(r,a)}this.__transform(e,a,s)}},cf=Eo;var D_=(t,e)=>b.isAsyncFn(t)?function(...a){let s=a.pop();t.apply(this,a).then(r=>{try{e?s(null,...e(r)):s(null,r)}catch(n){s(n)}},s)}:t,lf=D_;function N_(t,e){t=t||10;let a=new Array(t),s=new Array(t),r=0,n=0,i;return e=e!==void 0?e:1e3,function(p){let c=Date.now(),l=s[n];i||(i=c),a[r]=p,s[r]=c;let m=n,x=0;for(;m!==r;)x+=a[m++],m=m%t;if(r=(r+1)%t,r===n&&(n=(n+1)%t),c-i<e)return;let d=l&&c-l;return d?Math.round(x*1e3/d):void 0}}var pf=N_;function F_(t,e){let a=0,s=1e3/e,r,n,i=(c,l=Date.now())=>{a=l,r=null,n&&(clearTimeout(n),n=null),t.apply(null,c)};return[(...c)=>{let l=Date.now(),m=l-a;m>=s?i(c,l):(r=c,n||(n=setTimeout(()=>{n=null,i(r)},s-m)))},()=>r&&i(r)]}var uf=F_;var zt=(t,e,a=3)=>{let s=0,r=pf(50,250);return uf(n=>{let i=n.loaded,o=n.lengthComputable?n.total:void 0,p=i-s,c=r(p),l=i<=o;s=i;let m={loaded:i,total:o,progress:o?i/o:void 0,bytes:p,rate:c||void 0,estimated:c&&o&&l?(o-i)/c:void 0,event:n,lengthComputable:o!=null,[e?"download":"upload"]:!0};t(m)},a)},lr=(t,e)=>{let a=t!=null;return[s=>e[0]({lengthComputable:a,total:t,loaded:s}),e[1]]},pr=t=>(...e)=>b.asap(()=>t(...e));var df={flush:Mt.default.constants.Z_SYNC_FLUSH,finishFlush:Mt.default.constants.Z_SYNC_FLUSH},L_={flush:Mt.default.constants.BROTLI_OPERATION_FLUSH,finishFlush:Mt.default.constants.BROTLI_OPERATION_FLUSH},mf=b.isFunction(Mt.default.createBrotliDecompress),{http:$_,https:q_}=_f.default,U_=/https:?/,ff=me.protocols.map(t=>t+":"),hf=(t,[e,a])=>(t.on("end",a).on("error",a),e);function z_(t,e){t.beforeRedirects.proxy&&t.beforeRedirects.proxy(t),t.beforeRedirects.config&&t.beforeRedirects.config(t,e)}function Ef(t,e,a){let s=e;if(!s&&s!==!1){let r=xf.default.getProxyForUrl(a);r&&(s=new URL(r))}if(s){if(s.username&&(s.auth=(s.username||"")+":"+(s.password||"")),s.auth){(s.auth.username||s.auth.password)&&(s.auth=(s.auth.username||"")+":"+(s.auth.password||""));let n=Buffer.from(s.auth,"utf8").toString("base64");t.headers["Proxy-Authorization"]="Basic "+n}t.headers.host=t.hostname+(t.port?":"+t.port:"");let r=s.hostname||s.host;t.hostname=r,t.host=r,t.port=s.port,t.path=a,s.protocol&&(t.protocol=s.protocol.includes(":")?s.protocol:`${s.protocol}:`)}t.beforeRedirects.proxy=function(n){Ef(n,e,n.href)}}var M_=typeof process<"u"&&b.kindOf(process)==="process",B_=t=>new Promise((e,a)=>{let s,r,n=(p,c)=>{r||(r=!0,s&&s(p,c))},i=p=>{n(p),e(p)},o=p=>{n(p,!0),a(p)};t(i,o,p=>s=p).catch(o)}),H_=({address:t,family:e})=>{if(!b.isString(t))throw TypeError("address must be a string");return{address:t,family:e||(t.indexOf(".")<0?6:4)}},vf=(t,e)=>H_(b.isObject(t)?t:{address:t,family:e}),Sf=M_&&function(e){return B_(async function(s,r,n){let{data:i,lookup:o,family:p}=e,{responseType:c,responseEncoding:l}=e,m=e.method.toUpperCase(),x,d=!1,f;if(o){let F=lf(o,L=>b.isArray(L)?L:[L]);o=(L,Q,K)=>{F(L,Q,(re,be,Ne)=>{if(re)return K(re);let Y=b.isArray(be)?be.map(pe=>vf(pe)):[vf(be,Ne)];Q.all?K(re,Y):K(re,Y[0].address,Y[0].family)})}}let g=new wf.EventEmitter,h=()=>{e.cancelToken&&e.cancelToken.unsubscribe(w),e.signal&&e.signal.removeEventListener("abort",w),g.removeAllListeners()};n((F,L)=>{x=!0,L&&(d=!0,h())});function w(F){g.emit("abort",!F||F.type?new st(null,e,f):F)}g.once("abort",r),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(w),e.signal&&(e.signal.aborted?w():e.signal.addEventListener("abort",w)));let R=ka(e.baseURL,e.url,e.allowAbsoluteUrls),E=new URL(R,me.hasBrowserEnv?me.origin:void 0),k=E.protocol||ff[0];if(k==="data:"){let F;if(m!=="GET")return At(s,r,{status:405,statusText:"method not allowed",headers:{},config:e});try{F=go(e.url,c==="blob",{Blob:e.env&&e.env.Blob})}catch(L){throw q.from(L,q.ERR_BAD_REQUEST,e)}return c==="text"?(F=F.toString(l),(!l||l==="utf8")&&(F=b.stripBOM(F))):c==="stream"&&(F=Ia.default.Readable.from(F)),At(s,r,{data:F,status:200,statusText:"OK",headers:new Re,config:e})}if(ff.indexOf(k)===-1)return r(new q("Unsupported protocol "+k,q.ERR_BAD_REQUEST,e));let T=Re.from(e.headers).normalize();T.set("User-Agent","axios/"+Oa,!1);let{onUploadProgress:C,onDownloadProgress:B}=e,U=e.maxRate,I,Z;if(b.isSpecCompliantForm(i)){let F=T.getContentType(/boundary=([-_\w\d]{10,70})/i);i=nf(i,L=>{T.set(L)},{tag:`axios-${Oa}-boundary`,boundary:F&&F[1]||void 0})}else if(b.isFormData(i)&&b.isFunction(i.getHeaders)){if(T.set(i.getHeaders()),!T.hasContentLength())try{let F=await bf.default.promisify(i.getLength).call(i);Number.isFinite(F)&&F>=0&&T.setContentLength(F)}catch{}}else if(b.isBlob(i)||b.isFile(i))i.size&&T.setContentType(i.type||"application/octet-stream"),T.setContentLength(i.size||0),i=Ia.default.Readable.from(fn(i));else if(i&&!b.isStream(i)){if(!Buffer.isBuffer(i))if(b.isArrayBuffer(i))i=Buffer.from(new Uint8Array(i));else if(b.isString(i))i=Buffer.from(i,"utf-8");else return r(new q("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",q.ERR_BAD_REQUEST,e));if(T.setContentLength(i.length,!1),e.maxBodyLength>-1&&i.length>e.maxBodyLength)return r(new q("Request body larger than maxBodyLength limit",q.ERR_BAD_REQUEST,e))}let G=b.toFiniteNumber(T.getContentLength());b.isArray(U)?(I=U[0],Z=U[1]):I=Z=U,i&&(C||I)&&(b.isStream(i)||(i=Ia.default.Readable.from(i,{objectMode:!1})),i=Ia.default.pipeline([i,new _o({maxRate:b.toFiniteNumber(I)})],b.noop),C&&i.on("progress",hf(i,lr(G,zt(pr(C),!1,3)))));let M;if(e.auth){let F=e.auth.username||"",L=e.auth.password||"";M=F+":"+L}if(!M&&E.username){let F=E.username,L=E.password;M=F+":"+L}M&&T.delete("authorization");let D;try{D=Ra(E.pathname+E.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(F){let L=new Error(F.message);return L.config=e,L.url=e.url,L.exists=!0,r(L)}T.set("Accept-Encoding","gzip, compress, deflate"+(mf?", br":""),!1);let O={path:D,method:m,headers:T.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:M,protocol:k,family:p,beforeRedirect:z_,beforeRedirects:{}};!b.isUndefined(o)&&(O.lookup=o),e.socketPath?O.socketPath=e.socketPath:(O.hostname=E.hostname.startsWith("[")?E.hostname.slice(1,-1):E.hostname,O.port=E.port,Ef(O,e.proxy,k+"//"+E.hostname+(E.port?":"+E.port:"")+O.path));let H,le=U_.test(O.protocol);if(O.agent=le?e.httpsAgent:e.httpAgent,e.transport?H=e.transport:e.maxRedirects===0?H=le?yf.default:gf.default:(e.maxRedirects&&(O.maxRedirects=e.maxRedirects),e.beforeRedirect&&(O.beforeRedirects.config=e.beforeRedirect),H=le?q_:$_),e.maxBodyLength>-1?O.maxBodyLength=e.maxBodyLength:O.maxBodyLength=1/0,e.insecureHTTPParser&&(O.insecureHTTPParser=e.insecureHTTPParser),f=H.request(O,function(L){if(f.destroyed)return;let Q=[L],K=+L.headers["content-length"];if(B||Z){let pe=new _o({maxRate:b.toFiniteNumber(Z)});B&&pe.on("progress",hf(pe,lr(K,zt(pr(B),!0,3)))),Q.push(pe)}let re=L,be=L.req||f;if(e.decompress!==!1&&L.headers["content-encoding"])switch((m==="HEAD"||L.statusCode===204)&&delete L.headers["content-encoding"],(L.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":Q.push(Mt.default.createUnzip(df)),delete L.headers["content-encoding"];break;case"deflate":Q.push(new cf),Q.push(Mt.default.createUnzip(df)),delete L.headers["content-encoding"];break;case"br":mf&&(Q.push(Mt.default.createBrotliDecompress(L_)),delete L.headers["content-encoding"])}re=Q.length>1?Ia.default.pipeline(Q,b.noop):Q[0];let Ne=Ia.default.finished(re,()=>{Ne(),h()}),Y={status:L.statusCode,statusText:L.statusMessage,headers:new Re(L.headers),config:e,request:be};if(c==="stream")Y.data=re,At(s,r,Y);else{let pe=[],Ce=0;re.on("data",function(_e){pe.push(_e),Ce+=_e.length,e.maxContentLength>-1&&Ce>e.maxContentLength&&(d=!0,re.destroy(),r(new q("maxContentLength size of "+e.maxContentLength+" exceeded",q.ERR_BAD_RESPONSE,e,be)))}),re.on("aborted",function(){if(d)return;let _e=new q("stream has been aborted",q.ERR_BAD_RESPONSE,e,be);re.destroy(_e),r(_e)}),re.on("error",function(_e){f.destroyed||r(q.from(_e,null,e,be))}),re.on("end",function(){try{let _e=pe.length===1?pe[0]:Buffer.concat(pe);c!=="arraybuffer"&&(_e=_e.toString(l),(!l||l==="utf8")&&(_e=b.stripBOM(_e))),Y.data=_e}catch(_e){return r(q.from(_e,null,e,Y.request,Y))}At(s,r,Y)})}g.once("abort",pe=>{re.destroyed||(re.emit("error",pe),re.destroy())})}),g.once("abort",F=>{r(F),f.destroy(F)}),f.on("error",function(L){r(q.from(L,null,e,f))}),f.on("socket",function(L){L.setKeepAlive(!0,1e3*60)}),e.timeout){let F=parseInt(e.timeout,10);if(Number.isNaN(F)){r(new q("error trying to parse `config.timeout` to int",q.ERR_BAD_OPTION_VALUE,e,f));return}f.setTimeout(F,function(){if(x)return;let Q=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",K=e.transitional||ar;e.timeoutErrorMessage&&(Q=e.timeoutErrorMessage),r(new q(Q,K.clarifyTimeoutError?q.ETIMEDOUT:q.ECONNABORTED,e,f)),w()})}if(b.isStream(i)){let F=!1,L=!1;i.on("end",()=>{F=!0}),i.once("error",Q=>{L=!0,f.destroy(Q)}),i.on("close",()=>{!F&&!L&&w(new st("Request stream has been aborted",e,f))}),i.pipe(f)}else f.end(i)})};var Pf=me.hasStandardBrowserEnv?((t,e)=>a=>(a=new URL(a,me.origin),t.protocol===a.protocol&&t.host===a.host&&(e||t.port===a.port)))(new URL(me.origin),me.navigator&&/(msie|trident)/i.test(me.navigator.userAgent)):()=>!0;var Rf=me.hasStandardBrowserEnv?{write(t,e,a,s,r,n){let i=[t+"="+encodeURIComponent(e)];b.isNumber(a)&&i.push("expires="+new Date(a).toGMTString()),b.isString(s)&&i.push("path="+s),b.isString(r)&&i.push("domain="+r),n===!0&&i.push("secure"),document.cookie=i.join("; ")},read(t){let e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};var kf=t=>t instanceof Re?{...t}:t;function _t(t,e){e=e||{};let a={};function s(c,l,m,x){return b.isPlainObject(c)&&b.isPlainObject(l)?b.merge.call({caseless:x},c,l):b.isPlainObject(l)?b.merge({},l):b.isArray(l)?l.slice():l}function r(c,l,m,x){if(b.isUndefined(l)){if(!b.isUndefined(c))return s(void 0,c,m,x)}else return s(c,l,m,x)}function n(c,l){if(!b.isUndefined(l))return s(void 0,l)}function i(c,l){if(b.isUndefined(l)){if(!b.isUndefined(c))return s(void 0,c)}else return s(void 0,l)}function o(c,l,m){if(m in e)return s(c,l);if(m in t)return s(void 0,c)}let p={url:n,method:n,data:n,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:o,headers:(c,l,m)=>r(kf(c),kf(l),m,!0)};return b.forEach(Object.keys(Object.assign({},t,e)),function(l){let m=p[l]||r,x=m(t[l],e[l],l);b.isUndefined(x)&&m!==o||(a[l]=x)}),a}var hn=t=>{let e=_t({},t),{data:a,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:n,headers:i,auth:o}=e;e.headers=i=Re.from(i),e.url=Ra(ka(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),o&&i.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let p;if(b.isFormData(a)){if(me.hasStandardBrowserEnv||me.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((p=i.getContentType())!==!1){let[c,...l]=p?p.split(";").map(m=>m.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...l].join("; "))}}if(me.hasStandardBrowserEnv&&(s&&b.isFunction(s)&&(s=s(e)),s||s!==!1&&Pf(e.url))){let c=r&&n&&Rf.read(n);c&&i.set(r,c)}return e};var V_=typeof XMLHttpRequest<"u",Tf=V_&&function(t){return new Promise(function(a,s){let r=hn(t),n=r.data,i=Re.from(r.headers).normalize(),{responseType:o,onUploadProgress:p,onDownloadProgress:c}=r,l,m,x,d,f;function g(){d&&d(),f&&f(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let h=new XMLHttpRequest;h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout;function w(){if(!h)return;let E=Re.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),T={data:!o||o==="text"||o==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:E,config:t,request:h};At(function(B){a(B),g()},function(B){s(B),g()},T),h=null}"onloadend"in h?h.onloadend=w:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(w)},h.onabort=function(){h&&(s(new q("Request aborted",q.ECONNABORTED,t,h)),h=null)},h.onerror=function(){s(new q("Network Error",q.ERR_NETWORK,t,h)),h=null},h.ontimeout=function(){let k=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded",T=r.transitional||ar;r.timeoutErrorMessage&&(k=r.timeoutErrorMessage),s(new q(k,T.clarifyTimeoutError?q.ETIMEDOUT:q.ECONNABORTED,t,h)),h=null},n===void 0&&i.setContentType(null),"setRequestHeader"in h&&b.forEach(i.toJSON(),function(k,T){h.setRequestHeader(T,k)}),b.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),o&&o!=="json"&&(h.responseType=r.responseType),c&&([x,f]=zt(c,!0),h.addEventListener("progress",x)),p&&h.upload&&([m,d]=zt(p),h.upload.addEventListener("progress",m),h.upload.addEventListener("loadend",d)),(r.cancelToken||r.signal)&&(l=E=>{h&&(s(!E||E.type?new st(null,t,h):E),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));let R=Ur(r.url);if(R&&me.protocols.indexOf(R)===-1){s(new q("Unsupported protocol "+R+":",q.ERR_BAD_REQUEST,t));return}h.send(n||null)})};var Z_=(t,e)=>{let{length:a}=t=t?t.filter(Boolean):[];if(e||a){let s=new AbortController,r,n=function(c){if(!r){r=!0,o();let l=c instanceof Error?c:this.reason;s.abort(l instanceof q?l:new st(l instanceof Error?l.message:l))}},i=e&&setTimeout(()=>{i=null,n(new q(`timeout ${e} of ms exceeded`,q.ETIMEDOUT))},e),o=()=>{t&&(i&&clearTimeout(i),i=null,t.forEach(c=>{c.unsubscribe?c.unsubscribe(n):c.removeEventListener("abort",n)}),t=null)};t.forEach(c=>c.addEventListener("abort",n));let{signal:p}=s;return p.unsubscribe=()=>b.asap(o),p}},Af=Z_;var G_=function*(t,e){let a=t.byteLength;if(!e||a<e){yield t;return}let s=0,r;for(;s<a;)r=s+e,yield t.slice(s,r),s=r},J_=async function*(t,e){for await(let a of K_(t))yield*G_(a,e)},K_=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}let e=t.getReader();try{for(;;){let{done:a,value:s}=await e.read();if(a)break;yield s}}finally{await e.cancel()}},So=(t,e,a,s)=>{let r=J_(t,e),n=0,i,o=p=>{i||(i=!0,s&&s(p))};return new ReadableStream({async pull(p){try{let{done:c,value:l}=await r.next();if(c){o(),p.close();return}let m=l.byteLength;if(a){let x=n+=m;a(x)}p.enqueue(new Uint8Array(l))}catch(c){throw o(c),c}},cancel(p){return o(p),r.return()}},{highWaterMark:2})};var xn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Of=xn&&typeof ReadableStream=="function",Q_=xn&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),jf=(t,...e)=>{try{return!!t(...e)}catch{return!1}},W_=Of&&jf(()=>{let t=!1,e=new Request(me.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),Cf=64*1024,Po=Of&&jf(()=>b.isReadableStream(new Response("").body)),vn={stream:Po&&(t=>t.body)};xn&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!vn[e]&&(vn[e]=b.isFunction(t[e])?a=>a[e]():(a,s)=>{throw new q(`Response type '${e}' is not supported`,q.ERR_NOT_SUPPORT,s)})})})(new Response);var X_=async t=>{if(t==null)return 0;if(b.isBlob(t))return t.size;if(b.isSpecCompliantForm(t))return(await new Request(me.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(b.isArrayBufferView(t)||b.isArrayBuffer(t))return t.byteLength;if(b.isURLSearchParams(t)&&(t=t+""),b.isString(t))return(await Q_(t)).byteLength},Y_=async(t,e)=>{let a=b.toFiniteNumber(t.getContentLength());return a??X_(e)},If=xn&&(async t=>{let{url:e,method:a,data:s,signal:r,cancelToken:n,timeout:i,onDownloadProgress:o,onUploadProgress:p,responseType:c,headers:l,withCredentials:m="same-origin",fetchOptions:x}=hn(t);c=c?(c+"").toLowerCase():"text";let d=Af([r,n&&n.toAbortSignal()],i),f,g=d&&d.unsubscribe&&(()=>{d.unsubscribe()}),h;try{if(p&&W_&&a!=="get"&&a!=="head"&&(h=await Y_(l,s))!==0){let T=new Request(e,{method:"POST",body:s,duplex:"half"}),C;if(b.isFormData(s)&&(C=T.headers.get("content-type"))&&l.setContentType(C),T.body){let[B,U]=lr(h,zt(pr(p)));s=So(T.body,Cf,B,U)}}b.isString(m)||(m=m?"include":"omit");let w="credentials"in Request.prototype;f=new Request(e,{...x,signal:d,method:a.toUpperCase(),headers:l.normalize().toJSON(),body:s,duplex:"half",credentials:w?m:void 0});let R=await fetch(f,x),E=Po&&(c==="stream"||c==="response");if(Po&&(o||E&&g)){let T={};["status","statusText","headers"].forEach(I=>{T[I]=R[I]});let C=b.toFiniteNumber(R.headers.get("content-length")),[B,U]=o&&lr(C,zt(pr(o),!0))||[];R=new Response(So(R.body,Cf,B,()=>{U&&U(),g&&g()}),T)}c=c||"text";let k=await vn[b.findKey(vn,c)||"text"](R,t);return!E&&g&&g(),await new Promise((T,C)=>{At(T,C,{data:k,headers:Re.from(R.headers),status:R.status,statusText:R.statusText,config:t,request:f})})}catch(w){throw g&&g(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new q("Network Error",q.ERR_NETWORK,t,f),{cause:w.cause||w}):q.from(w,w&&w.code,t,f)}});var Ro={http:Sf,xhr:Tf,fetch:If};b.forEach(Ro,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});var Df=t=>`- ${t}`,ew=t=>b.isFunction(t)||t===null||t===!1,gn={getAdapter:t=>{t=b.isArray(t)?t:[t];let{length:e}=t,a,s,r={};for(let n=0;n<e;n++){a=t[n];let i;if(s=a,!ew(a)&&(s=Ro[(i=String(a)).toLowerCase()],s===void 0))throw new q(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+n]=s}if(!s){let n=Object.entries(r).map(([o,p])=>`adapter ${o} `+(p===!1?"is not supported by the environment":"is not available in the build")),i=e?n.length>1?`since :
`+n.map(Df).join(`
`):" "+Df(n[0]):"as no adapter specified";throw new q("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:Ro};function ko(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new st(null,t)}function yn(t){return ko(t),t.headers=Re.from(t.headers),t.data=Dr.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),gn.getAdapter(t.adapter||rr.adapter)(t).then(function(s){return ko(t),s.data=Dr.call(t,t.transformResponse,s),s.headers=Re.from(s.headers),s},function(s){return Nr(s)||(ko(t),s&&s.response&&(s.response.data=Dr.call(t,t.transformResponse,s.response),s.response.headers=Re.from(s.response.headers))),Promise.reject(s)})}var bn={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{bn[t]=function(s){return typeof s===t||"a"+(e<1?"n ":" ")+t}});var Nf={};bn.transitional=function(e,a,s){function r(n,i){return"[Axios v"+Oa+"] Transitional option '"+n+"'"+i+(s?". "+s:"")}return(n,i,o)=>{if(e===!1)throw new q(r(i," has been removed"+(a?" in "+a:"")),q.ERR_DEPRECATED);return a&&!Nf[i]&&(Nf[i]=!0,console.warn(r(i," has been deprecated since v"+a+" and will be removed in the near future"))),e?e(n,i,o):!0}};bn.spelling=function(e){return(a,s)=>(console.warn(`${s} is likely a misspelling of ${e}`),!0)};function tw(t,e,a){if(typeof t!="object")throw new q("options must be an object",q.ERR_BAD_OPTION_VALUE);let s=Object.keys(t),r=s.length;for(;r-- >0;){let n=s[r],i=e[n];if(i){let o=t[n],p=o===void 0||i(o,n,t);if(p!==!0)throw new q("option "+n+" must be "+p,q.ERR_BAD_OPTION_VALUE);continue}if(a!==!0)throw new q("Unknown option "+n,q.ERR_BAD_OPTION)}}var Mr={assertOptions:tw,validators:bn};var Ct=Mr.validators,ur=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Vi,response:new Vi}}async request(e,a){try{return await this._request(e,a)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;let n=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?n&&!String(s.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+n):s.stack=n}catch{}}throw s}}_request(e,a){typeof e=="string"?(a=a||{},a.url=e):a=e||{},a=_t(this.defaults,a);let{transitional:s,paramsSerializer:r,headers:n}=a;s!==void 0&&Mr.assertOptions(s,{silentJSONParsing:Ct.transitional(Ct.boolean),forcedJSONParsing:Ct.transitional(Ct.boolean),clarifyTimeoutError:Ct.transitional(Ct.boolean)},!1),r!=null&&(b.isFunction(r)?a.paramsSerializer={serialize:r}:Mr.assertOptions(r,{encode:Ct.function,serialize:Ct.function},!0)),a.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),Mr.assertOptions(a,{baseUrl:Ct.spelling("baseURL"),withXsrfToken:Ct.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let i=n&&b.merge(n.common,n[a.method]);n&&b.forEach(["delete","get","head","post","put","patch","common"],f=>{delete n[f]}),a.headers=Re.concat(i,n);let o=[],p=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(a)===!1||(p=p&&g.synchronous,o.unshift(g.fulfilled,g.rejected))});let c=[];this.interceptors.response.forEach(function(g){c.push(g.fulfilled,g.rejected)});let l,m=0,x;if(!p){let f=[yn.bind(this),void 0];for(f.unshift.apply(f,o),f.push.apply(f,c),x=f.length,l=Promise.resolve(a);m<x;)l=l.then(f[m++],f[m++]);return l}x=o.length;let d=a;for(m=0;m<x;){let f=o[m++],g=o[m++];try{d=f(d)}catch(h){g.call(this,h);break}}try{l=yn.call(this,d)}catch(f){return Promise.reject(f)}for(m=0,x=c.length;m<x;)l=l.then(c[m++],c[m++]);return l}getUri(e){e=_t(this.defaults,e);let a=ka(e.baseURL,e.url,e.allowAbsoluteUrls);return Ra(a,e.params,e.paramsSerializer)}};b.forEach(["delete","get","head","options"],function(e){ur.prototype[e]=function(a,s){return this.request(_t(s||{},{method:e,url:a,data:(s||{}).data}))}});b.forEach(["post","put","patch"],function(e){function a(s){return function(n,i,o){return this.request(_t(o||{},{method:e,headers:s?{"Content-Type":"multipart/form-data"}:{},url:n,data:i}))}}ur.prototype[e]=a(),ur.prototype[e+"Form"]=a(!0)});var Br=ur;var To=class t{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let a;this.promise=new Promise(function(n){a=n});let s=this;this.promise.then(r=>{if(!s._listeners)return;let n=s._listeners.length;for(;n-- >0;)s._listeners[n](r);s._listeners=null}),this.promise.then=r=>{let n,i=new Promise(o=>{s.subscribe(o),n=o}).then(r);return i.cancel=function(){s.unsubscribe(n)},i},e(function(n,i,o){s.reason||(s.reason=new st(n,i,o),a(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);a!==-1&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=s=>{e.abort(s)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new t(function(r){e=r}),cancel:e}}},Ff=To;function Ao(t){return function(a){return t.apply(null,a)}}function Co(t){return b.isObject(t)&&t.isAxiosError===!0}var Oo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Oo).forEach(([t,e])=>{Oo[e]=t});var Lf=Oo;function $f(t){let e=new Br(t),a=Rr(Br.prototype.request,e);return b.extend(a,Br.prototype,e,{allOwnKeys:!0}),b.extend(a,e,null,{allOwnKeys:!0}),a.create=function(r){return $f(_t(t,r))},a}var De=$f(rr);De.Axios=Br;De.CanceledError=st;De.CancelToken=Ff;De.isCancel=Nr;De.VERSION=Oa;De.toFormData=Xt;De.AxiosError=q;De.Cancel=De.CanceledError;De.all=function(e){return Promise.all(e)};De.spread=Ao;De.isAxiosError=Co;De.mergeConfig=_t;De.AxiosHeaders=Re;De.formToJSON=t=>cn(b.isHTMLForm(t)?new FormData(t):t);De.getAdapter=gn.getAdapter;De.HttpStatusCode=Lf;De.default=De;var Hr=De;var{Axios:HC,AxiosError:VC,CanceledError:ZC,isCancel:GC,CancelToken:JC,VERSION:KC,all:QC,Cancel:WC,isAxiosError:XC,spread:YC,toFormData:eO,AxiosHeaders:tO,HttpStatusCode:aO,formToJSON:rO,getAdapter:sO,mergeConfig:nO}=Hr;var _n=class{constructor(e,a){this.httpClient=Hr.create({baseURL:e,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"}}),this.httpClient.interceptors.request.use(s=>(a&&(s.headers["X-MG-UserAccessToken"]=`${a}`),s),s=>Promise.reject(s)),this.httpClient.interceptors.response.use(s=>s,s=>Promise.reject(s))}handleDslComponentDocumentLinks(e){let a=new Set,s=r=>{var n,i;if((i=(n=r==null?void 0:r.componentInfo)==null?void 0:n.componentSetDocumentLink)!=null&&i[0]&&a.add(r.componentInfo.componentSetDocumentLink[0]),r.children&&Array.isArray(r.children))for(let o of r.children)s(o)};for(let r of e.nodes??[])s(r);return Array.from(a)}async getMeta(e,a){return(await this.httpClient.get("/mcp/meta",{params:{fileId:e,layerId:a}})).data}async getDsl(e,a){try{let s={fileId:e,layerId:a},r=await this.httpClient.get("/mcp/dsl",{params:s});return{dsl:r.data,componentDocumentLinks:this.handleDslComponentDocumentLinks(r.data),rules:["token filed must be generated as a variable (colors, shadows, fonts, etc.) and the token field must be displayed in the comment",`
            componentDocumentLinks is a list of frontend component documentation links used in the DSL layer, designed to help you understand how to use the components.
            When it exists and is not empty, you need to use mcp__getComponentLink in a for loop to get the URL content of all components in the list, understand how to use the components, and generate code using the components.
            For example: 
              \`\`\`js  
                const componentDocumentLinks = [
                  'https://example.com/ant/button.mdx',
                  'https://example.com/ant/button.mdx'
                ]
                for (const url of componentDocumentLinks) {
                  const componentLink = await mcp__getComponentLink(url);
                  console.log(componentLink);
                }
              \`\`\`
          `,...JSON.parse(process.env.RULES??"[]")]}}catch(s){throw s}}async getComponentStyleJson(e,a){return(await this.httpClient.get("/mcp/style",{params:{fileId:e,layerId:a}})).data}async request(e){try{return(await this.httpClient(e)).data}catch(a){throw a}}async extractIdsFromUrl(e){try{if(e.includes("/goto/")){let p=(await Hr.get(e,{maxRedirects:0,validateStatus:c=>c>=300&&c<400})).headers.location;if(!p)throw new Error("No redirect URL found for short link");e=p}let a=new URL(e),s=a.pathname.split("/"),r=new URLSearchParams(a.search),n=s.find(o=>/^\d+$/.test(o));if(!n)throw new Error("Could not extract fileId from URL");let i=r.get("layer_id");if(!i)throw new Error("Could not extract layerId from URL");return{fileId:n,layerId:i}}catch(a){throw new Error(`Failed to extract IDs from URL: ${a.message}`)}}};var ft=class{register(e){e.tool(this.name,this.description,this.schema.shape,this.execute.bind(this))}};var aw="mcp__getDsl",rw=`
"Use this tool to retrieve the DSL (Domain Specific Language) data from MasterGo design files and the rules you must follow when generating code.
This tool is useful when you need to analyze the structure of a design, understand component hierarchy, or extract design properties.
You can provide either:
1. fileId and layerId directly, or
2. a short link (like https://{domain}/goto/LhGgBAK)
This tool returns the raw DSL data in JSON format that you can then parse and analyze.
This tool also returns the rules you must follow when generating code.
The DSL data can also be used to transform and generate code for different frameworks."
`,wn=class extends ft{constructor(a){super();this.name=aw;this.description=rw;this.schema=u.object({fileId:u.string().optional().describe("MasterGo design file ID (format: file/<fileId> in MasterGo URL). Required if shortLink is not provided."),layerId:u.string().optional().describe("Layer ID of the specific component or element to retrieve (format: ?layer_id=<layerId> / file=<fileId> in MasterGo URL). Required if shortLink is not provided."),shortLink:u.string().optional().describe("Short link (like https://{domain}/goto/LhGgBAK).")});this.httpUtil=a}async execute({fileId:a,layerId:s,shortLink:r}){var n;try{if(!r&&(!a||!s))throw new Error("Either provide both fileId and layerId, or provide a MasterGo URL");let i=a,o=s;if(r){let c=await this.httpUtil.extractIdsFromUrl(r);i=c.fileId,o=c.layerId}if(!i||!o)throw new Error("Could not determine fileId or layerId");let p=await this.httpUtil.getDsl(i,o);return{content:[{type:"text",text:JSON.stringify(p)}]}}catch(i){let o=((n=i.response)==null?void 0:n.data)??(i==null?void 0:i.message);return{isError:!0,content:[{type:"text",text:JSON.stringify(o)}]}}}};var sw="mcp__getComponentLink",nw="When the data returned by mcp__getDsl contains a non-empty componentDocumentLinks array, this tool is used to sequentially retrieve URLs from the componentDocumentLinks array and then obtain component documentation data. The returned document data is used for you to generate frontend code based on components.",En=class extends ft{constructor(a){super();this.name=sw;this.description=nw;this.schema=u.object({url:u.string().describe("Component documentation link URL, from the componentDocumentLinks property, please ensure the URL is valid")});this.httpUtil=a}async execute({url:a}){try{return{content:[{type:"text",text:`${await this.httpUtil.request({method:"GET",url:a})}`}]}}catch(s){return{content:[{type:"text",text:JSON.stringify({error:"Failed to get component documentation",message:s instanceof Error?s.message:String(s)})}]}}}};var qf=`The \`results\` returns an XML file, which contains two types of information:

- **meta**: Describes the site, environment, requirements, and other context.
- **action**: Corresponds to the entry page name and \`targetLayerId\`, which is the ID of the target page.

I need you to write a task.md file in the current directory according to the following steps, and strictly follow these steps to generate the final project code.

## Steps
1. Obtain the \`results\` information, extract the \`meta\` and \`action\` data, and create a new \`task.md\` file.
2. Analyze the \`meta\` field, summarize the requirement description, and write it into task.md.
3. Analyze the \`action\` field, and write the page information into task.md.
4. Use the \`targetLayerId\` from the \`action\` field to call the \`mcp__getDsl\` method to get data.
5. Analyze the page data to check if there is an \`interactive\` field. If it exists, this field contains information about the current node's navigation to another page. You must continue to call the \`mcp__getDsl\` method according to the \`interactive\` field.
6. Repeat step 5 until all page data has been parsed and written into task.md.
7. According to the content in task.md, sequentially parse the pages listed in task.md and generate code. Complete the project construction.

## Example
**Note**: Be sure to follow the order described in the example. Ensure that all page information is obtained!


uppose the obtained results are:
\`\`\`xml
<info>
  <meta title="Name" content="Food Delivery APP" />
  <meta title="Description" content="This is a food delivery app where users can log in, order food, and manage delivery orders, address information, etc." />
  <meta title="Requirements" content="Implement using React, bind to Ant Design component library" />
  <action title="Login Page" layerId="0:1" />
  <action title="Food Delivery Page" layerId="0:2" />
</info>
\`\`\`
Write the following into task.md:




\`\`\`markdown
Requirement Description: This is a food delivery app, which includes login, food ordering, and address management features. It should be built using React and use Ant Design as the component library.

## Page List:
Login Page (layerId: 0:1)
Food Delivery Page (layerId: 0:2)

## Navigation Information


\`\`\`

Use \`mcp__getDsl\` to parse the 0:1 page and analyze the data.

The data might be:
\`\`\`json
{
    nodes: [{
        id: "0:1",
        // ...others
        children: [{
            id: "1:12",
            interactive: [
                type: "navigation",
                targetLayerId: "0:3"
            ]
        }]
    }]
}
\`\`\`

if you find that the node data of page 0:1 contains the \`interactive\` field with id 0:3, write the 0:3 page and add the navigation information:
\`\`\`markdown

## Page List:
Login Page (layerId: 0:1)
Food Delivery Page (layerId: 0:2)
Login Page Navigation Page (layerId: 0:3)

## Navigation Information
0:1 => 0:3

\`\`\`
Continue to parse 0:3. If the data contains the \`interactive\` field, continue writing.
Repeat this process until the data parsed by \`mcp__getDsl\` no longer contains the \`interactive\` field.
Then, use \`mcp__getDsl\` to parse the 0:2 page and repeat the steps for the 0:1 page.

After completion, generate the project code sequentially according to the page list in task.md.
`;var ow="mcp__getMeta",cw=`
Use this tool when the user intends to build a complete website or needs to obtain high-level site
configuration information. You must provide a fileld and layerld to identify the specific design element.
This tool returns the rules and results of the site and page. The rules is a markdown file, you must
follow the rules and use the results to analyze the site and page.
`,Sn=class extends ft{constructor(a){super();this.name=ow;this.description=cw;this.schema=u.object({fileId:u.string().describe("MasterGo design file ID (format: file/<fileId> in MasterGo URL)"),layerId:u.string().describe("Layer ID of the specific component or element to retrieve (format: ?layer_id=<layerId> / file=<fileId> in MasterGo URL)")});this.httpUtil=a}async execute({fileId:a,layerId:s}){var r;try{let n=await this.httpUtil.getMeta(a,s);return{content:[{type:"text",text:JSON.stringify({result:n,rules:qf})}]}}catch(n){let i=((r=n.response)==null?void 0:r.data)??(n==null?void 0:n.message);return{isError:!0,content:[{type:"text",text:JSON.stringify(i)}]}}}};var Ot=Be(require("fs"));var Uf=`---
description:
globs:
alwaysApply: true
---

# MasterGo Component System Specification v1.0

## Core Contents

- Project Environment Setup
- Component Interaction Design
- Component Development Workflow

---

## Project Environment Setup

### Environment Check

Check if project is initialized:

- \`package.json\`
- TypeScript configuration
- Vite configuration
- VitePress configuration (\`docs/.vitepress/\`)
- Vue and testing dependencies

### Environment Initialization

Required steps:

\`\`\`bash
npm init -y
npm install vue@latest typescript vite@latest vitepress@latest vitest@latest @vitejs/plugin-vue@latest
npm install -D @vue/test-utils jsdom @types/node
\`\`\`

Required configuration files:

- \`tsconfig.json\`
- \`vite.config.ts\`
- \`docs/.vitepress/config.ts\`
- \`vitest.config.ts\`

### Project Structure

\`\`\`
project-root/
\u251C\u2500\u2500 docs/                # Component documentation
\u2502   \u251C\u2500\u2500 .vitepress/      # VitePress configuration
\u2502   \u251C\u2500\u2500 components/      # Component docs and demos
\u251C\u2500\u2500 src/
\u2502   \u251C\u2500\u2500 components/      # Component source code
\u2502   \u251C\u2500\u2500 styles/          # Style files
\u251C\u2500\u2500 __tests__/           # Component tests
\`\`\`

### Required Scripts

\`\`\`json
{
  "scripts": {
    "dev": "vitepress dev docs",
    "build": "vitepress build docs",
    "test": "vitest run",
    "test:ui": "vitest --ui"
  }
}
\`\`\`

### Project Verification

**CRITICAL STEP**: After project initialization, scripts must be run to verify configuration:

1. Run the development server:

   \`\`\`bash
   npm run dev
   \`\`\`

2. Verify the test environment:

   \`\`\`bash
   npm run test
   \`\`\`

3. Ensure no errors appear in the console for each script
4. Resolve any errors before proceeding to component development
5. Project is considered properly initialized only when all scripts run without errors

---

## Component Interaction Design Specification

### Core Principles

- **CSS Priority**: Use CSS pseudo-classes for basic states
- **State Extension**: Allow overriding default states via props
- **Consistency**: Maintain consistent state management patterns
- **Performance Priority**: Minimize JavaScript state management

### State Priority

CSS Pseudo-classes > Props-specified States > JavaScript State Management

### Component Reuse Principles

Reuse decision priority:

1. Direct Use (when functionality completely matches)
2. Component Composition (implement by combining existing components)
3. Component Extension (add new functionality based on existing components)
4. Redevelopment (only when above methods are not feasible)

---

## Component Development Workflow

### Complete Process

\`\`\`
[Environment Check] \u2192 [Project Verification] \u2192 [Component Analysis] \u2192 [User Confirmation] \u2192 [Test Generation] \u2192 [Component Development] \u2192 [Validation] \u2192 [Documentation & Preview]
\`\`\`

### 1. Component Analysis

**Input**: Component JSON specification  
**Output**: Architecture document (\`.mastergo/\${componentName}-arch.md\`)

#### Slot Analysis

AI must analyze component design and infer:

- Slots that may be needed
- Purpose of each slot
- Default content suggestions
- Optional/required status

#### Checklist

- [ ] Property analysis
- [ ] States and variants identification
- [ ] Common styles extraction
- [ ] Interface definition
- [ ] Slot definition

#### Architecture Document Verification

**CRITICAL BREAK POINT**: After generating the architecture document, execution must pause.

1. Present the architecture document to the user for review
2. Ask user to verify all aspects of the document:
   - Component properties and types
   - State definitions
   - Slot specifications
   - Component structure
   - Image assets and their paths
3. If user identifies issues:
   - Collect all feedback
   - Make required modifications to the architecture document
   - Present updated document for review
4. Repeat review cycle until user explicitly approves the document
5. Only proceed to Test Generation phase after user confirmation

#### Image Resource Handling

**CRITICAL STEP**: After user confirmation of the architecture document, and before proceeding to Test Generation:

1.  **Resource Inventory and Path Documentation**:

    - The architecture document must include an "Image Resources" section that clearly lists all necessary resources in a table format:

    \`\`\`markdown
    ## Image Resources

    ### Resource List and Paths

    | Icon Description | Original Path             | Target Path                                             | Icon Color Control                                   |
    | ---------------- | ------------------------- | ------------------------------------------------------- | ---------------------------------------------------- |
    | Close Icon       | \`/original/path/icon.svg\` | \`src/components/\${componentName}/images/icon-close.svg\` | Dynamically controlled, defaults to match text color |
    | Other Icon       | ...                       | ...                                                     | ...                                                  |
    \`\`\`

2.  **Copy Images**:

    - Copy all necessary image resources listed in the architecture document to the component-specific directory.
    - Use semantic filenames such as \`icon-close.svg\`, \`icon-success.svg\`, \`bg-header.png\`, etc., ensuring the names clearly indicate the purpose of each image.
    - The target path must be \`src/components/\${componentName}/images/\`. Create this directory if it doesn't exist.
    - Example:
      \`\`\`bash
      mkdir -p src/components/\${componentName}/images
      cp /original/path/close-icon.svg src/components/\${componentName}/images/icon-close.svg
      \`\`\`

3.  **SVG Image Import and Color Specification**:

    - The architecture document must clearly specify the import method and color control approach for SVG icons.
    - SVGs must be imported using the following method to ensure dynamic color control:

      \`\`\`typescript
      import CloseIcon from "./images/icon-close.svg?raw"; // ?raw ensures it's imported as a string
      \`\`\`

    - The architecture document must include code examples for SVG usage and color control:

      \`\`\`\`markdown
      ### Icon Import and Usage Method

      \`\`\`typescript
      // In \${componentName}.vue, import icons
      import CloseIcon from "./images/icon-close.svg?raw";
      import SuccessIcon from "./images/icon-success.svg?raw";
      \`\`\`
      \`\`\`\`

      Using SVGs in templates and controlling their color:

      \`\`\`html
      <template>
        <div class="icon-container" v-html="CloseIcon"></div>
      </template>

      <style scoped>
        .icon-container svg {
          fill: v-bind("dynamicColorVariable"); /* Dynamically bind color */
        }
        /* Or use CSS variables to control color */
        .icon-container svg {
          fill: var(--icon-color, currentColor);
        }
      </style>
      \`\`\`

    - For each SVG icon, the architecture document must clearly specify:
      1. Default color
      2. Whether the color is fixed or needs to be dynamically controlled
      3. Color variations in different states

### 2. Test Generation

**Input**: Approved architecture document  
**Output**: Component unit tests

#### Test Coverage

- All component properties
- All component states and behaviors
- Edge cases
- All inferred slots
- State management (hover, focus, active, disabled, etc.)

### 3. Component Development

**Input**: Architecture document and test cases  
**Output**: Functional component

#### Required Files

- \`src/components/\${componentName}/index.ts\`
- \`src/components/\${componentName}/types.ts\`
- \`src/components/\${componentName}/\${componentName}.vue\`

#### Development Method

- Test-driven development
- Must follow UI interaction design specifications
- Iterative implementation: Minimal code \u2192 Run tests \u2192 Refactor \u2192 Next test

### 4. Validation

- All tests pass
- Component visually matches design
- Component is accessible
- Responsive behavior is correct

### 5. Documentation & Preview

**Output**: VitePress documentation and interactive previews

#### Documentation Content

- Component overview
- API reference
- Interactive examples
- Complete slot documentation
- Various states and use cases demonstrations

#### Interactive Preview

\`\`\`\`md
## Basic Usage

:::demo

\`\`\`vue
<template>
  <ComponentName prop="value" />
</template>
\`\`\`

:::
\`\`\`\`

### Checkpoints

- **Environment**: Correct configuration, dependencies installed, documentation preview system working
- **Structure**: Files created, exports working, interfaces defined, slot definitions
- **Tests**: Coverage for all features, edge cases, slots and states
- **Implementation**: Renders correctly, properties work, state management complies with specifications, styles applied correctly, slot functionality works
- **Documentation**: Feature documentation complete, examples available, API reference complete, slot usage documentation complete
`;var pw="mcp__getComponentGenerator",uw=`
Users need to actively call this tool to get the component development workflow. When Generator is mentioned, please actively call this tool.
This tool provides a structured workflow for component development following best practices.
You must provide an absolute rootPath of workspace to save workflow files.
`,Pn=class extends ft{constructor(a){super();this.name=pw;this.description=uw;this.schema=u.object({rootPath:u.string().describe("The root path of the project, if the user does not provide, you can use the current directory as the root path"),fileId:u.string().describe("MasterGo design file ID (format: file/<fileId> in MasterGo URL)"),layerId:u.string().describe("Layer ID of the specific component or element to retrieve (format: ?layer_id=<layerId> / file=<fileId> in MasterGo URL)")});this.httpUtil=a}async execute({rootPath:a,fileId:s,layerId:r}){var l;let n=`${a}/.mastergo/`;Ot.default.existsSync(n)||Ot.default.mkdirSync(n,{recursive:!0});let i=`${n}/component-workflow.md`,o=await this.httpUtil.getComponentStyleJson(s,r),p=`${n}/${o[0].name}.json`,c=m=>{if(m.path&&m.path.length>0){m.imageUrls=[];let x=m.id.replaceAll("/","&"),d=`${n}/images`;Ot.default.existsSync(d)||Ot.default.mkdirSync(d,{recursive:!0}),(m.path??[]).forEach((f,g)=>{let h=`${d}/${x}-${g}.svg`;Ot.default.existsSync(h)||Ot.default.writeFileSync(h,`<svg width="100%" height="100%" viewBox="0 0 16 16"xmlns="http://www.w3.org/2000/svg">
  <path d="${f}" fill="currentColor"/>
</svg>`),m.imageUrls.push(h)}),delete m.path}m.children&&m.children.forEach(x=>{c(x)})};c(o[0]),Ot.default.existsSync(i)||Ot.default.writeFileSync(i,Uf),Ot.default.writeFileSync(p,JSON.stringify(o[0]));try{return{content:[{type:"text",text:JSON.stringify({files:{workflow:i,componentSpec:p},message:"Component development files successfully created",rules:[`Follow the component workflow process defined in file://${i} for structured development. This workflow contains a lot of content, you'll need to read it in multiple sessions.`,`Implement the component according to the specifications in file://${p}, ensuring all properties and states are properly handled.`]})}]}}catch(m){let x=((l=m.response)==null?void 0:l.data)??(m==null?void 0:m.message);return{isError:!0,content:[{type:"text",text:JSON.stringify(x)}]}}}};var Rn={name:"@mastergo/magic-mcp",version:"0.0.6",description:"MasterGo MCP standalone service",main:"dist/index.js",bin:{"mastergo-magic-mcp":"bin/cli.js"},files:["dist/**/*","bin/**/*","!**/*.map","!**/.DS_Store","!**/.idea","!**/.vscode"],engines:{node:">=18"},scripts:{build:"node build.js",start:"node bin/cli.js --token=test --url=http://localhost:3000 --debug",prepublishOnly:"npm run build"},keywords:["mastergo","mcp","ai"],author:"",license:"ISC",private:!1,dependencies:{"@modelcontextprotocol/sdk":"^1.6.1",axios:"^1.6.0",zod:"^3.22.4"},devDependencies:{"@types/node":"^20.8.10","@typescript-eslint/eslint-plugin":"^6.9.1","@typescript-eslint/parser":"^6.9.1",esbuild:"^0.25.1","esbuild-plugin-tsc":"^0.5.0",eslint:"^8.52.0",pkg:"^5.8.1",prettier:"^3.0.3","ts-node":"^10.9.1",typescript:"^5.2.2"}};var mw=`version_${Rn.version.replace(/\./g,"_")}`,fw=`the current version is ${Rn.version}`,kn=class extends ft{constructor(){super();this.name=mw;this.description=fw;this.schema=u.object({})}async execute({}){return{content:[{type:"text",text:JSON.stringify(Rn.version)}]}}};var zf=t=>{process.env.DEBUG==="true"&&console.log(t)};function hw(){let t=process.env.MASTERGO_API_TOKEN,e=process.env.API_BASE_URL||"http://localhost:3000",a=process.env.DEBUG==="true";t||(console.error("Error: MASTERGO_API_TOKEN environment variable not set"),process.exit(1)),zf("Starting MasterGo MCP server..."),zf(`API base URL: ${e}`);let s=new Ms({name:"MasterGoMcpServer",version:"0.0.1"}),r=new _n(e,t);new kn().register(s),new wn(r).register(s),new En(r).register(s),new Sn(r).register(s),new Pn(r).register(s),s.connect(new Hs),a&&console.log("MasterGo MCP server started and waiting for connection...")}hw();
/*! Bundled license information:

uri-js/dist/es5/uri.all.js:
  (** @license URI.js v4.4.1 (c) 2011 Gary Court. License: http://github.com/garycourt/uri-js *)

mime-db/index.js:
  (*!
   * mime-db
   * Copyright(c) 2014 Jonathan Ong
   * Copyright(c) 2015-2022 Douglas Christopher Wilson
   * MIT Licensed
   *)

mime-types/index.js:
  (*!
   * mime-types
   * Copyright(c) 2014 Jonathan Ong
   * Copyright(c) 2015 Douglas Christopher Wilson
   * MIT Licensed
   *)
*/
