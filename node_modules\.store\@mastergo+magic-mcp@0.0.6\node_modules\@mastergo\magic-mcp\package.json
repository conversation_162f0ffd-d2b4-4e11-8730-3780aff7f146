{"name": "@mastergo/magic-mcp", "version": "0.0.6", "description": "MasterGo MCP standalone service", "main": "dist/index.js", "bin": {"mastergo-magic-mcp": "bin/cli.js"}, "files": ["dist/**/*", "bin/**/*", "!**/*.map", "!**/.DS_Store", "!**/.idea", "!**/.vscode"], "engines": {"node": ">=18"}, "scripts": {"build": "node build.js", "start": "node bin/cli.js --token=test --url=http://localhost:3000 --debug", "prepublishOnly": "npm run build"}, "keywords": ["mastergo", "mcp", "ai"], "author": "", "license": "ISC", "private": false, "dependencies": {"@modelcontextprotocol/sdk": "^1.6.1", "axios": "^1.6.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.8.10", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "esbuild": "^0.25.1", "esbuild-plugin-tsc": "^0.5.0", "eslint": "^8.52.0", "pkg": "^5.8.1", "prettier": "^3.0.3", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "__npminstall_done": true, "_from": "@mastergo/magic-mcp@0.0.6", "_resolved": "https://registry.npmmirror.com/@mastergo/magic-mcp/-/magic-mcp-0.0.6.tgz"}