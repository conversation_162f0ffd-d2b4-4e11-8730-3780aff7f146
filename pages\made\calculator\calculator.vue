<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList" bgColor="#f7f7f7"
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="房贷计算器" mpWeiXinShow :autoBack="true" :fixed="false" class="custom_navbar">
				</cl-navbar>
				<!-- 贷款类型选择 -->
				<view class="loan_type_section">
					<u-tabs :list="tabList" :scrollable="false" :inactiveStyle="inactiveStyle"
						:activeStyle="activeStyle" lineWidth="50rpx" :lineColor="baseColor" lineHeight="6rpx"
						@click="tabClick"></u-tabs>
				</view>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="calculator_container" v-if="loanType === '商业贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod('total')">
									<view class="radio_button" :class="{ active: form1.type === 'total' }">
										<view class="radio_inner" v-if="form1.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod('price')">
									<view class="radio_button" :class="{ active: form1.type === 'price' }">
										<view class="radio_inner" v-if="form1.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>
						<view class="form_row" v-if="form1.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.sumprice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form1.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area" @click="openPicker('loanRatio', 'form1', 'bl')">
								<view class="input_value">{{ form1.bl }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form1.loanAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
					</view>

					<view class="form_section">

						<view class="form_row">
							<view class="label_text">商贷年限</view>
							<view class="input_area" @click="openPicker('loanYears', 'form1', 'loanYears')">
								<view class="input_value">{{ form1.loanYears }}</view>

								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">利率方式</view>
							<view class="input_area" @click="openPicker('rateType', 'form1', 'rateType')">
								<view class="input_value">{{ form1.rateType }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷利率</view>
							<view class="input_area" @click="openPicker('businessRate', 'form1', 'interestRate')">
								<view class="input_value">{{ form1.interestRate }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="calculator_container" v-if="loanType === '公积金贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod2('total')">
									<view class="radio_button" :class="{ active: form2.type === 'total' }">
										<view class="radio_inner" v-if="form2.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod2('price')">
									<view class="radio_button" :class="{ active: form2.type === 'price' }">
										<view class="radio_inner" v-if="form2.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>

						<!-- 按房屋总价时显示的字段 -->
						<view class="form_row" v-if="form2.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.housePrice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form2.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area" @click="openPicker('loanRatio', 'form2', 'loanRatio')">
								<view class="input_value">{{ form2.loanRatio }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>

						<!-- 按贷款总额时显示的字段 -->
						<view class="form_row" v-if="form2.type === 'total'">
							<view class="label_text">贷款总额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.loanAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>

						<!-- 公积金金额 -->
						<view class="form_row">
							<view class="label_text">公积金金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.fundAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
					</view>

					<!-- 公积金相关字段 -->
					<view class="form_section">
						<view class="form_row">
							<view class="label_text">公积金年限</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入年限" v-model="form2.fundYears" />
								<view class="unit_text">年</view>

							</view>
						</view>
						<view class="form_row">
							<view class="label_text">公积金利率</view>
							<view class="input_area" @click="openPicker('fundRate', 'form2', 'fundRate')">
								<view class="input_value">{{ form2.fundRate }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
					</view>

					<!-- 商贷相关字段 -->
					<view class="form_section">
						<view class="form_row">
							<view class="label_text">商贷金额</view>
							<view class="input_area" @click="selectBusinessAmount">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form2.businessAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷年限</view>
							<view class="input_area" @click="openPicker('loanYears', 'form2', 'businessYears')">
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">利率方式</view>
							<view class="input_area" @click="openPicker('rateType', 'form2', 'rateType')">
								<view class="input_value">{{ form2.rateType }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">商贷利率</view>
							<view class="input_area" @click="openPicker('businessRate', 'form2', 'businessRate')">
								<view class="input_value">{{ form2.businessRate }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="calculator_container" v-if="loanType === '组合贷款'">
					<!-- 计算方式选择 -->
					<view class="calculation_method_section form_section">
						<view class="form_row">
							<view class="label_text">计算方式</view>
							<view class="method_options">
								<view class="method_option" @click="selectMethod3('total')">
									<view class="radio_button" :class="{ active: form3.type === 'total' }">
										<view class="radio_inner" v-if="form3.type === 'total'"></view>
									</view>
									<view class="method_text">按贷款总额</view>
								</view>
								<view class="method_option" @click="selectMethod3('price')">
									<view class="radio_button" :class="{ active: form3.type === 'price' }">
										<view class="radio_inner" v-if="form3.type === 'price'"></view>
									</view>
									<view class="method_text">按房屋总价</view>
								</view>
							</view>
						</view>

						<!-- 按房屋总价时显示的字段 -->
						<view class="form_row" v-if="form3.type === 'price'">
							<view class="label_text">房屋总价</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form3.housePrice" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row" v-if="form3.type === 'price'">
							<view class="label_text">贷款比例</view>
							<view class="input_area" @click="openPicker('loanRatio', 'form3', 'loanRatio')">
								<view class="input_value">{{ form3.loanRatio }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>


					</view>

					<!-- 公积金相关字段 -->
					<view class="form_section">
						<!-- 公积金金额 -->
						<view class="form_row">
							<view class="label_text">公积金金额</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入金额" v-model="form3.fundAmount" />
								<view class="unit_text">万</view>
							</view>
						</view>
						<view class="form_row">
							<view class="label_text">公积金年限</view>
							<view class="input_area">
								<input class="input_field" placeholderStyle="color: #BFBFBF;" fontSize="28rpx"
									color="#333" placeholder="请输入年限" v-model="form3.fundYears" />
								<view class="unit_text">年</view>

							</view>
						</view>
						<view class="form_row">
							<view class="label_text">公积金利率</view>
							<view class="input_area" @click="openPicker('fundRate', 'form3', 'fundRate')">
								<view class="input_value">{{ form3.fundRate }}</view>
								<u-icon name="arrow-right" color="#333333" size="28rpx"></u-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="bottom" class="bottom">
				<view class="calculate_button" @click="calculate">
					开始计算
				</view>
			</view>
		</z-paging>

		<!-- 通用选择器 -->
		<u-picker :show="pickerShow" :columns="getPickerColumns()" :defaultIndex="getPickerIndex()"
			@confirm="confirmPicker" @cancel="pickerShow = false" :title="getPickerTitle()">
		</u-picker>
	</view>
</template>

<script>
export default {
	data() {
		return {
			refresherStatus: 0,
			dataList: [],
			// 表单数据
			loanType: '商业贷款', // 贷款类型：商业贷款, 公积金贷款, 组合贷款
			form1: {
				sumn_price: "",
				bl: '6.5成',
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				loanAmount: '', // 贷款金额
				loanYears: "30年", // 贷款年限
				rateType: '按LPR浮动', // 利率方式
				interestRate: '4.2%', // 利率
			},
			form2: {
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				loanAmount: '', // 贷款总额
				fundAmount: '', // 公积金金额
				fundYears: '', // 公积金年限
				fundRate: '3.25%', // 公积金利率
				businessAmount: '', // 商贷金额
				businessYears: '30', // 商贷年限
				rateType: '按LPR', // 利率方式
				businessRate: '4.2%', // 商贷利率
				housePrice: '', // 房屋总价
				loanRatio: '6.5成', // 贷款比例
			},
			form3: {
				type: 'total', // 计算方式：total-按贷款总额, price-按房屋总价
				fundAmount: '', // 公积金金额
				fundYears: '30', // 公积金年限
				fundRate: '3.1%', // 公积金利率
				housePrice: '', // 房屋总价
				loanRatio: '6.5成', // 贷款比例
			},
			// 通用选择器数据
			pickerShow: false,
			pickerType: '', // 当前选择器类型
			currentForm: '', // 当前操作的表单 form1/form2/form3
			currentField: '', // 当前操作的字段
			// 贷款年限选择器 (2025年标准)
			loanYearsList: ['1年', '2年', '3年', '5年', '8年', '10年', '15年', '20年', '25年', '30年'],
			loanYearsIndex: 9, // 默认30年
			// 利率方式选择器 (2025年标准)
			rateTypeList: ['按LPR浮动', '固定利率', '按基准利率浮动'],
			rateTypeIndex: 0, // 默认按LPR浮动
			// 公积金利率选择器 (2025年最新利率)
			fundRateList: ['3.1%', '3.25%', '3.35%', '3.5%'],
			fundRateIndex: 0, // 默认3.1%
			// 商贷利率选择器 (2025年标准利率)
			businessRateList: ['3.5%', '3.8%', '4.0%', '4.2%', '4.5%', '4.8%', '5.0%'],
			businessRateIndex: 3, // 默认4.2%
			// 贷款比例选择器 (2025年标准)
			loanRatioList: ['2成', '3成', '4成', '5成', '6成', '6.5成', '7成', '8成'],
			loanRatioIndex: 5, // 默认6.5成,
			tabList: [
				{ name: '商业贷款' },
				{ name: '公积金贷款' },
				{ name: '组合贷款' }
			],
			inactiveStyle: {
				fontSize: '30rpx',
				fontWeight: 'bold',
				color: '#333333',
			},
			activeStyle: {
				color: '#006AFC',
				fontSize: '30rpx',
				fontWeight: 'bold',
			},
		};
	},
	computed: {},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};

			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false"
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });
		},
		// 切换标签页
		tabClick(e) {
			this.loanType = e.name;
		},
		// 选择计算方式
		selectMethod(method) {
			this.form1.type = method;
		},
		// 选择公积金贷款计算方式
		selectMethod2(method) {
			this.form2.type = method;
		},
		// 选择组合贷款计算方式
		selectMethod3(method) {
			this.form3.type = method;
		},
		// 通用选择器方法
		openPicker(type, form, field) {
			this.pickerType = type;
			this.currentForm = form;
			this.currentField = field;
			this.pickerShow = true;
		},
		// 获取选择器列数据
		getPickerColumns() {
			switch (this.pickerType) {
				case 'loanYears':
					return [this.loanYearsList];
				case 'rateType':
					return [this.rateTypeList];
				case 'fundRate':
					return [this.fundRateList];
				case 'businessRate':
					return [this.businessRateList];
				case 'loanRatio':
					return [this.loanRatioList];
				default:
					return [[]];
			}
		},
		// 获取选择器默认索引
		getPickerIndex() {
			switch (this.pickerType) {
				case 'loanYears':
					return [this.loanYearsIndex];
				case 'rateType':
					return [this.rateTypeIndex];
				case 'fundRate':
					return [this.fundRateIndex];
				case 'businessRate':
					return [this.businessRateIndex];
				case 'loanRatio':
					return [this.loanRatioIndex];
				default:
					return [0];
			}
		},
		// 获取选择器标题
		getPickerTitle() {
			switch (this.pickerType) {
				case 'loanYears':
					return '选择贷款年限';
				case 'rateType':
					return '选择利率方式';
				case 'fundRate':
					return '选择公积金利率';
				case 'businessRate':
					return '选择商贷利率';
				case 'loanRatio':
					return '选择贷款比例';
				default:
					return '请选择';
			}
		},
		// 确认选择器选择
		confirmPicker(e) {
			const { indexs, value } = e;
			const selectedValue = value[0];

			// 更新对应表单的字段值
			this[this.currentForm][this.currentField] = selectedValue;

			// 更新对应的索引
			switch (this.pickerType) {
				case 'loanYears':
					this.loanYearsIndex = indexs[0];
					break;
				case 'rateType':
					this.rateTypeIndex = indexs[0];
					break;
				case 'fundRate':
					this.fundRateIndex = indexs[0];
					break;
				case 'businessRate':
					this.businessRateIndex = indexs[0];
					break;
				case 'loanRatio':
					this.loanRatioIndex = indexs[0];
					break;
			}

			this.pickerShow = false;
		},
		// 开始计算
		calculate() {
			let result = {};

			try {
				if (this.loanType === '商业贷款') {
					result = this.calculateBusiness();
				} else if (this.loanType === '公积金贷款') {
					result = this.calculateFund();
				} else if (this.loanType === '组合贷款') {
					result = this.calculateCombination();
				}

				if (result.error) {
					uni.showToast({
						title: result.error,
						icon: 'none'
					});
					return;
				}

				// 跳转到结果页面或显示结果
				this.showResult(result);

			} catch (error) {
				uni.showToast({
					title: '计算出错，请检查输入',
					icon: 'none'
				});
			}
		},

		// 商业贷款计算
		calculateBusiness() {
			const form = this.form1;
			let loanAmount = 0;

			// 根据计算方式确定贷款金额
			if (form.type === 'price') {
				// 按房屋总价计算
				if (!form.sumprice || !form.bl) {
					return { error: '请输入房屋总价和贷款比例' };
				}
				loanAmount = parseFloat(form.sumprice) * parseFloat(form.bl) / 10;
			} else {
				// 按贷款总额计算
				if (!form.loanAmount) {
					return { error: '请输入贷款金额' };
				}
				loanAmount = parseFloat(form.loanAmount);
			}

			if (!form.loanYears || !form.interestRate) {
				return { error: '请完善贷款信息' };
			}

			const years = parseInt(form.loanYears);
			const rate = parseFloat(form.interestRate) / 100;

			return this.calculateLoan(loanAmount, years, rate, '商业贷款');
		},

		// 公积金贷款计算
		calculateFund() {
			const form = this.form2;
			let fundAmount = 0;
			let businessAmount = 0;

			// 根据计算方式确定贷款金额
			if (form.type === 'price') {
				// 按房屋总价计算
				if (!form.housePrice || !form.loanRatio) {
					return { error: '请输入房屋总价和贷款比例' };
				}
				const totalLoan = parseFloat(form.housePrice) * parseFloat(form.loanRatio) / 10;
				fundAmount = parseFloat(form.fundAmount) || 0;
				businessAmount = totalLoan - fundAmount;
			} else {
				// 按贷款总额计算
				if (!form.loanAmount || !form.fundAmount) {
					return { error: '请输入贷款总额和公积金金额' };
				}
				const totalLoan = parseFloat(form.loanAmount);
				fundAmount = parseFloat(form.fundAmount);
				businessAmount = totalLoan - fundAmount;
			}

			if (fundAmount <= 0) {
				return { error: '公积金金额必须大于0' };
			}

			// 公积金部分计算
			const fundYears = parseInt(form.fundYears);
			const fundRate = this.parseRate(form.fundRate) / 100;
			const fundResult = this.calculateLoan(fundAmount, fundYears, fundRate, '公积金');

			// 商贷部分计算
			let businessResult = null;
			if (businessAmount > 0) {
				const businessYears = parseInt(form.businessYears);
				const businessRate = parseFloat(form.businessRate) / 100;
				businessResult = this.calculateLoan(businessAmount, businessYears, businessRate, '商业贷款');
			}

			return {
				type: '公积金贷款',
				fundResult,
				businessResult,
				totalLoan: fundAmount + businessAmount,
				totalMonthly: fundResult.monthlyPayment + (businessResult ? businessResult.monthlyPayment : 0),
				totalInterest: fundResult.totalInterest + (businessResult ? businessResult.totalInterest : 0),
				totalPayment: fundResult.totalPayment + (businessResult ? businessResult.totalPayment : 0)
			};
		},

		// 组合贷款计算
		calculateCombination() {
			const form = this.form3;
			let fundAmount = 0;

			// 根据计算方式确定公积金金额
			if (form.type === 'price') {
				// 按房屋总价计算
				if (!form.housePrice || !form.loanRatio || !form.fundAmount) {
					return { error: '请输入房屋总价、贷款比例和公积金金额' };
				}
				fundAmount = parseFloat(form.fundAmount);
			} else {
				// 按贷款总额计算
				if (!form.fundAmount) {
					return { error: '请输入公积金金额' };
				}
				fundAmount = parseFloat(form.fundAmount);
			}

			if (fundAmount <= 0) {
				return { error: '公积金金额必须大于0' };
			}

			// 公积金部分计算
			const fundYears = parseInt(form.fundYears);
			const fundRate = this.parseRate(form.fundRate) / 100;
			const fundResult = this.calculateLoan(fundAmount, fundYears, fundRate, '公积金');

			return {
				type: '组合贷款',
				fundResult,
				totalLoan: fundAmount,
				totalMonthly: fundResult.monthlyPayment,
				totalInterest: fundResult.totalInterest,
				totalPayment: fundResult.totalPayment
			};
		},

		// 核心贷款计算方法（等额本息）
		calculateLoan(principal, years, annualRate, type) {
			const months = years * 12;
			const monthlyRate = annualRate / 12;

			if (monthlyRate === 0) {
				// 无息贷款
				const monthlyPayment = principal / months;
				return {
					type,
					principal: principal,
					years: years,
					months: months,
					rate: annualRate,
					monthlyPayment: monthlyPayment,
					totalPayment: principal,
					totalInterest: 0
				};
			}

			// 等额本息计算公式
			const monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, months)) /
				(Math.pow(1 + monthlyRate, months) - 1);
			const totalPayment = monthlyPayment * months;
			const totalInterest = totalPayment - principal;

			return {
				type,
				principal: principal,
				years: years,
				months: months,
				rate: annualRate,
				monthlyPayment: monthlyPayment,
				totalPayment: totalPayment,
				totalInterest: totalInterest
			};
		},

		// 解析利率字符串
		parseRate(rateStr) {
			if (typeof rateStr === 'string' && rateStr.includes('%')) {
				return parseFloat(rateStr.replace('%', ''));
			}
			return parseFloat(rateStr) || 0;
		},

		// 显示计算结果
		showResult(result) {
			let message = '';

			if (result.type === '商业贷款') {
				message = `贷款金额：${this.$u.priceFormat(result.principal, 2)}万元\n` +
					`月供：${this.$u.priceFormat(result.monthlyPayment, 2)}元\n` +
					`总利息：${this.$u.priceFormat(result.totalInterest, 2)}万元\n` +
					`还款总额：${this.$u.priceFormat(result.totalPayment, 2)}万元`;
			} else if (result.type === '公积金贷款') {
				message = `总贷款：${this.$u.priceFormat(result.totalLoan, 2)}万元\n` +
					`月供合计：${this.$u.priceFormat(result.totalMonthly, 2)}元\n` +
					`总利息：${this.$u.priceFormat((result.totalInterest / 10000), 2)}万元\n` +
					`还款总额：${this.$u.priceFormat((result.totalPayment / 10000), 2)}万元`;
			} else if (result.type === '组合贷款') {
				message = `公积金贷款：${this.$u.priceFormat(result.fundResult.principal, 2)}万元\n` +
					`月供：${this.$u.priceFormat(result.fundResult.monthlyPayment, 2)}元\n` +
					`总利息：${this.$u.priceFormat((result.fundResult.totalInterest / 10000), 2)}万元`;
			}

			uni.showModal({
				title: '计算结果',
				content: message,
				showCancel: false,
				confirmText: '确定'
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.loan_type_section {
		background: #ffffff;
	}

	.loading_list {
		width: 100%;
		padding: 20rpx 0;

		.calculator_container {
			width: 100%;
			@include flex_center(column, null, null);
			gap: 20rpx;

			>view {
				width: 1005;
			}

			// 贷款类型选择


			// 计算方式选择区域
			.calculation_method_section {
				background: #ffffff;
				padding: 0 25rpx;

				&:last-child {
					.form_row {
						border: none
					}
				}

				.form_row {
					@include flex-center(row, space-between, center);
					padding: 30rpx 0;
					border-bottom: 1rpx solid #EEEEEE;

					&:last-child {

						border-bottom: 0;
					}

					.label_text {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						line-height: 28rpx;
					}

					.method_options {
						@include flex-center(row, flex-end, center);
						gap: 60rpx;

						.method_option {
							@include flex-center(row, center, center);
							gap: 16rpx;

							.radio_button {
								width: 32rpx;
								height: 32rpx;
								border-radius: 50%;
								background: #F5F5F5;
								@include flex-center(row, center, center);

								&.active {
									background: rgba(0, 106, 252, 0.18);
								}

								.radio_inner {
									width: 16rpx;
									height: 16rpx;
									border-radius: 50%;
									background: #006AFC;
								}
							}

							.method_text {
								font-size: 28rpx;
								color: #333333;
								line-height: 28rpx;
							}
						}
					}
				}


			}

			// 表单区域
			.form_section {
				background: #ffffff;
				padding: 0 25rpx;

				.form_row {
					@include flex-center(row, space-between, center);
					padding: 30rpx 0;
					border-bottom: 1rpx solid #EEEEEE;

					&:last-child {
						border-bottom: none;
					}

					.label_text {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						line-height: 28rpx;
					}

					.input_area {
						@include flex-center(row, flex-end, center);
						gap: 20rpx;
						flex: 1;

						.input_field {
							flex: 1;
							text-align: right;
							color: #333333;
							font-size: 28rpx;

							&::placeholder {
								color: #BFBFBF;
								font-size: 28rpx;
							}
						}

						.input_value {
							color: #333333;
							font-size: 28rpx;

							&.inactive {
								color: #BFBFBF;
							}
						}

						.unit_text {
							color: #333333;
							font-size: 28rpx;
							padding-bottom: 4rpx
						}
					}
				}


			}


		}
	}

	// 计算按钮
	.bottom {
		padding: 20rpx 25rpx;
		background-color: #ffffff;

		.calculate_button {
			width: 100%;
			height: 88rpx;
			background: #006AFC;
			border-radius: 8rpx;
			@include flex-center(row, center, center);
			color: #ffffff;
			font-size: 30rpx;
		}
	}

}
</style>